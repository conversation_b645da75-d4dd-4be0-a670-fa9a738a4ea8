package com.just.template.core;

import com.just.template.exception.TemplateException;

/**
 * 模板接口
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
public interface Template {
    
    /**
     * 生成模板内容
     * 
     * @param context 类型安全的模板上下文
     * @return 生成的内容
     * @throws TemplateException 模板生成异常
     */
    String generate(TypedTemplateContext context) throws TemplateException;
    
    /**
     * 获取模板名称
     * 
     * @return 模板名称
     */
    String getTemplateName();
    
    /**
     * 获取模板类型
     * 
     * @return 模板类型
     */
    TemplateType getTemplateType();
} 