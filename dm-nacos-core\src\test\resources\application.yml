spring:
  application:
    name: dm-nacos-core-test

# DM Nacos 核心配置
dm:
  nacos:
    # 环境配置
    environments:
      dev:
        name: dev
        description: 开发环境
        # end-point: mse.cn-shanghai.aliyuncs.com
        server-addr: mse-4d0af020-nacos-ans.mse.aliyuncs.com:8848
        instance-id: mse_regserverless_cn-3mp3ys14x01
        access-key: LTAI5tJ72ozLyf6eQnsupgV5
        secret-key: ******************************
        namespace: dev
        default-group: dev
        enabled: true
      qa:
        name: qa
        description: qa环境
        end-point: mse.cn-shanghai.aliyuncs.com
        instance-id: mse_regserverless_cn-3mp3ys14x01
        server-addr: mse-4d0af020-nacos-ans.mse.aliyuncs.com:8848
        access-key: LTAI5tJ72ozLyf6eQnsupgV5
        secret-key: ******************************
        namespace: qa
        default-group: qa
        enabled: true
      uat:
        name: uat
        server-addr: mse-f3de5d70-nacos-ans.mse.aliyuncs.com:8848
        instance-id: mse_prepaid_public_cn-k963wd2yi02
        access-key: LTAI5tJ72ozLyf6eQnsupgV5
        secret-key: ******************************
        description: uat环境
        namespace: uat
        default-group: uat
        enabled: true

# 日志配置
logging:
  level:
    com.just.nacos: DEBUG
    root: INFO
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n'