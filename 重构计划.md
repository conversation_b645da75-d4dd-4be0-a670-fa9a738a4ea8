# DM Auto Utils 项目重构计划

## 项目概述

**项目名称**: DM Auto Utils  
**当前版本**: 1.1  
**重构目标**: 提升代码扩展性，模块化架构，分离业务逻辑，统一操作接口  

## 一、当前架构分析

### 1.1 现有架构问题

**1. 紧耦合设计**
- GitLab操作直接使用gitlab4j-api，与JGit混用
- 命令行接口与业务逻辑强耦合
- 配置信息硬编码在NacosConfig类中

**2. 扩展性限制**
- 文件操作分散在各个类中，缺乏统一管理
- 配置模板写死在代码中，难以维护
- 环境配置无法动态扩展

**3. 界面限制**
- 仅有Spring Shell命令行界面
- 缺乏Web界面支持
- 用户体验不友好

### 1.2 核心模块分析

**配置模块**
- `NacosConfig`: 硬编码环境配置
- `NacosConfigService`: Nacos操作服务
- `EnvironmentConfig`: 环境相关配置

**GitLab操作模块**
- `********************`: GitLab操作接口
- `GitlabOperationsAccessor`: 使用gitlab4j-api实现
- `IGitLabService`: GitLab服务接口

**命令行模块**
- `NacosConfigCommands`: Spring Shell命令实现
- `Canal2KafkaConfigCommands`: Canal配置命令

**配置生成模块**
- `ConfigurationWriter`: 配置写入器
- `NacosConfigurationWriter`: Nacos配置写入实现
- `ConfigurationGenerator`: 配置生成器接口

## 二、重构目标

### 2.1 架构目标

**1. 模块化架构**
- 清晰的层次划分
- 松耦合设计
- 高内聚低耦合

**2. 扩展性提升**
- 支持多种Git操作方式
- 可配置的环境管理
- 插件化的模板系统

**3. 接口统一**
- 统一的REST API接口
- 标准化的数据交换格式
- 完善的错误处理机制

### 2.2 技术目标

**1. Git操作标准化**
- 完全使用org.eclipse.jgit原生操作
- 移除gitlab4j-api依赖
- 标准化的Git工作流

**2. 配置外部化**
- 环境配置文件化
- 动态配置加载
- 配置热更新支持

**3. 模板工厂化**
- 配置模板抽离
- 模板工厂模式
- 可扩展的模板系统

## 三、新架构设计

### 3.1 模块化架构设计

**总体模块结构**
```
dm-auto-utils/
├── dm-common-core/                    # 公共核心模块
├── dm-git-core/                       # Git操作核心模块  
├── dm-nacos-core/                     # Nacos操作核心模块
├── dm-template-core/                  # 模板引擎核心模块
├── dm-file-utils/                     # 文件操作工具模块
├── dm-web-api/                        # REST API模块
├── dm-web-console/                    # Web前端控制台
├── dm-application/                    # 主应用模块
└── pom.xml                           # 父级POM
```

### 3.2 各模块详细设计

#### 3.2.1 dm-common-core 公共核心模块

**职责**：提供通用的基础功能和工具类

**包结构**
```
dm-common-core/
├── src/main/java/com/just/common/
│   ├── constants/                     # 常量定义
│   │   ├── GlobalConstants.java      # 全局常量
│   │   └── ErrorCodes.java           # 错误码定义
│   ├── dto/                          # 数据传输对象
│   │   ├── ApiResponse.java          # 统一响应格式
│   │   ├── PageResult.java           # 分页结果
│   │   └── BaseRequest.java          # 基础请求
│   ├── enums/                        # 枚举定义
│   │   ├── EnvironmentType.java      # 环境类型(对应现有EnvPathEnum)
│   │   ├── OperationStatus.java      # 操作状态
│   │   └── LogLevel.java             # 日志级别
│   ├── exception/                    # 异常定义
│   │   ├── BusinessException.java    # 业务异常
│   │   ├── SystemException.java      # 系统异常
│   │   └── ConfigurationException.java # 配置异常
│   ├── utils/                        # 工具类
│   │   ├── StringUtils.java          # 字符串工具(增强版)
│   │   ├── JsonUtils.java            # JSON工具
│   │   ├── DateUtils.java            # 日期工具
│   │   └── ValidationUtils.java      # 验证工具
│   └── config/                       # 配置类
│       ├── ThreadPoolConfig.java     # 线程池配置
│       └── SerializationConfig.java  # 序列化配置
└── pom.xml
```

**核心类实现**
```java
// GlobalConstants.java
public final class GlobalConstants {
    public static final String DEFAULT_BRANCH_PREFIX = "auto-upgrade-";
    public static final String NACOS_UPGRADE_BRANCH = "nacos-upgrade";
    public static final String CONFIG_SERVER_BRANCH = "auto-upgrade-config-server";
    
    // 从现有代码中提取的常量
    public static final String[] SUPPORTED_ENVIRONMENTS = {"dev", "qa", "uat", "prd"};
    public static final String BOOTSTRAP_PROPERTIES = "bootstrap.properties";
    public static final String APPLICATION_PROPERTIES = "application.properties";
    public static final String POM_XML = "pom.xml";
    public static final String LOGBACK_XML = "logback.xml";
}

// EnvironmentType.java - 对应现有的EnvPathEnum
public enum EnvironmentType {
    DEV("dev", "开发环境"),
    QA("qa", "测试环境"), 
    UAT("uat", "预发环境"),
    PRD("prd", "生产环境");
    
    private final String code;
    private final String description;
    
    // 从现有EnvPathEnum迁移逻辑
}
```

#### 3.2.2 dm-nacos-core Nacos操作核心模块

**职责**：封装所有Nacos相关的操作，这是最重要的模块

**包结构**
```
dm-nacos-core/
├── src/main/java/com/just/nacos/
│   ├── config/                       # 配置管理
│   │   ├── NacosConfig.java          # Nacos配置(重构现有类)
│   │   ├── NacosEnvironment.java     # 环境配置
│   │   └── NacosNamespace.java       # 命名空间配置
│   ├── service/                      # 服务层
│   │   ├── NacosConfigService.java   # Nacos配置服务接口(现有)
│   │   ├── NacosConfigServiceImpl.java # 实现类(现有)
│   │   ├── NacosUpgradeService.java  # Nacos升级服务(新增)
│   │   └── NacosValidationService.java # 配置验证服务
│   ├── processor/                    # 处理器(从现有代码迁移)
│   │   ├── PropertyProcessor.java    # 属性处理器接口(现有)
│   │   ├── NacosPropertyProcessor.java # Nacos属性处理器(现有)
│   │   └── EnvironmentVariableCollector.java # 环境变量收集器(新增)
│   ├── model/                        # 数据模型
│   │   ├── NacosProperty.java        # Nacos属性(现有)
│   │   ├── UpgradeRequest.java       # 升级请求
│   │   ├── UpgradeResult.java        # 升级结果
│   │   ├── UnresolvedVariable.java   # 未解析变量
│   │   └── ConfigValidationResult.java # 配置验证结果
│   ├── reader/                       # 读取器(从现有代码迁移)
│   │   ├── ExcelConfigReader.java    # Excel配置读取器(现有)
│   │   └── ExcelConfigReaderImpl.java # 实现类(现有)
│   └── cloud/                        # 云服务(从现有代码迁移)
│       ├── AliyunMseActionService.java # 阿里云MSE服务(现有)
│       └── AliyunMseActionServiceImpl.java # 实现类(现有)
└── pom.xml
```

**关键实现 - NacosUpgradeService（基于现有NacosConfigCommands.generateProps）**
```java
@Service
@Slf4j
public class NacosUpgradeService {
    
    private final NacosConfigService nacosConfigService;
    private final PropertyProcessor propertyProcessor;
    private final EnvironmentVariableCollector variableCollector;
    private final ConfigTemplateFactory templateFactory;
    
    /**
     * 核心升级方法 - 基于NacosConfigCommands.generateProps重构
     * 
     * @param request 升级请求
     * @return 升级结果
     */
    public UpgradeResult upgradeNacosConfig(UpgradeRequest request) {
        UpgradeResult result = new UpgradeResult();
        result.setTaskId(UUID.randomUUID().toString());
        result.setStartTime(System.currentTimeMillis());
        
        try {
            log.warn("开始Nacos配置升级, 服务: {}, 环境: {}", 
                request.getServiceName(), request.getEnv());
            
            // 1. 验证请求参数
            validateUpgradeRequest(request);
            
            // 2. 创建配置写入器 - 使用现有的ConfigurationWriterFactory
            ConfigurationWriter writer = templateFactory.createNacosWriter(
                request.getServiceName(),
                request.getPath(), 
                request.getRefresh(),
                request.getEnv(),
                request.getRegister()
            );
            
            // 3. 处理属性 - 使用现有的NacosPropertyProcessor
            NacosPropertyProcessor processor = new NacosPropertyProcessor(request.getEnv());
            
            // 4. 生成application-nacos.properties
            String applicationConfig = writer.generateApplicationProperties(processor);
            result.setApplicationConfig(applicationConfig);
            log.warn("√ 生成application-nacos.properties成功");
            
            // 5. 生成bootstrap.properties  
            writer.generateBootstrapProperties(processor);
            log.warn("√ 生成bootstrap.properties成功");
            
            // 6. 生成其他配置文件(logback.xml, pom.xml)
            writer.generateOtherProperties(processor);
            log.warn("√ 生成其他配置文件成功");
            
            // 7. 收集未解析的环境变量
            List<UnresolvedVariable> unresolvedVars = variableCollector.collectUnresolvedVariables(
                request.getPath(), applicationConfig
            );
            result.setUnresolvedVariables(unresolvedVars);
            
            if (!unresolvedVars.isEmpty()) {
                log.warn("发现{}个未解析的环境变量", unresolvedVars.size());
                result.addWarning(String.format("发现%d个未解析的环境变量", unresolvedVars.size()));
            }
            
            // 8. 升级注册中心配置 - 基于现有upgradeRegisterCenter逻辑
            if ("true".equals(request.getRegister())) {
                writer.upgradeRegisterCenter(applicationConfig);
                log.warn("√ 升级注册中心配置成功");
            }
            
            // 9. 发布到Nacos (如果需要)
            if ("true".equals(request.getPublish())) {
                publishToNacos(writer, applicationConfig, request);
                log.warn("√ 发布到Nacos成功");
            }
            
            result.setSuccess(true);
            result.setMessage("配置升级成功");
            result.setEndTime(System.currentTimeMillis());
            
            // 10. 输出未解析环境变量 - 对应现有printUnresolvedEnvVars
            printUnresolvedEnvVars(unresolvedVars);
            
            log.warn("Nacos配置升级完成");
            return result;
            
        } catch (Exception e) {
            log.error("Nacos配置升级失败", e);
            result.setSuccess(false);
            result.setMessage("升级失败: " + e.getMessage());
            result.setErrorDetails(getStackTrace(e));
            result.setEndTime(System.currentTimeMillis());
            return result;
        }
    }
    
    /**
     * 发布配置到Nacos - 基于现有publishToNacos逻辑
     */
    private void publishToNacos(ConfigurationWriter writer, String content, UpgradeRequest request) 
            throws NacosException {
        
        // 检查生产环境发布确认 - 保持现有逻辑
        if ("prd".equals(request.getEnv())) {
            String nacosContent = nacosConfigService.getConfig(
                StringUtils.replace(request.getServiceName(), "_", "-") + ".properties", 
                request.getServiceName(), 
                5000
            );
            
            if (nacosContent != null) {
                log.warn("获取到的mse nacos配置内容: {}", nacosContent);
                // 在Web环境中，这里需要通过回调或者状态返回给前端确认
                // 暂时跳过交互式确认，由前端处理
            }
        }
        
        writer.publishToNacos(content);
    }
    
    /**
     * 打印未解析的环境变量 - 基于现有printUnresolvedEnvVars逻辑  
     */
    private void printUnresolvedEnvVars(List<UnresolvedVariable> unresolvedVars) {
        if (unresolvedVars.isEmpty()) {
            return;
        }
        
        log.warn("=== 未解析的环境变量列表 ===");
        for (UnresolvedVariable var : unresolvedVars) {
            log.warn("文件: {}, 变量: {}, 行: {}", 
                var.getFile(), var.getVariable(), var.getLineNumber());
            log.warn("上下文: {}", var.getContext());
            log.warn("建议: {}", var.getSuggestion());
            log.warn("---");
        }
        log.warn("请检查以上环境变量配置");
    }
}

/**
 * 环境变量收集器 - 新增功能
 */
@Component
@Slf4j
public class EnvironmentVariableCollector {
    
    /**
     * 收集未解析的环境变量
     */
    public List<UnresolvedVariable> collectUnresolvedVariables(String projectPath, String configContent) {
        List<UnresolvedVariable> unresolvedVars = new ArrayList<>();
        
        try {
            // 1. 扫描生成的配置文件
            Path resourcesPath = Paths.get(projectPath, "src", "main", "resources");
            if (!Files.exists(resourcesPath)) {
                return unresolvedVars;
            }
            
            // 2. 遍历配置文件
            Files.walk(resourcesPath)
                .filter(path -> isConfigFile(path))
                .forEach(file -> {
                    collectFromFile(file, unresolvedVars);
                });
                
            // 3. 检查传入的配置内容
            collectFromContent(configContent, "application-nacos.properties", unresolvedVars);
            
        } catch (Exception e) {
            log.error("收集环境变量时发生错误", e);
        }
        
        return unresolvedVars;
    }
    
    private boolean isConfigFile(Path path) {
        String fileName = path.getFileName().toString();
        return fileName.endsWith(".properties") || 
               fileName.endsWith(".yml") || 
               fileName.endsWith(".yaml");
    }
    
    private void collectFromFile(Path file, List<UnresolvedVariable> unresolvedVars) {
        try {
            List<String> lines = Files.readAllLines(file);
            collectFromLines(lines, file.getFileName().toString(), unresolvedVars);
        } catch (IOException e) {
            log.error("读取文件失败: {}", file.getFileName(), e);
        }
    }
    
    private void collectFromContent(String content, String fileName, List<UnresolvedVariable> unresolvedVars) {
        if (content == null) return;
        
        String[] lines = content.split("\\n");
        collectFromLines(Arrays.asList(lines), fileName, unresolvedVars);
    }
    
    private void collectFromLines(List<String> lines, String fileName, List<UnresolvedVariable> unresolvedVars) {
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)\\}");
        
        for (int i = 0; i < lines.size(); i++) {
            String line = lines.get(i);
            Matcher matcher = pattern.matcher(line);
            
            while (matcher.find()) {
                String variable = matcher.group(0);  // ${VARIABLE_NAME}
                String varName = matcher.group(1);   // VARIABLE_NAME
                
                UnresolvedVariable unresolvedVar = new UnresolvedVariable();
                unresolvedVar.setFile(fileName);
                unresolvedVar.setVariable(variable);
                unresolvedVar.setContext(line.trim());
                unresolvedVar.setLineNumber(i + 1);
                unresolvedVar.setSuggestion(generateSuggestion(varName));
                
                unresolvedVars.add(unresolvedVar);
            }
        }
    }
    
    private String generateSuggestion(String varName) {
        Map<String, String> suggestions = Map.of(
            "NACOS_ADDR", "设置环境变量NACOS_ADDR为Nacos服务器地址",
            "NACOS_NAMESPACE", "设置环境变量NACOS_NAMESPACE为命名空间ID", 
            "NACOS_ACCESS_KEY", "设置环境变量NACOS_ACCESS_KEY为访问密钥",
            "NACOS_SECRET_KEY", "设置环境变量NACOS_SECRET_KEY为访问密钥Secret"
        );
        
        return suggestions.getOrDefault(varName, 
            "请在环境变量中设置 " + varName + " 或在配置模板中提供默认值");
    }
}
```

#### 3.2.3 dm-git-core Git操作核心模块

**职责**：封装所有Git相关操作，使用纯JGit实现

**包结构**
```
dm-git-core/
├── src/main/java/com/just/git/
│   ├── service/                      # 服务层
│   │   ├── GitOperationService.java  # Git操作服务接口
│   │   └── JGitOperationServiceImpl.java # JGit实现(重构现有GitlabOperationsAccessor)
│   ├── model/                        # 数据模型
│   │   ├── GitRepository.java        # Git仓库信息
│   │   ├── GitCloneRequest.java      # 克隆请求
│   │   ├── GitCommitResult.java      # 提交结果
│   │   └── GitMergeRequest.java      # 合并请求
│   ├── config/                       # 配置
│   │   └── GitConfig.java            # Git配置
│   └── utils/                        # 工具类
│       └── GitUtils.java             # Git工具类
└── pom.xml
```

**核心实现 - JGitOperationServiceImpl（基于现有GitlabOperationsAccessor）**
```java
@Service
@Slf4j  
public class JGitOperationServiceImpl implements GitOperationService {
    
    private final GitConfig gitConfig;
    private final FileOperationService fileOperationService;
    
    /**
     * Git克隆 - 基于现有gitClone()方法重构
     */
    @Override
    public Git gitClone(String serviceName, Path localPath) throws GitAPIException, IOException {
        
        // 1. 构建仓库URL - 保持现有逻辑
        String cloneUrl = buildRepositoryUrl(serviceName);
        
        // 2. 检查是否已存在Git仓库
        if (isGitRepository(localPath.toFile())) {
            log.info("本地已存在Git仓库，直接打开: {}", localPath);
            return Git.open(localPath.toFile());
        }
        
        // 3. 执行克隆操作
        log.info("开始克隆项目: {} -> {}", cloneUrl, localPath);
        
        return Git.cloneRepository()
            .setURI(cloneUrl)
            .setDirectory(localPath.toFile())
            .setBranch("master")
            .setCredentialsProvider(createCredentialsProvider())
            .call();
    }
    
    /**
     * 切换分支 - 基于现有switchBranch()方法
     */
    @Override
    public Git switchBranch(Git git, String branchName) throws Exception {
        
        // 1. 检查分支是否存在
        boolean branchExists = checkBranchExists(git, branchName);
        
        // 2. 如果不存在则创建
        if (!branchExists) {
            log.info("分支{}不存在，正在创建...", branchName);
            git.branchCreate()
                .setName(branchName)
                .call();
        }
        
        // 3. 检出分支
        git.checkout()
            .setName(branchName)
            .call();
            
        log.info("当前分支: {}", branchName);
        return git;
    }
}
```

#### 3.2.4 dm-template-core 模板引擎核心模块

**职责**：管理所有配置模板，实现模板工厂模式

**包结构**
```
dm-template-core/
├── src/main/java/com/just/template/
│   ├── factory/                      # 工厂类
│   │   ├── TemplateFactory.java      # 模板工厂接口
│   │   └── ConfigTemplateFactory.java # 配置模板工厂(重构现有ConfigurationWriterFactory)
│   ├── template/                     # 模板实现
│   │   ├── ConfigTemplate.java       # 模板接口
│   │   ├── BootstrapTemplate.java    # Bootstrap模板
│   │   ├── ApplicationNacosTemplate.java # Application Nacos模板
│   │   └── PomTemplate.java          # POM模板
│   ├── writer/                       # 写入器(从现有代码迁移)
│   │   ├── ConfigurationWriter.java  # 配置写入器接口(现有)
│   │   └── NacosConfigurationWriter.java # Nacos配置写入器(现有,重构)
│   ├── engine/                       # 模板引擎
│   │   └── FreeMarkerEngine.java     # FreeMarker引擎封装
│   └── context/                      # 模板上下文
│       └── TemplateContext.java      # 模板上下文
├── src/main/resources/templates/     # 模板文件
│   ├── bootstrap.ftl                 # Bootstrap模板
│   ├── application-nacos.ftl         # Application Nacos模板
│   └── pom-parent.ftl               # POM父级模板
└── pom.xml
```

**关键实现 - ConfigTemplateFactory（重构现有ConfigurationWriterFactory）**
```java
@Component
@Slf4j
public class ConfigTemplateFactory implements TemplateFactory {
    
    private final FreeMarkerEngine freeMarkerEngine;
    private final NacosConfig nacosConfig;
    private final FileOperationService fileOperationService;
    
    /**
     * 创建Nacos配置写入器 - 重构现有createNacosConfigurationWriter
     */
    @Override
    public ConfigurationWriter createNacosWriter(String serviceName, String path, 
                                                String refresh, String env, String register) {
        
        // 从现有代码迁移逻辑
        TemplateContext context = buildTemplateContext(serviceName, path, refresh, env, register);
        
        return new NacosConfigurationWriter(
            freeMarkerEngine, 
            context, 
            fileOperationService
        );
    }
    
    private TemplateContext buildTemplateContext(String serviceName, String path, 
                                               String refresh, String env, String register) {
        TemplateContext context = new TemplateContext();
        context.put("serviceName", serviceName);
        context.put("path", path);
        context.put("refresh", refresh);
        context.put("env", env);
        context.put("register", register);
        
        // 添加环境相关配置
        NacosEnvironment nacosEnv = nacosConfig.getEnvironment(env);
        context.put("nacosAddr", nacosEnv.getAddr());
        context.put("nacosNamespace", nacosEnv.getNamespace());
        
        return context;
    }
}
```

#### 3.2.5 dm-file-utils 文件操作工具模块

**职责**：统一所有文件操作，提供标准化的文件处理能力

**包结构**
```
dm-file-utils/
├── src/main/java/com/just/file/
│   ├── service/                      # 服务层
│   │   ├── FileOperationService.java # 文件操作服务接口
│   │   └── FileOperationServiceImpl.java # 实现类
│   ├── model/                        # 数据模型
│   │   ├── FileInfo.java             # 文件信息
│   │   ├── FileCopyResult.java       # 文件拷贝结果
│   │   └── FileBackupInfo.java       # 文件备份信息
│   ├── utils/                        # 工具类
│   │   ├── FilePathUtils.java        # 文件路径工具
│   │   ├── FileContentUtils.java     # 文件内容工具
│   │   └── FileBackupUtils.java      # 文件备份工具
│   └── exception/                    # 异常类
│       └── FileOperationException.java # 文件操作异常
└── pom.xml
```

**核心实现**
```java
@Service
@Slf4j
public class FileOperationServiceImpl implements FileOperationService {
    
    /**
     * 统一文件写入操作
     */
    @Override
    public void writeFile(Path filePath, String content) throws FileOperationException {
        try {
            // 1. 确保父目录存在
            Files.createDirectories(filePath.getParent());
            
            // 2. 备份现有文件
            if (Files.exists(filePath)) {
                backupFile(filePath);
            }
            
            // 3. 写入新内容
            Files.write(filePath, content.getBytes(StandardCharsets.UTF_8));
            
            log.info("文件写入成功: {}", filePath);
            
        } catch (IOException e) {
            throw new FileOperationException("文件写入失败: " + filePath, e);
        }
    }
    
    /**
     * 文件备份
     */
    private void backupFile(Path filePath) throws IOException {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String backupName = filePath.getFileName() + ".backup." + timestamp;
        Path backupPath = filePath.getParent().resolve(backupName);
        
        Files.copy(filePath, backupPath);
        log.info("文件已备份: {} -> {}", filePath, backupPath);
    }
}
```

#### 3.2.6 dm-web-api REST API模块

**职责**：提供REST API接口，替换原有Spring Shell命令

**包结构**
```
dm-web-api/
├── src/main/java/com/just/web/
│   ├── controller/                   # 控制器层
│   │   ├── NacosController.java      # Nacos操作接口
│   │   ├── GitController.java        # Git操作接口
│   │   └── SystemController.java     # 系统管理接口
│   ├── dto/                         # DTO对象
│   │   ├── request/                 # 请求DTO
│   │   │   ├── NacosUpgradeRequest.java # Nacos升级请求
│   │   │   ├── GitOperationRequest.java # Git操作请求
│   │   │   └── ConfigSearchRequest.java # 配置搜索请求
│   │   └── response/                # 响应DTO
│   │       ├── NacosUpgradeResponse.java # Nacos升级响应
│   │       ├── GitOperationResponse.java # Git操作响应
│   │       └── ConfigListResponse.java # 配置列表响应
│   ├── service/                     # 服务层
│   │   ├── TaskManagementService.java # 任务管理服务
│   │   └── ErrorLogService.java      # 错误日志服务
│   └── config/                      # 配置类
│       ├── WebMvcConfig.java        # MVC配置
│       └── AsyncConfig.java         # 异步配置
└── pom.xml
```

**关键实现 - NacosController（替换NacosConfigCommands）**
```java
@RestController
@RequestMapping("/api/nacos")
@Slf4j
public class NacosController {
    
    private final NacosUpgradeService nacosUpgradeService;
    private final TaskManagementService taskManagementService;
    private final ErrorLogService errorLogService;
    
    /**
     * Nacos配置升级 - 对应原generateProps命令
     */
    @PostMapping("/upgrade")
    public ApiResponse<NacosUpgradeResponse> upgradeNacosConfig(
            @RequestBody @Valid NacosUpgradeRequest request) {
        
        try {
            // 创建异步任务
            String taskId = taskManagementService.createTask("NACOS_UPGRADE", request);
            
            // 异步执行升级
            CompletableFuture.supplyAsync(() -> {
                try {
                    UpgradeResult result = nacosUpgradeService.upgradeNacosConfig(request);
                    taskManagementService.updateTaskResult(taskId, result);
                    return result;
                } catch (Exception e) {
                    log.error("Nacos升级异步执行失败", e);
                    errorLogService.logError("NACOS_UPGRADE", e, request);
                    taskManagementService.updateTaskError(taskId, e);
                    throw new RuntimeException(e);
                }
            });
            
            NacosUpgradeResponse response = new NacosUpgradeResponse();
            response.setTaskId(taskId);
            response.setStatus("RUNNING");
            response.setMessage("升级任务已启动");
            
            return ApiResponse.success(response);
            
        } catch (Exception e) {
            log.error("启动Nacos升级任务失败", e);
            return ApiResponse.error("启动升级任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询升级任务状态
     */
    @GetMapping("/upgrade/status/{taskId}")
    public ApiResponse<TaskStatus> getUpgradeStatus(@PathVariable String taskId) {
        TaskStatus status = taskManagementService.getTaskStatus(taskId);
        return ApiResponse.success(status);
    }
    
    /**
     * 获取环境变量收集结果
     */
    @GetMapping("/variables/unresolved/{taskId}")
    public ApiResponse<List<UnresolvedVariable>> getUnresolvedVariables(@PathVariable String taskId) {
        List<UnresolvedVariable> variables = taskManagementService.getUnresolvedVariables(taskId);
        return ApiResponse.success(variables);
    }
}
```

#### 3.2.7 dm-web-console Web前端控制台

**职责**：提供Web界面，替换Spring Shell命令行

**项目结构**
```
dm-web-console/
├── src/main/resources/static/        # 静态资源
│   ├── css/                         # 样式文件
│   │   ├── bootstrap.min.css        # Bootstrap框架
│   │   ├── common.css               # 公共样式
│   │   └── component.css            # 组件样式
│   ├── js/                          # JavaScript文件
│   │   ├── lib/                     # 第三方库
│   │   │   ├── bootstrap.bundle.min.js # Bootstrap JS
│   │   │   └── axios.min.js         # HTTP库
│   │   ├── components/              # 组件
│   │   │   ├── SearchSelect.js      # 搜索选择组件
│   │   │   ├── BooleanSelect.js     # 布尔选择组件
│   │   │   └── TaskStatus.js        # 任务状态组件
│   │   ├── pages/                   # 页面脚本
│   │   │   ├── nacos-env.js         # Nacos环境管理
│   │   │   ├── nacos-config.js      # Nacos配置管理
│   │   │   └── project-upgrade.js   # 项目升级
│   │   └── common.js                # 公共脚本
│   └── pages/                       # HTML页面
│       ├── index.html               # 首页
│       ├── nacos-env.html           # Nacos环境管理
│       ├── nacos-config.html        # Nacos配置管理
│       └── project-upgrade.html     # 项目升级
└── pom.xml
```

**关键实现 - SearchSelect组件（替换原有Provider）**
```javascript
// SearchSelect.js - 替换原有的NacosPromptProvider等
class SearchSelect {
    constructor(containerId, options) {
        this.container = document.getElementById(containerId);
        this.options = options || {};
        this.data = [];
        this.filteredData = [];
        this.selectedValue = null;
        
        this.init();
    }
    
    init() {
        this.render();
        this.bindEvents();
        this.loadData();
    }
    
    render() {
        this.container.innerHTML = `
            <div class="search-select">
                <div class="input-group">
                    <input type="text" class="form-control search-input" 
                           placeholder="${this.options.placeholder || '请输入搜索关键字'}" 
                           autocomplete="off">
                    <button class="btn btn-outline-secondary dropdown-toggle" 
                            type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-chevron-down"></i>
                    </button>
                    <ul class="dropdown-menu w-100 dropdown-list"></ul>
                </div>
            </div>
        `;
    }
    
    async loadData() {
        if (this.options.dataUrl) {
            try {
                const response = await axios.get(this.options.dataUrl);
                this.data = response.data.data || response.data;
                this.updateDropdown();
            } catch (error) {
                console.error('加载数据失败:', error);
            }
        } else if (this.options.data) {
            this.data = this.options.data;
            this.updateDropdown();
        }
    }
    
    updateDropdown() {
        const searchInput = this.container.querySelector('.search-input');
        const keyword = searchInput.value.toLowerCase();
        
        this.filteredData = this.data.filter(item => {
            const text = typeof item === 'string' ? item : item.label || item.name;
            return text.toLowerCase().includes(keyword);
        });
        
        this.renderDropdown();
    }
    
    renderDropdown() {
        const dropdownList = this.container.querySelector('.dropdown-list');
        dropdownList.innerHTML = '';
        
        if (this.filteredData.length === 0) {
            dropdownList.innerHTML = '<li><span class="dropdown-item-text text-muted">无匹配结果</span></li>';
            return;
        }
        
        this.filteredData.forEach(item => {
            const li = document.createElement('li');
            const text = typeof item === 'string' ? item : item.label || item.name;
            const value = typeof item === 'string' ? item : item.value || item.code;
            
            li.innerHTML = `<a class="dropdown-item" href="#" data-value="${value}">${text}</a>`;
            dropdownList.appendChild(li);
        });
    }
}
```

#### 3.2.8 dm-application 主应用模块

**职责**：应用启动和整体配置

**包结构**
```
dm-application/
├── src/main/java/com/just/
│   ├── Application.java              # 主启动类(现有,迁移)
│   ├── config/                      # 应用配置
│   │   ├── AppConfig.java           # 应用配置
│   │   └── DatabaseConfig.java      # 数据库配置(如需要)
│   └── shell/                       # Shell命令(保留,可选)
│       └── SystemShellCommands.java # 系统Shell命令
├── src/main/resources/
│   ├── application.yml              # 应用配置(现有,重构)
│   ├── environments.yml             # 环境配置(新增)
│   └── banner.txt                   # 启动横幅(现有)
└── pom.xml
```

**核心配置 - environments.yml（外部化环境配置）**
```yaml
# environments.yml - 外部化环境配置
git:
  baseUrl: "https://gitlab.example.com/microservice"
  token: "${GITLAB_TOKEN:your-gitlab-token}"
  timeout: 30000

nacos:
  environments:
    dev:
      addr: "${NACOS_ADDR_DEV:127.0.0.1:8848}"
      namespace: "${NACOS_NAMESPACE_DEV:dev-namespace-id}"
      accessKey: "${NACOS_ACCESS_KEY_DEV:}"
      secretKey: "${NACOS_SECRET_KEY_DEV:}"
      clusterName: "DEFAULT"
      enabled: true
    qa:
      addr: "${NACOS_ADDR_QA:nacos-qa.example.com:8848}"
      namespace: "${NACOS_NAMESPACE_QA:qa-namespace-id}"
      accessKey: "${NACOS_ACCESS_KEY_QA:}"
      secretKey: "${NACOS_SECRET_KEY_QA:}"
      clusterName: "DEFAULT"
      enabled: true
    uat:
      addr: "${NACOS_ADDR_UAT:nacos-uat.example.com:8848}"
      namespace: "${NACOS_NAMESPACE_UAT:uat-namespace-id}"
      accessKey: "${NACOS_ACCESS_KEY_UAT:}"
      secretKey: "${NACOS_SECRET_KEY_UAT:}"
      clusterName: "DEFAULT"
      enabled: true
    prd:
      addr: "${NACOS_ADDR_PRD:nacos-prd.example.com:8848}"
      namespace: "${NACOS_NAMESPACE_PRD:prd-namespace-id}"
      accessKey: "${NACOS_ACCESS_KEY_PRD:}"
      secretKey: "${NACOS_SECRET_KEY_PRD:}"
      clusterName: "DEFAULT"
      enabled: true

# 各环境的默认配置
defaults:
  refresh: "true"
  register: "true"  
  publish: "false"
  timeout: 5000
```

### 3.3 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    表现层 (Presentation Layer)                │
│  ┌─────────────────┐    ┌─────────────────┐                  │
│  │   Web Console   │    │   REST API      │                  │
│  │   (Vue.js)      │    │   (Spring MVC)  │                  │
│  └─────────────────┘    └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│                    应用层 (Application Layer)                 │
│  ┌─────────────────┐    ┌─────────────────┐                  │
│  │  Nacos Service  │    │   Git Service   │                  │
│  │   Management    │    │   Management    │                  │
│  └─────────────────┘    └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│                    领域层 (Domain Layer)                      │
│  ┌─────────────────┐    ┌─────────────────┐                  │
│  │ Template Factory│    │  Config Engine  │                  │
│  │     System      │    │     System      │                  │
│  └─────────────────┘    └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│                   基础设施层 (Infrastructure Layer)            │
│  ┌─────────────────┐    ┌─────────────────┐                  │
│  │  File Utils     │    │  Git Utils      │                  │
│  │    Toolkit      │    │   (JGit Only)   │                  │
│  └─────────────────┘    └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 核心模块重新设计

**1. Git操作模块 (git-core)**
```java
// 纯JGit实现
public interface GitOperationService {
    GitRepository clone(GitCloneRequest request);
    GitBranch createBranch(String branchName);
    GitCommit commit(String message);
    GitPushResult push(String branch);
    GitMergeRequest createMergeRequest(MergeRequestInfo info);
}
```

**2. Nacos管理模块 (nacos-core)**
```java
// 环境配置外部化
public interface NacosConfigManager {
    NacosEnvironment getEnvironment(String env);
    ConfigPublishResult publishConfig(ConfigPublishRequest request);
    List<NacosConfig> listConfigs(String env, String namespace);
}
```

**3. 配置模板模块 (template-core)**
```java
// 模板工厂
public interface TemplateFactory {
    ConfigTemplate createTemplate(TemplateType type);
    String generateConfig(TemplateContext context);
    void registerTemplate(String name, TemplateProvider provider);
}
```

**4. 文件操作模块 (file-utils)**
```java
// 统一文件操作
public interface FileOperationService {
    FileContent readFile(String path);
    FileWriteResult writeFile(String path, String content);
    List<FileInfo> findFiles(String directory, String pattern);
    FileBackup backup(String path);
}
```

## 四、基于现有代码的重构实施计划

### 4.0 重构前准备（1天）

**4.0.1 代码备份和分支创建**
- 创建重构专用分支：`feature/refactor-architecture`
- 备份当前稳定版本
- 准备重构环境

**4.0.2 现有代码分析**
基于当前代码结构分析：
```java
// 现有核心类分析
- NacosConfigCommands.java          // 命令行接口 -> 需要重构为REST API
- GitlabOperationsAccessor.java     // GitLab操作 -> 需要JGit化
- NacosConfigurationWriter.java     // 配置写入 -> 需要模板化
- ConfigurationWriterFactory.java   // 工厂类 -> 需要增强
- NacosConfig.java                  // 配置类 -> 需要外部化
- NacosConfigServiceImpl.java       // Nacos服务 -> 需要增强
```

## 四、重构实施计划

### 4.1 阶段一：基础设施重构（1-2周）

**4.1.1 创建文件操作工具类（2天）**
```java
// 1. 创建统一文件操作接口
public interface FileOperationService {
    FileContent readFile(String path);
    FileWriteResult writeFile(String path, String content);
    List<FileInfo> findFiles(String directory, String pattern);
    FileBackup backup(String path);
}

// 2. 实现类
@Service
public class FileOperationServiceImpl implements FileOperationService {
    // 替换NacosConfigurationWriter中的所有文件操作
}
```

**4.1.2 重构GitlabOperationsAccessor为纯JGit实现（3天）**
```java
// 1. 保留********************接口
public interface ******************** {
    Git gitClone() throws GitAPIException, IOException;
    Git switchBranch(String branchName) throws Exception;
    MergeRequest commitAndPush(String title, String commitMessage) throws Exception;
    // ... 其他方法
}

// 2. 创建新的JGit实现类
@Service 
public class JGitOperationServiceImpl implements ******************** {
    // 移除所有gitlab4j-api调用
    // 使用纯JGit实现所有Git操作
    // 保持现有接口不变，确保向后兼容
}
```

**4.1.3 重构NacosConfig为外部化配置（2天）**
```java
// 1. 修改NacosConfig.java
@ConfigurationProperties(prefix = "nacos")
public class NacosConfig {
    private Map<String, NacosEnvironment> environments = new HashMap<>();
    // 移除硬编码的服务器地址和实例ID
}

// 2. 创建环境配置类
public class NacosEnvironment {
    private String server;
    private String instanceId;
    private String accessKey;
    private String secretKey;
    private List<NacosNamespace> namespaces;
}
```

### 4.2 阶段二：配置外部化（1周）

**2.1 环境配置文件化**
创建环境配置文件：
```yaml
# environments.yml
git:
  url: "http://172.16.100.23"
  token: "${GITLAB_TOKEN}"
  defaultBranch: "master"
  upgradePrefix: "auto-upgrade-"

nacos:
  environments:
    dev:
      server: "mse-4d0af020-nacos-ans.mse.aliyuncs.com:8848"
      instanceId: "mse_regserverless_cn-3mp3ys14x01"
      namespace: "dev"
      accessKey: "${NACOS_ACCESS_KEY}"
      secretKey: "${NACOS_SECRET_KEY}"
    qa:
      server: "mse-4d0af020-nacos-ans.mse.aliyuncs.com:8848"
      instanceId: "mse_regserverless_cn-3mp3ys14x01"
      namespace: "qa"
      accessKey: "${NACOS_ACCESS_KEY}"
      secretKey: "${NACOS_SECRET_KEY}"
    uat:
      server: "mse-f3de5d70-nacos-ans.mse.aliyuncs.com:8848"
      instanceId: "mse_prepaid_public_cn-k963wd2yi02"
      namespace: "uat"
      accessKey: "${NACOS_ACCESS_KEY}"
      secretKey: "${NACOS_SECRET_KEY}"
    prd:
      server: "mse-2e1b1f50-nacos-ans.mse.aliyuncs.com:8848"
      instanceId: "mse_prepaid_public_cn-1ls3wd2yu01"
      namespace: "prd"
      accessKey: "${NACOS_ACCESS_KEY}"
      secretKey: "${NACOS_SECRET_KEY}"
```

**2.2 配置管理服务**
- 创建ConfigurationManager
- 支持配置热加载
- 配置验证机制

### 4.3 阶段三：模板工厂实现（1-2周）

**4.3.1 抽离NacosConfigurationWriter中的模板逻辑（3天）**
```java
// 1. 创建模板接口
public interface ConfigTemplate {
    String generate(TemplateContext context);
    String getTemplateName();
    TemplateType getType();
}

// 2. 从NacosConfigurationWriter抽离bootstrap生成逻辑
public class BootstrapPropertiesTemplate implements ConfigTemplate {
    @Override
    public String generate(TemplateContext context) {
        // 将generateBootstrapContent()方法的逻辑移到这里
        // 使用FreeMarker模板引擎替代硬编码字符串拼接
    }
}

// 3. 抽离application.properties生成逻辑  
public class ApplicationNacosTemplate implements ConfigTemplate {
    @Override
    public String generate(TemplateContext context) {
        // 将generateApplicationProperties()方法的逻辑移到这里
    }
}
```

**4.3.2 重构ConfigurationWriterFactory（2天）**
```java
// 1. 增强现有的ConfigurationWriterFactory
@Component
public class ConfigurationWriterFactory {
    private final Map<TemplateType, ConfigTemplate> templates;
    private final TemplateEngine templateEngine;
    
    // 2. 添加模板创建方法
    public ConfigTemplate createTemplate(TemplateType type) {
        return templates.get(type);
    }
    
    // 3. 保持现有的createNacosWriter方法向后兼容
    public NacosConfigurationWriter createNacosWriter(...) {
        // 注入模板工厂
    }
}
```

**4.3.3 创建FreeMarker模板文件（2天）**
```freemarker
<!-- bootstrap.properties.ftl -->
spring.application.name=${serviceName}
# nacos config property
spring.cloud.nacos.config.server-addr=${nacosServerAddr}
spring.cloud.nacos.config.namespace=${nacosNamespace}
spring.cloud.nacos.config.access-key=${nacosAccessKey}
spring.cloud.nacos.config.secret-key=${nacosSecretKey}

<#if refresh == "true">
spring.cloud.nacos.config.refresh-enabled=true
</#if>

<#if register == "true">
# nacos register center property  
spring.cloud.nacos.discovery.server-addr=${nacosServerAddr}
spring.cloud.nacos.discovery.namespace=${nacosNamespace}
spring.cloud.nacos.discovery.access-key=${nacosAccessKey}
spring.cloud.nacos.discovery.secret-key=${nacosSecretKey}
</#if>

<#if sharedConfigs?has_content>
# shared configs
<#list sharedConfigs as config>
spring.cloud.nacos.config.shared-configs[${config_index}].data-id=${config.dataId}
spring.cloud.nacos.config.shared-configs[${config_index}].group=${config.group}
spring.cloud.nacos.config.shared-configs[${config_index}].refresh=${config.refresh}
</#list>
</#if>
```

### 4.4 阶段四：REST API开发（2周）

**4.4.1 基于NacosConfigCommands创建REST API（5天）**
```java
// 1. 将NacosConfigCommands中的方法转换为REST API
@RestController
@RequestMapping("/api/v1/nacos")
public class NacosConfigController {
    
    private final NacosConfigListService configListService;
    private final NacosConfigCheckService configCheckService;
    
    // 对应 listConfigs 命令
    @GetMapping("/configs")
    public ApiResponse<String> listConfigs(
        @RequestParam String env,
        @RequestParam(defaultValue = "") String namespace) {
        String result = configListService.printNacosConfigs(env, namespace);
        return ApiResponse.success(result);
    }
    
    // 对应 searchNacosFile 命令
    @GetMapping("/configs/search")
    public ApiResponse<String> searchConfigs(
        @RequestParam String env,
        @RequestParam String keyword) {
        // 调用现有的searchNacosFile逻辑
    }
    
    // 对应 generateProps 命令
    @PostMapping("/configs/upgrade")
    public ApiResponse<String> upgradeConfig(@RequestBody UpgradeRequest request) {
        String result = configListService.nacosConfigUpgrade(
            request.getServiceName(),
            request.getPath(),
            request.getEnv(),
            request.getBranchName(),
            request.getRefresh(),
            request.getPublish(),
            request.getRegister()
        );
        return ApiResponse.success(result);
    }
    
    // 对应 checkConfigs 命令
    @GetMapping("/configs/check")
    public ApiResponse<String> checkConfigs(@RequestParam String env) {
        String result = configCheckService.checkConfigs(env);
        return ApiResponse.success(result);
    }
    
    // 对应 getConfig 命令
    @GetMapping("/configs/{dataId}")
    public ApiResponse<String> getConfig(
        @PathVariable String dataId,
        @RequestParam String group,
        @RequestParam String env,
        @RequestParam(defaultValue = "") String namespace) {
        // 调用现有的getConfig逻辑
    }
    
    // 对应 publishConfig 命令
    @PostMapping("/configs/publish")
    public ApiResponse<String> publishConfig(@RequestBody PublishRequest request) {
        // 调用现有的publishConfig逻辑
    }
}
```

**4.4.2 创建数据传输对象（2天）**
```java
// 1. 请求对象
@Data
public class UpgradeRequest {
    @NotBlank(message = "服务名称不能为空")
    private String serviceName;
    private String path;
    private String env;
    private String branchName = "nacos-upgrade";
    private String refresh = "false";
    private String publish = "false"; 
    private String register = "false";
}

@Data
public class PublishRequest {
    @NotBlank
    private String dataId;
    @NotBlank
    private String group;
    @NotBlank
    private String filePath;
    @NotBlank
    private String env;
    private String namespace = "";
}

// 2. 响应对象
@Data
public class ApiResponse<T> {
    private boolean success;
    private String message;
    private T data;
    private String errorCode;
    private long timestamp;
    
    public static <T> ApiResponse<T> success(T data) {
        ApiResponse<T> response = new ApiResponse<>();
        response.success = true;
        response.data = data;
        response.timestamp = System.currentTimeMillis();
        return response;
    }
}
```

**4.4.3 添加数据提供API和异步升级状态跟踪（5天）**
```java
// 1. 环境数据API
@GetMapping("/environments")
public ApiResponse<List<String>> getEnvironments() {
    return ApiResponse.success(Arrays.asList("dev", "qa", "uat", "prd"));
}

// 2. 服务名称数据API（支持搜索）
@GetMapping("/services")
public ApiResponse<List<String>> getServices(@RequestParam(required = false) String search) {
    // 从GitLab获取项目列表，支持搜索过滤
}

// 3. 数据ID和Group数据API
@GetMapping("/dataids")
public ApiResponse<Set<String>> getAllDataIds() {
    return ApiResponse.success(configListService.getAllEnvDistinctDataId());
}

@GetMapping("/groups") 
public ApiResponse<Set<String>> getAllGroups() {
    return ApiResponse.success(configListService.getAllEnvDistinctGroup());
}

// 4. 异步升级状态跟踪API
@GetMapping("/configs/upgrade/status/{taskId}")
public ApiResponse<UpgradeStatus> getUpgradeStatus(@PathVariable String taskId) {
    UpgradeStatus status = upgradeTaskManager.getTaskStatus(taskId);
    return ApiResponse.success(status);
}

// 5. 升级详情查看API
@GetMapping("/configs/upgrade/details/{taskId}")
public ApiResponse<UpgradeDetails> getUpgradeDetails(@PathVariable String taskId) {
    UpgradeDetails details = upgradeTaskManager.getTaskDetails(taskId);
    return ApiResponse.success(details);
}
```

**环境变量收集和错误日志支持**
```java
// 1. 升级状态数据模型
@Data
public class UpgradeStatus {
    private String taskId;
    private boolean completed;
    private int progress;  // 0-100
    private List<UpgradeLog> logs;
    private List<UnresolvedVariable> unresolvedVars;  // 未替换的环境变量
    private List<ErrorLog> errorLogs;                 // 错误日志
    private String result;
    private boolean success;
    private long startTime;
    private long endTime;
}

// 2. 未替换环境变量模型
@Data
public class UnresolvedVariable {
    private String file;         // 所在文件
    private String variable;     // 变量名 ${VARIABLE_NAME}
    private String context;      // 上下文信息，如所在行内容
    private int lineNumber;      // 行号
    private String suggestion;   // 建议的解决方案
}

// 3. 错误日志模型
@Data
public class ErrorLog {
    private String timestamp;    // 错误发生时间
    private String level;        // 错误级别：ERROR, WARN
    private String message;      // 错误信息
    private String stackTrace;   // 堆栈信息
    private String source;       // 错误来源：TEMPLATE, GIT, NACOS, FILE_IO
    private String context;      // 错误上下文
}

// 4. 升级日志模型
@Data
public class UpgradeLog {
    private String timestamp;
    private String level;       // INFO, WARN, ERROR, DEBUG
    private String message;
    private String phase;       // 升级阶段：CLONE, GENERATE, PUBLISH, COMMIT
}

// 5. 升级任务管理器
@Service
public class UpgradeTaskManager {
    private final Map<String, UpgradeTask> tasks = new ConcurrentHashMap<>();
    
    public String createTask(UpgradeRequest request) {
        String taskId = UUID.randomUUID().toString();
        UpgradeTask task = new UpgradeTask(taskId, request);
        tasks.put(taskId, task);
        
        // 异步执行升级任务
        CompletableFuture.runAsync(() -> executeUpgrade(task));
        
        return taskId;
    }
    
    private void executeUpgrade(UpgradeTask task) {
        try {
            task.addLog("INFO", "开始项目升级", "INIT");
            
            // 1. Git克隆阶段
            task.updateProgress(10);
            task.addLog("INFO", "正在克隆项目代码", "CLONE");
            // ... Git操作
            
            // 2. 配置生成阶段  
            task.updateProgress(30);
            task.addLog("INFO", "正在生成配置文件", "GENERATE");
            
            // 在这个阶段收集未替换的环境变量
            List<UnresolvedVariable> unresolvedVars = collectUnresolvedVariables(task);
            task.setUnresolvedVars(unresolvedVars);
            
            if (!unresolvedVars.isEmpty()) {
                task.addLog("WARN", 
                    String.format("发现%d个未替换的环境变量", unresolvedVars.size()), 
                    "GENERATE");
            }
            
            // 3. Nacos发布阶段
            if (task.getRequest().getPublish().equals("true")) {
                task.updateProgress(70);
                task.addLog("INFO", "正在发布配置到Nacos", "PUBLISH");
                // ... Nacos发布操作
            }
            
            // 4. Git提交阶段
            task.updateProgress(90);
            task.addLog("INFO", "正在提交代码变更", "COMMIT");
            // ... Git提交操作
            
            task.updateProgress(100);
            task.complete(true, "升级完成");
            task.addLog("INFO", "项目升级成功完成", "COMPLETE");
            
        } catch (Exception e) {
            task.addErrorLog("ERROR", e.getMessage(), getStackTrace(e), "EXECUTION");
            task.complete(false, "升级失败: " + e.getMessage());
            task.addLog("ERROR", "升级失败: " + e.getMessage(), "FAILED");
        }
    }
    
    // 收集未替换的环境变量
    private List<UnresolvedVariable> collectUnresolvedVariables(UpgradeTask task) {
        List<UnresolvedVariable> unresolvedVars = new ArrayList<>();
        
        try {
            // 遍历生成的配置文件，查找未替换的变量
            Path projectPath = task.getProjectPath();
            Path resourcesPath = projectPath.resolve("src/main/resources");
            
            if (Files.exists(resourcesPath)) {
                Files.walk(resourcesPath)
                    .filter(path -> path.toString().endsWith(".properties") || 
                                   path.toString().endsWith(".yml"))
                    .forEach(file -> {
                        try {
                            List<String> lines = Files.readAllLines(file);
                            for (int i = 0; i < lines.size(); i++) {
                                String line = lines.get(i);
                                // 查找 ${...} 模式的未替换变量
                                Pattern pattern = Pattern.compile("\\$\\{([^}]+)\\}");
                                Matcher matcher = pattern.matcher(line);
                                
                                while (matcher.find()) {
                                    String variable = matcher.group(0);
                                    String varName = matcher.group(1);
                                    
                                    UnresolvedVariable unresolvedVar = new UnresolvedVariable();
                                    unresolvedVar.setFile(file.getFileName().toString());
                                    unresolvedVar.setVariable(variable);
                                    unresolvedVar.setContext(line.trim());
                                    unresolvedVar.setLineNumber(i + 1);
                                    unresolvedVar.setSuggestion(generateSuggestion(varName));
                                    
                                    unresolvedVars.add(unresolvedVar);
                                    
                                    // 记录日志
                                    task.addLog("WARN", 
                                        String.format("未替换变量: %s (文件: %s, 行: %d)", 
                                                    variable, file.getFileName(), i + 1), 
                                        "GENERATE");
                                }
                            }
                        } catch (IOException e) {
                            task.addErrorLog("ERROR", 
                                "读取文件失败: " + file.getFileName(), 
                                e.getMessage(), 
                                "FILE_IO");
                        }
                    });
            }
            
        } catch (Exception e) {
            task.addErrorLog("ERROR", "收集环境变量时发生错误", getStackTrace(e), "VARIABLE_COLLECTION");
        }
        
        return unresolvedVars;
    }
    
    // 生成变量替换建议
    private String generateSuggestion(String varName) {
        // 基于变量名生成建议
        Map<String, String> suggestions = Map.of(
            "NACOS_ADDR", "请在环境变量中设置Nacos服务器地址",
            "NACOS_NAMESPACE", "请在环境变量中设置Nacos命名空间",
            "NACOS_ACCESS_KEY", "请在环境变量中设置Nacos访问密钥",
            "NACOS_SECRET_KEY", "请在环境变量中设置Nacos访问密钥Secret",
            "GITLAB_TOKEN", "请在环境变量中设置GitLab访问令牌"
        );
        
        return suggestions.getOrDefault(varName, 
            "请检查环境变量配置或在模板中提供默认值");
    }
}
```

**4.2 统一响应格式**
```java
public class ApiResponse<T> {
    private boolean success;
    private String message;
    private T data;
    private String errorCode;
    private long timestamp;
}
```

**4.3 异常处理**
- 全局异常处理器
- 统一错误码定义
- 详细的错误信息

### 4.5 阶段五：Web控制台开发（2-3周）

**4.5.1 前端基础架构搭建（2天）**
```bash
# 1. 创建原生HTML+CSS项目
mkdir dm-auto-utils-web
cd dm-auto-utils-web

# 2. 项目结构
dm-auto-utils-web/
├── index.html                # 主页面
├── css/
│   ├── bootstrap.min.css     # Bootstrap CSS
│   ├── bootstrap-icons.css   # Bootstrap Icons
│   └── app.css               # 自定义样式
├── js/
│   ├── bootstrap.bundle.min.js # Bootstrap JS
│   ├── axios.min.js          # HTTP请求库
│   ├── app.js                # 主应用逻辑
│   ├── api.js                # API调用封装
│   └── components/
│       ├── searchSelect.js   # 搜索下拉框组件
│       └── booleanSelect.js  # 布尔值下拉框组件
├── pages/
│   ├── nacos-env.html        # Nacos环境管理
│   ├── nacos-config.html     # Nacos配置管理
│   └── project-upgrade.html  # 项目升级
└── templates/                # HTML模板片段
    ├── sidebar.html
    └── header.html
```

**3. 基础依赖引入**
```html
<!-- CDN方式引入Bootstrap和其他依赖 -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/axios@1.4.0/dist/axios.min.js"></script>
```

**4.5.2 创建核心组件（3天）**
```javascript
// searchSelect.js - 支持搜索的下拉选择器组件
class SearchSelect {
  constructor(elementId, options = {}) {
    this.element = document.getElementById(elementId);
    this.placeholder = options.placeholder || '请选择';
    this.apiUrl = options.apiUrl;
    this.staticOptions = options.staticOptions || [];
    this.onSelect = options.onSelect || (() => {});
    this.searchDelay = 300;
    this.searchTimer = null;
    
    this.init();
  }
  
  init() {
    // 创建Bootstrap下拉框结构
    this.element.innerHTML = `
      <div class="dropdown">
        <input type="text" class="form-control dropdown-toggle" 
               placeholder="${this.placeholder}" 
               data-bs-toggle="dropdown" 
               data-bs-auto-close="outside">
        <ul class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto;">
          <li><span class="dropdown-item-text text-muted">搜索中...</span></li>
        </ul>
      </div>
    `;
    
    this.input = this.element.querySelector('input');
    this.dropdown = this.element.querySelector('.dropdown-menu');
    
    // 绑定事件
    this.input.addEventListener('input', this.handleInput.bind(this));
    this.dropdown.addEventListener('click', this.handleSelect.bind(this));
    
    // 初始加载数据
    this.loadOptions();
  }
  
  handleInput(event) {
    const searchText = event.target.value;
    clearTimeout(this.searchTimer);
    
    this.searchTimer = setTimeout(() => {
      this.loadOptions(searchText);
    }, this.searchDelay);
  }
  
  async loadOptions(searchText = '') {
    try {
      let options = [];
      
      if (this.apiUrl) {
        // 从API加载数据
        const response = await axios.get(this.apiUrl, {
          params: { search: searchText }
        });
        options = response.data.data || [];
      } else {
        // 使用静态数据
        options = this.staticOptions.filter(option => 
          option.label.toLowerCase().includes(searchText.toLowerCase())
        );
      }
      
      this.renderOptions(options);
    } catch (error) {
      console.error('加载选项失败:', error);
      this.dropdown.innerHTML = '<li><span class="dropdown-item-text text-danger">加载失败</span></li>';
    }
  }
  
  renderOptions(options) {
    if (options.length === 0) {
      this.dropdown.innerHTML = '<li><span class="dropdown-item-text text-muted">无匹配项</span></li>';
      return;
    }
    
    const html = options.map(option => 
      `<li><a class="dropdown-item" href="#" data-value="${option.value}">${option.label}</a></li>`
    ).join('');
    
    this.dropdown.innerHTML = html;
  }
  
  handleSelect(event) {
    if (event.target.classList.contains('dropdown-item')) {
      event.preventDefault();
      const value = event.target.dataset.value;
      const label = event.target.textContent;
      
      this.input.value = label;
      this.input.dataset.value = value;
      
      // 关闭下拉框
      bootstrap.Dropdown.getInstance(this.element.querySelector('[data-bs-toggle="dropdown"]'))?.hide();
      
      this.onSelect(value, label);
    }
  }
  
  getValue() {
    return this.input.dataset.value || '';
  }
  
  setValue(value, label) {
    this.input.value = label;
    this.input.dataset.value = value;
  }
}

// booleanSelect.js - 布尔值选择器组件
class BooleanSelect {
  constructor(elementId, options = {}) {
    this.element = document.getElementById(elementId);
    this.defaultValue = options.defaultValue || 'false';
    this.onChange = options.onChange || (() => {});
    
    this.init();
  }
  
  init() {
    this.element.innerHTML = `
      <select class="form-select">
        <option value="false">否</option>
        <option value="true">是</option>
      </select>
    `;
    
    this.select = this.element.querySelector('select');
    this.select.value = this.defaultValue;
    
    this.select.addEventListener('change', (event) => {
      this.onChange(event.target.value);
    });
  }
  
  getValue() {
    return this.select.value;
  }
  
  setValue(value) {
    this.select.value = value;
  }
}

// 对应原有的Provider功能
// GitLabServiceNameProvider -> SearchSelect with apiUrl: '/api/v1/nacos/services'
// EnvNameProvider -> SearchSelect with staticOptions: environments
// BooleanProvider -> BooleanSelect
```

**5.2 页面功能模块**

**Nacos环境管理页面**
- Nacos环境列表展示（dev/qa/uat/prd）
- 环境配置编辑和验证
- 环境连接状态检测
- 环境切换下拉选择器（支持搜索）

**Nacos配置管理页面**
- 配置列表查看和搜索
- 配置内容在线编辑器
- 配置发布功能（包含确认对话框）
- 配置差异对比工具
- 服务名称下拉选择器（支持搜索）
- 命名空间下拉选择器（支持搜索）

**项目升级页面**  
- 项目选择下拉器（支持搜索GitLab项目）
- 环境选择下拉器（dev/qa/uat/prd）
- 刷新配置下拉选择（true/false）
- 发布到Nacos下拉选择（true/false）
- 注册中心配置下拉选择（true/false）
- 升级进度实时展示
- 升级结果详情查看
- 升级历史记录管理

**4.5.3 项目升级页面实现（5天）**
```html
<!-- project-upgrade.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目升级 - DM Auto Utils</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="../css/app.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <h2 class="mb-4">项目升级配置</h2>
                
                <!-- 升级配置表单 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="bi bi-gear-fill"></i> 配置升级参数
                    </div>
                    <div class="card-body">
                        <form id="upgradeForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">服务名称 *</label>
                                        <div id="serviceNameSelect"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">目标环境 *</label>
                                        <div id="envSelect"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">本地路径</label>
                                        <input type="text" class="form-control" id="localPath" 
                                               placeholder="留空则自动从GitLab克隆">
                                        <div class="form-text">如果指定本地路径，将直接使用本地代码</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">分支名称</label>
                                        <input type="text" class="form-control" id="branchName" 
                                               value="nacos-upgrade" placeholder="默认: nacos-upgrade">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">启用刷新</label>
                                        <div id="refreshSelect"></div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">发布到Nacos</label>
                                        <div id="publishSelect"></div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">注册中心</label>
                                        <div id="registerSelect"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-primary" id="startUpgradeBtn">
                                    <i class="bi bi-upload"></i> 开始升级
                                </button>
                                <button type="button" class="btn btn-secondary" id="resetFormBtn">
                                    <i class="bi bi-arrow-clockwise"></i> 重置
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- 升级进度 -->
                <div class="card mb-4" id="progressCard" style="display: none;">
                    <div class="card-header">
                        <i class="bi bi-hourglass-split"></i> 升级进度
                    </div>
                    <div class="card-body">
                        <div class="progress mb-3" style="height: 25px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 id="progressBar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="progressLogs" class="border rounded p-3" style="height: 300px; overflow-y: auto;">
                            <!-- 动态加载进度日志 -->
                        </div>
                    </div>
                </div>
                
                <!-- 未替换环境变量展示 -->
                <div class="card mb-4" id="unresolvedVarsCard" style="display: none;">
                    <div class="card-header bg-warning text-dark">
                        <i class="bi bi-exclamation-triangle-fill"></i> 未替换的环境变量
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning" role="alert">
                            <strong>注意：</strong>以下环境变量在配置生成过程中未能替换，需要手动处理
                        </div>
                        <div id="unresolvedVarsList">
                            <!-- 动态加载未替换的环境变量 -->
                        </div>
                    </div>
                </div>
                
                <!-- 错误日志展示 -->
                <div class="card mb-4" id="errorLogsCard" style="display: none;">
                    <div class="card-header bg-danger text-white">
                        <i class="bi bi-bug-fill"></i> 错误日志
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger" role="alert">
                            <strong>错误：</strong>升级过程中发生了以下错误
                        </div>
                        <div id="errorLogsList">
                            <!-- 动态加载错误日志 -->
                        </div>
                    </div>
                </div>
                
                <!-- 升级结果 -->
                <div class="card" id="resultCard" style="display: none;">
                    <div class="card-header">
                        <i class="bi bi-check-circle-fill"></i> 升级结果
                    </div>
                    <div class="card-body text-center">
                        <div id="resultContent">
                            <!-- 动态加载升级结果 -->
                        </div>
                        <div class="mt-3">
                            <button type="button" class="btn btn-primary" id="viewDetailsBtn">
                                <i class="bi bi-eye"></i> 查看详情
                            </button>
                            <button type="button" class="btn btn-success" id="newUpgradeBtn">
                                <i class="bi bi-plus-circle"></i> 新建升级
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@1.4.0/dist/axios.min.js"></script>
    <script src="../js/components/searchSelect.js"></script>
    <script src="../js/components/booleanSelect.js"></script>
    <script src="../js/project-upgrade.js"></script>
</body>
</html>
```

```javascript
// project-upgrade.js - 项目升级页面逻辑
class ProjectUpgrade {
    constructor() {
        this.upgradeForm = {
            serviceName: '',
            env: '',
            path: '',
            branchName: 'nacos-upgrade',
            refresh: 'false',
            publish: 'false',
            register: 'false'
        };
        
        this.isUpgrading = false;
        this.upgradeTaskId = null;
        
        this.init();
    }
    
    init() {
        this.initComponents();
        this.bindEvents();
    }
    
    initComponents() {
        // 初始化搜索选择组件
        this.serviceNameSelect = new SearchSelect('serviceNameSelect', {
            placeholder: '请选择或输入服务名称',
            apiUrl: '/api/v1/nacos/services',
            onSelect: (value) => this.upgradeForm.serviceName = value
        });
        
        this.envSelect = new SearchSelect('envSelect', {
            placeholder: '请选择环境',
            staticOptions: [
                { label: '开发环境', value: 'dev' },
                { label: '测试环境', value: 'qa' },
                { label: '预发环境', value: 'uat' },
                { label: '生产环境', value: 'prd' }
            ],
            onSelect: (value) => this.upgradeForm.env = value
        });
        
        // 初始化布尔选择组件
        this.refreshSelect = new BooleanSelect('refreshSelect', {
            defaultValue: 'false',
            onChange: (value) => this.upgradeForm.refresh = value
        });
        
        this.publishSelect = new BooleanSelect('publishSelect', {
            defaultValue: 'false',
            onChange: (value) => this.upgradeForm.publish = value
        });
        
        this.registerSelect = new BooleanSelect('registerSelect', {
            defaultValue: 'false',
            onChange: (value) => this.upgradeForm.register = value
        });
    }
    
    bindEvents() {
        document.getElementById('startUpgradeBtn').addEventListener('click', () => this.startUpgrade());
        document.getElementById('resetFormBtn').addEventListener('click', () => this.resetForm());
        document.getElementById('newUpgradeBtn').addEventListener('click', () => this.startNewUpgrade());
        document.getElementById('viewDetailsBtn').addEventListener('click', () => this.viewDetails());
    }
    
    async startUpgrade() {
        if (!this.validateForm()) {
            return;
        }
        
        try {
            this.isUpgrading = true;
            this.updateUpgradeButton(true);
            this.showProgress();
            
            // 收集表单数据
            this.upgradeForm.path = document.getElementById('localPath').value;
            this.upgradeForm.branchName = document.getElementById('branchName').value;
            
            // 发起升级请求
            const response = await axios.post('/api/v1/nacos/configs/upgrade', this.upgradeForm);
            
            if (response.data.success) {
                this.upgradeTaskId = response.data.data.taskId;
                this.pollUpgradeStatus();
            } else {
                throw new Error(response.data.message);
            }
            
        } catch (error) {
            this.handleUpgradeError(error);
        }
    }
    
    async pollUpgradeStatus() {
        if (!this.upgradeTaskId) return;
        
        try {
            const response = await axios.get(`/api/v1/nacos/configs/upgrade/status/${this.upgradeTaskId}`);
            const status = response.data.data;
            
            // 更新进度条
            this.updateProgress(status.progress);
            
            // 更新日志
            this.appendProgressLogs(status.logs);
            
            // 显示未替换的环境变量
            if (status.unresolvedVars && status.unresolvedVars.length > 0) {
                this.showUnresolvedVars(status.unresolvedVars);
            }
            
            // 显示错误日志
            if (status.errorLogs && status.errorLogs.length > 0) {
                this.showErrorLogs(status.errorLogs);
            }
            
            if (status.completed) {
                this.handleUpgradeComplete(status);
            } else {
                // 继续轮询
                setTimeout(() => this.pollUpgradeStatus(), 2000);
            }
            
        } catch (error) {
            console.error('轮询升级状态失败:', error);
            setTimeout(() => this.pollUpgradeStatus(), 5000);
        }
    }
    
    showUnresolvedVars(unresolvedVars) {
        const card = document.getElementById('unresolvedVarsCard');
        const list = document.getElementById('unresolvedVarsList');
        
        const html = unresolvedVars.map(item => `
            <div class="alert alert-warning" role="alert">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>文件:</strong> ${item.file}<br>
                        <strong>变量:</strong> <code>${item.variable}</code><br>
                        <strong>上下文:</strong> <small class="text-muted">${item.context}</small>
                    </div>
                    <div>
                        <span class="badge bg-warning">未解析</span>
                    </div>
                </div>
            </div>
        `).join('');
        
        list.innerHTML = html;
        card.style.display = 'block';
    }
    
    showErrorLogs(errorLogs) {
        const card = document.getElementById('errorLogsCard');
        const list = document.getElementById('errorLogsList');
        
        const html = errorLogs.map(error => `
            <div class="alert alert-danger" role="alert">
                <div class="d-flex justify-content-between align-items-start">
                    <div style="flex-grow: 1;">
                        <strong>时间:</strong> ${error.timestamp}<br>
                        <strong>错误:</strong> ${error.message}<br>
                        ${error.stackTrace ? `<strong>堆栈:</strong><br><pre style="font-size: 0.8em; max-height: 100px; overflow-y: auto;">${error.stackTrace}</pre>` : ''}
                    </div>
                    <span class="badge bg-danger">错误</span>
                </div>
            </div>
        `).join('');
        
        list.innerHTML = html;
        card.style.display = 'block';
    }
    
    updateProgress(percentage) {
        const progressBar = document.getElementById('progressBar');
        progressBar.style.width = percentage + '%';
        progressBar.textContent = percentage + '%';
    }
    
    appendProgressLogs(logs) {
        const container = document.getElementById('progressLogs');
        
        logs.forEach(log => {
            const logElement = document.createElement('div');
            logElement.className = `alert alert-${this.getLogTypeClass(log.level)} py-1 mb-1`;
            logElement.innerHTML = `
                <small class="text-muted">${log.timestamp}</small>
                <span class="ms-2">${log.message}</span>
            `;
            container.appendChild(logElement);
        });
        
        // 自动滚动到底部
        container.scrollTop = container.scrollHeight;
    }
    
    getLogTypeClass(level) {
        const mapping = {
            'INFO': 'info',
            'WARN': 'warning', 
            'ERROR': 'danger',
            'DEBUG': 'secondary'
        };
        return mapping[level] || 'light';
    }
    
    validateForm() {
        if (!this.upgradeForm.serviceName) {
            alert('请选择服务名称');
            return false;
        }
        if (!this.upgradeForm.env) {
            alert('请选择目标环境');
            return false;
        }
        return true;
    }
    
    // ... 其他方法
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new ProjectUpgrade();
});
```

**5.3 页面设计**
```
┌─────────────────────────────────────────────────────────┐
│  Header: 导航栏 + 用户信息 + 环境选择器                    │
├─────────────────────────────────────────────────────────┤
│ │ Sidebar │         Main Content Area                   │
│ │ - Nacos环境 │  ┌─────────────────────────────────────┐    │
│ │ - Nacos配置 │  │                                     │    │
│ │ - 项目升级  │  │     功能页面内容                     │    │
│ │ - 操作历史  │  │   (支持下拉框搜索和布尔选择)          │    │
│ │ - 系统设置  │  │                                     │    │
│ │           │  └─────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────┘
```

**界面组件设计要求**：
- 所有原生tab提示改为下拉框搜索
- true/false选项使用下拉框选择
- 服务名称支持自动补全和搜索
- 环境选择器支持快速切换
- 操作结果实时反馈

### 4.6 阶段六：数据持久化与优化（1周）

**6.1 操作历史记录**
- 升级操作历史
- 配置变更记录
- 错误日志存储

**6.2 缓存优化**
- 配置信息缓存
- Git仓库信息缓存
- 模板编译缓存

**6.3 异步处理**
- 长时间操作异步化
- 操作进度通知
- 任务队列管理

## 五、技术实现细节

### 5.1 JGit替换GitLab4j-api

**原有实现**:
```java
// 使用gitlab4j-api
GitLabApi gitLabApi = new GitLabApi(gitlabUrl, gitlabToken);
Project project = gitLabApi.getProjectApi().getProject(projectId);
```

**新实现**:
```java
// 使用JGit
@Service
public class JGitOperationService implements GitOperationService {
    
    @Override
    public GitRepository clone(GitCloneRequest request) {
        Git git = Git.cloneRepository()
            .setURI(request.getRepositoryUrl())
            .setDirectory(request.getLocalPath())
            .setCredentialsProvider(createCredentialsProvider())
            .call();
        return new GitRepository(git);
    }
    
    private CredentialsProvider createCredentialsProvider() {
        return new UsernamePasswordCredentialsProvider(
            "PRIVATE-TOKEN", gitConfigProperties.getToken());
    }
}
```

### 5.2 配置模板工厂

**模板定义**:
```java
// application-nacos.properties.ftl
spring.application.name=${serviceName}
spring.profiles.active=${profilesActive}

<#if refresh == "true">
spring.cloud.nacos.config.refresh-enabled=true
</#if>

<#if register == "true">
spring.cloud.nacos.discovery.server-addr=${nacosServerAddr}
spring.cloud.nacos.discovery.namespace=${nacosNamespace}
</#if>
```

**模板使用**:
```java
@Service
public class ConfigGenerationService {
    
    @Autowired
    private TemplateFactory templateFactory;
    
    public String generateApplicationConfig(GenerationContext context) {
        ConfigTemplate template = templateFactory.createTemplate(
            TemplateType.APPLICATION_NACOS);
        return template.generate(context);
    }
}
```

### 5.3 文件操作统一化

```java
@Service
public class FileOperationServiceImpl implements FileOperationService {
    
    @Override
    public FileContent readFile(String path) {
        try {
            Path filePath = Paths.get(path);
            String content = Files.readString(filePath, StandardCharsets.UTF_8);
            return FileContent.builder()
                .content(content)
                .lastModified(Files.getLastModifiedTime(filePath).toInstant())
                .size(Files.size(filePath))
                .build();
        } catch (IOException e) {
            throw new FileOperationException("读取文件失败: " + path, e);
        }
    }
    
    @Override
    public FileWriteResult writeFile(String path, String content) {
        try {
            Path filePath = Paths.get(path);
            // 确保目录存在
            Files.createDirectories(filePath.getParent());
            // 备份原文件
            FileBackup backup = backup(path);
            // 写入新内容
            Files.writeString(filePath, content, StandardCharsets.UTF_8);
            
            return FileWriteResult.builder()
                .success(true)
                .backup(backup)
                .writtenBytes(content.getBytes(StandardCharsets.UTF_8).length)
                .build();
        } catch (IOException e) {
            throw new FileOperationException("写入文件失败: " + path, e);
        }
    }
}
```

## 六、配置管理方案

### 6.1 多环境配置文件

**主配置文件 application.yml**:
```yaml
spring:
  application:
    name: dm-auto-utils
  profiles:
    active: dev

dm:
  auto:
    git:
      timeout: 30000
      retry-count: 3
    nacos:
      timeout: 5000
      connection-pool-size: 10
    file:
      backup-enabled: true
      backup-directory: "${user.home}/.dm-auto-utils/backups"
```

**环境特定配置 environments.yml**:
```yaml
# Git配置（全局唯一）
git:
  baseUrl: "http://172.16.100.23"
  token: "${GITLAB_TOKEN}"
  defaultBranch: "master"
  upgradePrefix: "auto-upgrade-"

# Nacos多环境配置
nacos:
  environments:
    dev:
      server: "mse-4d0af020-nacos-ans.mse.aliyuncs.com:8848"
      instanceId: "mse_regserverless_cn-3mp3ys14x01"
      accessKey: "${NACOS_ACCESS_KEY}"
      secretKey: "${NACOS_SECRET_KEY}"
      namespaces:
        - name: "dev"
          id: "dev"
        - name: "test"
          id: "test"
    qa:
      server: "mse-4d0af020-nacos-ans.mse.aliyuncs.com:8848"
      instanceId: "mse_regserverless_cn-3mp3ys14x01"
      accessKey: "${NACOS_ACCESS_KEY}"
      secretKey: "${NACOS_SECRET_KEY}"
      namespaces:
        - name: "qa"
          id: "qa"
    uat:
      server: "mse-f3de5d70-nacos-ans.mse.aliyuncs.com:8848"
      instanceId: "mse_prepaid_public_cn-k963wd2yi02"
      accessKey: "${NACOS_ACCESS_KEY}"
      secretKey: "${NACOS_SECRET_KEY}"
      namespaces:
        - name: "uat"
          id: "uat"
    prd:
      server: "mse-2e1b1f50-nacos-ans.mse.aliyuncs.com:8848"
      instanceId: "mse_prepaid_public_cn-1ls3wd2yu01"
      accessKey: "${NACOS_ACCESS_KEY}"
      secretKey: "${NACOS_SECRET_KEY}"
      namespaces:
        - name: "prd"
          id: "prd"
```

### 6.2 配置加载机制

```java
@ConfigurationProperties(prefix = "dm.auto.environments")
@Component
public class EnvironmentProperties {
    private Map<String, EnvironmentConfig> environments = new HashMap<>();
    
    // getters and setters
}

@Configuration
public class EnvironmentConfigurationLoader {
    
    @Bean
    @Primary
    public EnvironmentManager environmentManager(
            EnvironmentProperties properties) {
        return new EnvironmentManagerImpl(properties);
    }
}
```

## 七、测试策略

### 7.1 单元测试
- 每个服务类的单元测试
- Mock外部依赖
- 测试覆盖率 >= 80%

### 7.2 集成测试
- API接口集成测试
- 数据库集成测试
- 外部服务集成测试

### 7.3 端到端测试
- 完整业务流程测试
- Web界面自动化测试
- 性能测试

## 八、部署方案

### 8.1 Docker化部署
```dockerfile
FROM openjdk:8-jre-slim

WORKDIR /app
COPY target/dm-auto-utils.jar app.jar
COPY environments.yml environments.yml

EXPOSE 8080

CMD ["java", "-jar", "app.jar"]
```

### 8.2 配置外部化
- 使用环境变量注入敏感信息
- 配置文件挂载
- 支持配置热更新

## 九、风险评估与缓解

### 9.1 技术风险
**风险**: JGit替换可能导致功能缺失  
**缓解**: 分步骤替换，保留原有实现作为备选

**风险**: 大规模重构可能引入新的Bug  
**缓解**: 完善的测试覆盖，分阶段发布

### 9.2 业务风险
**风险**: 重构期间影响现有功能使用  
**缓解**: 保持API向后兼容，逐步迁移

**风险**: 用户界面变更影响用户体验  
**缓解**: 提供培训文档，保留命令行接口

## 十、详细时间计划

| 阶段 | 子任务 | 预计时间 | 主要产出 |
|------|--------|----------|----------|
| **准备阶段** | 代码备份和分析 | 1天 | 重构分支、代码分析报告 |
| **阶段一** | 文件操作工具类 | 2天 | FileOperationService |
|  | JGit实现 | 3天 | JGitOperationServiceImpl |
|  | 配置外部化 | 2天 | NacosConfig重构、environments.yml |
| **阶段二** | 配置管理服务 | 3天 | ConfigurationManager |
|  | 配置验证和加载 | 2天 | 配置热加载机制 |
| **阶段三** | 模板逻辑抽离 | 3天 | ConfigTemplate接口和实现 |
|  | 工厂类重构 | 2天 | ConfigurationWriterFactory增强 |
|  | FreeMarker模板 | 2天 | bootstrap.ftl、application.ftl |
| **阶段四** | REST API转换 | 5天 | NacosConfigController |
|  | DTO对象创建 | 2天 | 请求响应对象 |
|  | 异步任务和环境变量收集 | 5天 | 异步升级、环境变量收集、错误日志 |
| **阶段五** | 前端架构搭建 | 2天 | 原生HTML+Bootstrap项目结构 |
|  | 核心组件开发 | 3天 | SearchSelect、BooleanSelect组件 |
|  | 页面实现和集成 | 5天 | 三个主要页面及环境变量展示 |
| **阶段六** | 测试和优化 | 4天 | 单元测试、集成测试 |
|  | 部署和文档 | 3天 | Docker部署、使用文档 |
| **总计** | **11个阶段** | **51天(约10-11周)** | **完整的重构版本** |

### 关键里程碑

- **第1周末**：基础设施重构完成，JGit替换完成
- **第3周末**：模板工厂实现完成，配置生成优化
- **第6周末**：REST API完成，后端功能齐全
- **第10周末**：Web控制台完成，用户界面可用
- **第11周末**：测试完成，正式发布

### 风险缓解时间预留

每个阶段预留10%的时间缓冲，确保在遇到技术难点时有足够时间解决。

## 重点功能实现

### 1. 环境变量收集功能
- **扫描范围**：自动扫描生成的配置文件（.properties、.yml）
- **识别模式**：识别`${VARIABLE_NAME}`格式的未替换变量
- **详细信息**：提供文件名、行号、上下文和解决建议
- **实时展示**：在Web界面中实时显示收集到的未替换变量

### 2. 错误日志收集功能
- **错误分类**：按照来源分类（TEMPLATE、GIT、NACOS、FILE_IO）
- **详细信息**：包含时间戳、错误级别、堆栈信息
- **实时展示**：在Web界面中实时显示错误日志
- **持久化**：错误日志持久化存储，便于问题排查

### 3. 前端技术调整
- **框架选择**：使用原生HTML+CSS+Bootstrap替代Vue.js
- **组件化**：通过JavaScript类实现组件化开发
- **搜索功能**：所有原有Provider改为支持搜索的下拉框
- **用户体验**：实时进度展示、异步操作、错误提示

## 十一、成功标准

### 11.1 功能标准
- [ ] 完全移除gitlab4j-api依赖，使用纯JGit实现
- [ ] 提供完整的REST API接口
- [ ] 提供友好的Web管理界面
- [ ] 支持环境配置文件化管理
- [ ] 实现模板工厂模式
- [ ] 统一文件操作接口

### 11.2 性能标准
- [ ] API响应时间 < 2秒
- [ ] Git操作超时时间可配置
- [ ] 支持并发操作
- [ ] 内存使用优化

### 11.3 质量标准
- [ ] 单元测试覆盖率 >= 80%
- [ ] 代码质量检查通过
- [ ] 安全扫描无高危漏洞
- [ ] 文档完整性检查通过

---

**文档版本**: 1.0  
**最后更新**: 2025年1月  
**负责人**: 开发团队  
**审核人**: 项目负责人  

## 八、详细模块化架构设计

### 8.1 父级POM配置

**dm-auto-utils/pom.xml**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.just</groupId>
    <artifactId>dm-auto-utils</artifactId>
    <version>2.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>DM Auto Utils - 重构版</name>

    <modules>
        <module>dm-common-core</module>
        <module>dm-nacos-core</module>
        <module>dm-git-core</module>
        <module>dm-template-core</module>
        <module>dm-file-utils</module>
        <module>dm-web-api</module>
        <module>dm-web-console</module>
        <module>dm-application</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <spring.boot.version>2.3.12.RELEASE</spring.boot.version>
        <spring.cloud.alibaba.version>2.2.9.RELEASE</spring.cloud.alibaba.version>
        <nacos.client.version>1.4.2</nacos.client.version>
        <jgit.version>5.13.1.202206130422-r</jgit.version>
        <freemarker.version>2.3.31</freemarker.version>
        <hutool.version>5.8.16</hutool.version>
        <lombok.version>1.18.24</lombok.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot Dependencies -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <!-- 内部模块依赖 -->
            <dependency>
                <groupId>com.just</groupId>
                <artifactId>dm-common-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.just</groupId>
                <artifactId>dm-nacos-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.just</groupId>
                <artifactId>dm-git-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.just</groupId>
                <artifactId>dm-template-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.just</groupId>
                <artifactId>dm-file-utils</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.just</groupId>
                <artifactId>dm-web-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            
            <!-- 第三方依赖 -->
            <dependency>
                <groupId>org.eclipse.jgit</groupId>
                <artifactId>org.eclipse.jgit</artifactId>
                <version>${jgit.version}</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
```

### 8.2 模块依赖关系详细定义

#### 8.2.1 dm-common-core/pom.xml
```xml
<dependencies>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter</artifactId>
    </dependency>
    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <scope>provided</scope>
    </dependency>
    <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-all</artifactId>
    </dependency>
</dependencies>
```

#### 8.2.2 dm-nacos-core/pom.xml  
```xml
<dependencies>
    <dependency>
        <groupId>com.just</groupId>
        <artifactId>dm-common-core</artifactId>
    </dependency>
    <dependency>
        <groupId>com.just</groupId>
        <artifactId>dm-template-core</artifactId>
    </dependency>
    <dependency>
        <groupId>com.just</groupId>
        <artifactId>dm-file-utils</artifactId>
    </dependency>
    <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
    </dependency>
    <dependency>
        <groupId>com.alibaba.nacos</groupId>
        <artifactId>nacos-client</artifactId>
        <version>${nacos.client.version}</version>
    </dependency>
    <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-ooxml</artifactId>
    </dependency>
</dependencies>
```

### 8.3 Nacos升级核心逻辑重构分析

**基于现有代码的精确重构方案**

#### 8.3.1 NacosUpgradeService（重构NacosConfigCommands.generateProps）

```java
@Service
@Slf4j
public class NacosUpgradeService {
    
    private final NacosConfigService nacosConfigService;
    private final ConfigTemplateFactory templateFactory;
    private final EnvironmentVariableCollector variableCollector;
    private final FileOperationService fileOperationService;
    
    /**
     * 核心升级方法 - 基于现有NacosConfigCommands.generateProps重构
     * 完全保持现有逻辑，确保不出错
     */
    public UpgradeResult upgradeNacosConfig(UpgradeRequest request) {
        UpgradeResult result = new UpgradeResult();
        result.setTaskId(UUID.randomUUID().toString());
        result.setStartTime(System.currentTimeMillis());
        
        try {
            log.warn("生成配置属性, 服务: {}, 环境: {}, 路径: {}, 刷新: {}, 推送: {}",
                    request.getServiceName(), request.getEnv(), request.getPath(), 
                    request.getRefresh(), request.getPublish());
            
            // 1. 创建环境配置 - 对应现有EnvironmentConfig
            EnvironmentConfig envConfig = new EnvironmentConfig(request.getEnv());
            
            // 2. 创建Excel配置读取器 - 对应现有ExcelConfigReaderImpl
            ExcelConfigReader configReader = new ExcelConfigReaderImpl(request.getEnv());
            
            // 3. 创建属性处理器 - 对应现有NacosPropertyProcessor
            NacosPropertyProcessor propertyProcessor = new NacosPropertyProcessor(configReader, envConfig);
            
            // 4. 使用工厂创建配置写入器 - 对应现有ConfigurationWriterFactory.createNacosWriter
            ConfigurationWriter configWriter = templateFactory.createNacosWriter(
                request.getServiceName(), 
                request.getPath(), 
                request.getRefresh(), 
                request.getEnv(), 
                request.getRegister()
            );
            
            // 5. 读取配置 - 对应现有configReader.readConfiguration()
            configReader.readConfiguration();
            
            // 6. 生成application-nacos.properties - 对应现有generateApplicationProperties
            String applicationConfig = configWriter.generateApplicationProperties(propertyProcessor);
            result.setApplicationConfig(applicationConfig);
            
            // 7. 如果本地没有application.properties，从MSE获取 - 保持现有逻辑
            if (StringUtils.isBlank(applicationConfig)) {
                log.warn("获取mse application.properties 配置文件");
                nacosConfigService.initialize(request.getEnv());
                String dataId = request.getServiceName() + ".properties";
                applicationConfig = nacosConfigService.getConfig(dataId, request.getServiceName(), 5000);
                
                if (StringUtils.isBlank(applicationConfig)) {
                    // 尝试另一种命名格式 - 保持现有逻辑
                    dataId = request.getServiceName().replace("-", "_") + ".application.properties";
                    applicationConfig = nacosConfigService.getConfig(dataId, request.getServiceName(), 5000);
                }
                result.setApplicationConfig(applicationConfig);
            }
            
            // 8. 生成bootstrap.properties - 对应现有generateBootstrapProperties
            configWriter.generateBootstrapProperties(propertyProcessor);
            log.warn("√ 生成bootstrap.properties配置文件成功");
            
            // 9. 生成其他配置文件 - 对应现有generateOtherProperties
            configWriter.generateOtherProperties(propertyProcessor);
            log.warn("√ 生成其他配置文件成功");
            
            // 10. 升级注册中心配置 - 对应现有upgradeRegisterCenter逻辑
            if ("true".equalsIgnoreCase(request.getRegister()) || 
                "y".equalsIgnoreCase(request.getRegister()) || 
                "yes".equalsIgnoreCase(request.getRegister())) {
                configWriter.upgradeRegisterCenter(applicationConfig);
                log.warn("√ 升级注册中心配置成功");
            }
            
            // 11. 发布到Nacos（如果需要） - 对应现有publishToNacos逻辑
            if (Boolean.parseBoolean(request.getPublish()) || 
                "y".equalsIgnoreCase(request.getPublish()) || 
                "yes".equalsIgnoreCase(request.getPublish())) {
                publishToNacos(configWriter, applicationConfig, request);
                log.warn("√ 配置已推送到Nacos服务器");
            }
            
            // 12. 收集未解析的环境变量 - 新增功能
            List<UnresolvedVariable> unresolvedVars = variableCollector.collectUnresolvedVariables(
                request.getPath(), applicationConfig
            );
            result.setUnresolvedVariables(unresolvedVars);
            
            // 13. 打印未解析环境变量 - 对应现有printUnresolvedEnvVars
            if (propertyProcessor != null) {
                propertyProcessor.printUnresolvedEnvVars();
            }
            
            result.setSuccess(true);
            result.setMessage("配置生成完成，请检查是否存在未解析的服务私有变量");
            result.setEndTime(System.currentTimeMillis());
            
            return result;
            
        } catch (Exception e) {
            log.error("生成配置属性异常: {}", e.getMessage(), e);
            result.setSuccess(false);
            result.setMessage("操作失败: " + e.getMessage());
            result.setErrorDetails(getStackTrace(e));
            result.setEndTime(System.currentTimeMillis());
            return result;
        }
    }
    
    /**
     * 发布配置到Nacos - 基于现有NacosConfigurationWriter.publishToNacos重构
     * 完全保持现有交互逻辑
     */
    private void publishToNacos(ConfigurationWriter configWriter, String content, UpgradeRequest request) 
            throws NacosException {
        
        // 保持现有的生产环境确认逻辑
        nacosConfigService.initialize(request.getEnv());
        String nacosContent = nacosConfigService.getConfig(
            StringUtils.replace(request.getServiceName(), "_", "-") + ".properties", 
            request.getServiceName(), 
            5000
        );
        
        if (nacosContent != null) {
            log.warn("获取到的mse nacos配置内容: {}", nacosContent);
            if ("prd".equals(request.getEnv())) {
                // 在Web环境中，这里需要通过异步方式处理用户确认
                // 暂时跳过交互式确认，由前端页面处理
                log.warn("生产环境配置已存在，需要用户确认");
            }
        }
        
        // 执行发布
        configWriter.publishToNacos(content);
    }
}
```

#### 8.3.2 ConfigTemplateFactory（重构ConfigurationWriterFactory）

```java
@Component
@Slf4j
public class ConfigTemplateFactory implements TemplateFactory {
    
    private final NacosConfigService nacosConfigService;
    private final FileOperationService fileOperationService;
    
    /**
     * 创建Nacos配置写入器 - 对应现有ConfigurationWriterFactory.createNacosWriter
     * 完全保持现有参数和逻辑
     */
    @Override
    public ConfigurationWriter createNacosWriter(String serviceName, String servicePath, 
                                               String refresh, String profilesActive, String register) {
        
        // 使用现有的NacosConfigurationWriter构造逻辑
        return new NacosConfigurationWriter(
            nacosConfigService, 
            serviceName, 
            servicePath, 
            refresh, 
            profilesActive, 
            register
        );
    }
}
```

#### 8.3.3 NacosConfigurationWriter（迁移现有类）

```java
@Slf4j
public class NacosConfigurationWriter implements ConfigurationWriter {
    
    private final String serviceName;
    private final String refresh;
    private final String servicePath;
    private final String profilesActive;
    private final boolean register;
    private final NacosConfigService nacosConfigService;
    private final FileOperationService fileOperationService;

    public NacosConfigurationWriter(NacosConfigService nacosConfigService, 
                                  String serviceName, String servicePath, 
                                  String refresh, String profilesActive, String register) {
        this.nacosConfigService = nacosConfigService;
        this.serviceName = serviceName;
        this.refresh = refresh;
        this.servicePath = servicePath;
        this.profilesActive = profilesActive;
        this.register = "true".equalsIgnoreCase(register) || 
                       "y".equalsIgnoreCase(register) || 
                       "yes".equalsIgnoreCase(register);
    }

    /**
     * 生成application-nacos.properties - 完全保持现有逻辑
     */
    @Override
    public String generateApplicationProperties(PropertyProcessor processor) {
        try {
            // 在resources目录下查找application.properties - 保持现有逻辑
            Path resourcesPath = Paths.get(servicePath, "src", "main", "resources");
            Path applicationPropertiesPath = fileOperationService.findFile(resourcesPath, "application.properties");
            
            if (applicationPropertiesPath == null) {
                return null;
            }
            
            String newPropertiesPath = applicationPropertiesPath.getParent().toString() + 
                                     File.separator + "application-nacos.properties";
                                     
            // 读取原文件 - 使用FileOperationService替代直接文件操作
            List<String> lines = fileOperationService.readLines(applicationPropertiesPath);
            
            // 处理属性 - 保持现有逻辑
            List<String> newLines = processor.processProperties(lines);
            
            // 写入新文件 - 使用FileOperationService
            fileOperationService.writeLines(Paths.get(newPropertiesPath), newLines);
            
            log.warn("√  生成【application-nacos.properties】配置文件成功");
            log.warn("> {}", newPropertiesPath);

            // 返回内容 - 保持现有逻辑
            StringBuilder sb = new StringBuilder();
            for (String line : newLines) {
                sb.append(line).append("\n");
            }
            return sb.toString();
            
        } catch (IOException e) {
            log.error("生成配置文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成配置文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成bootstrap.properties - 完全保持现有逻辑
     */
    @Override
    public void generateBootstrapProperties(PropertyProcessor processor) {
        try {
            Path resourcesPath = Paths.get(servicePath, "src", "main", "resources");
            Path applicationPropertiesPath = fileOperationService.findFile(resourcesPath, "application.properties");
            
            if (applicationPropertiesPath == null) {
                applicationPropertiesPath = fileOperationService.findFile(resourcesPath, "bootstrap.properties");
                if (applicationPropertiesPath == null) {
                    log.error("× 未找到application.properties, bootstrap.properties 文件");
                    throw new RuntimeException("升级失败：未找到配置文件");
                }
                
                // 处理已存在bootstrap.properties的情况 - 保持现有逻辑
                List<String> lines = fileOperationService.readLines(applicationPropertiesPath);
                if (register) {
                    lines.add("# nacos register center property");
                    lines.add("spring.cloud.nacos.discovery.namespace=${NACOS_NAMESPACE}");
                    lines.add("spring.cloud.nacos.discovery.access-key=${NACOS_ACCESS_KEY}");
                    lines.add("spring.cloud.nacos.discovery.secret-key=${NACOS_SECRET_KEY}");
                }
                log.warn("√  注册中心添加【bootstrap.properties】配置文件成功");
                return;
            }

            String bootstrapPropertiesPath = applicationPropertiesPath.getParent().toString() + 
                                           File.separator + "bootstrap.properties";
            List<String> newLines = generateBootstrapContent(processor);

            fileOperationService.writeLines(Paths.get(bootstrapPropertiesPath), newLines);
            log.warn("√  生成【bootstrap.properties】配置文件成功");
            log.warn("> {}", bootstrapPropertiesPath);
            
        } catch (IOException e) {
            log.error("生成bootstrap.properties配置文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("× 生成bootstrap.properties配置文件失败", e);
        }
    }

    /**
     * 生成bootstrap配置内容 - 完全保持现有逻辑
     */
    private List<String> generateBootstrapContent(PropertyProcessor processor) {
        List<String> newLines = new ArrayList<>();

        // 添加nacos基础配置 - 保持现有逻辑
        newLines.add("spring.application.name=" + serviceName);
        newLines.add("# dev nacos property");
        newLines.add("#spring.cloud.nacos.config.server-addr=${NACOS_ADDR:mse-4d0af020-nacos-ans.mse.aliyuncs.com:8848}");
        newLines.add("#spring.cloud.nacos.config.namespace=${NACOS_NAMESPACE:dev}");
        newLines.add("#spring.cloud.nacos.config.access-key=${NACOS_ACCESS_KEY:LTAI5tHaMNaoAGRzMwtMgjMN}");
        newLines.add("#spring.cloud.nacos.config.secret-key=${NACOS_SECRET_KEY:******************************}");

        newLines.add("# prd nacos property");
        newLines.add("spring.cloud.nacos.config.server-addr=${NACOS_ADDR}");
        newLines.add("spring.cloud.nacos.config.namespace=${NACOS_NAMESPACE}");
        newLines.add("spring.cloud.nacos.config.access-key=${NACOS_ACCESS_KEY}");
        newLines.add("spring.cloud.nacos.config.secret-key=${NACOS_SECRET_KEY}");

        // 添加共享配置 - 保持现有逻辑
        if (processor instanceof NacosPropertyProcessor) {
            Set<String> usePropertiesFileName = ((NacosPropertyProcessor) processor).getUsePropertiesFileName();
            if (!usePropertiesFileName.isEmpty()) {
                addSharedConfigs(newLines, usePropertiesFileName);
            }
        }

        return newLines;
    }

    /**
     * 添加共享配置 - 完全保持现有逻辑
     */
    private void addSharedConfigs(List<String> newLines, Set<String> usePropertiesFileName) {
        // 保持现有的共享配置逻辑
        newLines.add("# Extension Config");
        int index = 0;
        for (String fileName : usePropertiesFileName) {
            newLines.add(String.format("spring.cloud.nacos.config.extension-configs[%d].data-id=%s", index, fileName));
            newLines.add(String.format("spring.cloud.nacos.config.extension-configs[%d].group=DEFAULT_GROUP", index));
            newLines.add(String.format("spring.cloud.nacos.config.extension-configs[%d].refresh=%s", index, refresh));
            index++;
        }
    }

    /**
     * 升级注册中心配置 - 完全保持现有复杂逻辑
     * 这是最关键的FeignClient处理逻辑，必须完全保持现有实现
     */
    @Override
    public void upgradeRegisterCenter(String propertiesFileContent) {
        // 完全保持现有的复杂FeignClient处理逻辑
        // 包括扫描internal文件夹、提取URL变量、替换为name属性等
        // 这部分逻辑非常复杂且关键，必须一字不改地迁移
        // [现有的upgradeRegisterCenter方法实现]
    }
}
```

### 8.4 模块版本管理

**模块版本一致性管理**
```xml
<!-- 所有子模块的pom.xml都应该引用父级版本 -->
<parent>
    <groupId>com.just</groupId>
    <artifactId>dm-auto-utils</artifactId>
    <version>2.0.0-SNAPSHOT</version>
    <relativePath>../pom.xml</relativePath>
</parent>
```

**发布版本管理**
- 开发版本：2.0.0-SNAPSHOT
- 正式版本：2.0.0, 2.0.1, 2.1.0等
- 所有模块版本保持一致

**关键要求**：
1. **Nacos升级逻辑不能有任何错误**：完全基于现有代码重构，保持所有复杂逻辑
2. **模块依赖关系清晰**：避免循环依赖，分层明确
3. **版本管理统一**：所有模块版本一致，便于维护
4. **向后兼容**：确保现有功能在重构后完全可用

## 九、重构验收标准

### 9.1 功能验收
- [ ] 所有现有Nacos升级功能完全可用
- [ ] 环境变量收集功能正常
- [ ] FeignClient升级逻辑无误
- [ ] Web界面功能完整

### 9.2 代码质量验收  
- [ ] 模块间依赖关系清晰
- [ ] 代码覆盖率 > 80%
- [ ] 无循环依赖
- [ ] 所有TODO完成

### 9.3 性能验收
- [ ] 升级性能不低于现有版本
- [ ] Web界面响应时间 < 3秒
- [ ] 内存使用合理

**总结：本重构计划确保了Nacos升级代码的准确性和完整性，通过详细的模块化设计实现了系统的可扩展性和可维护性。**