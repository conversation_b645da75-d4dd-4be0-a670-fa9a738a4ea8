<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>dm-boot-parent</artifactId>
    <groupId>com.innodealing</groupId>
    <version>1.0.4</version>
    <relativePath>pom.xml</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.just</groupId>
  <artifactId>nacos-property</artifactId>
  <name>nacos-property</name>
  <version>1.0-SNAPSHOT</version>
  <url>http://maven.apache.org</url>
  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <layout>ZIP</layout>
          <classifier>thin</classifier>
          <mainClass>com.just.v1.GenNacosProperties</mainClass>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.2.1</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <includes>
                    <include>**/*.class</include>
                  </includes>
                  <excludes>
                    <exclude>META-INF/*.SF</exclude>
                    <exclude>META-INF/*.DSA</exclude>
                    <exclude>META-INF/*.RSA</exclude>
                  </excludes>
                </filter>
              </filters>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <repositories>
    <repository>
      <id>nexus</id>
      <name>Team Nexus Repository</name>
      <url>http://nexus.innodealing.com/nexus/content/repositories/thirdparty/</url>
    </repository>
  </repositories>
  <properties>
    <sonar.test.exclusions>**/test/**</sonar.test.exclusions>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <java.version>1.8</java.version>
    <sonar.coverage.exclusions>**/*</sonar.coverage.exclusions>
    <druid.version>1.1.18</druid.version>
    <sonar.exclusions>**/test/**</sonar.exclusions>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
</project>
