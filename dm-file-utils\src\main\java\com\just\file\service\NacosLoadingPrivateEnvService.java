package com.just.file.service;

import com.just.file.config.ExcelConfigProperties;
import com.just.file.model.ConfigurationDataCenter;
import com.just.file.model.ConfigurationType;
import com.just.file.model.ProcessingContext;
import com.just.file.model.ProcessingResult;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 加载私有变量扩展接口
 * <AUTHOR>
 */
@Service
public class NacosLoadingPrivateEnvService implements LoadingEnvAware {

    @Override
    public void beforeLoading(ConfigurationType type, String environment, ExcelConfigProperties.FileConfig fileConfig, ProcessingContext processingContext) {

    }

    @Override
    public void afterLoading(ConfigurationType type, String environment, ExcelConfigProperties.FileConfig fileConfig, ProcessingResult result) {
        if (!Objects.equals(ConfigurationType.NACOS, type)) {
            return;
        }
        Map<String, Object> map = null;
        if (Objects.equals(environment, "prd")) {
            map = this.getProductionConfig();
        } else {
            map = this.getNonProductionConfig(environment);
        }
        map.forEach((key, val) -> {
            result.getConfigurations().add(this.createNacosProperty("custom user private environment variables",
                    "custom", 0, key, Objects.toString(val), "custom_env", environment));
        });
    }


    /**
     * 创建Nacos属性对象
     */
    private ConfigurationDataCenter.NacosProperty createNacosProperty(
            String filePath, String sheetName, int rowIndex,
            String k8sEnv, String nacosEnv, String nacosFileName, String environment) {

        return ConfigurationDataCenter.NacosProperty.builder()
                .fileName(filePath)
                .environment(environment)
                .sheetName(sheetName)
                .type(ConfigurationType.NACOS)
                .rowIndex(rowIndex)
                .k8sEnv(k8sEnv)
                .nacosEnv(nacosEnv)
                .nacosFileName(nacosFileName)
                .build();
    }


    private Map<String, Object> getProductionConfig() {
        final Map<String, Object> map = new HashMap<>();

        map.put("PUBLIC_CODIS_DATABASE", 0);
        map.put("PUBLIC_REDIS_DATABASE_NEW", 1);
        map.put("PUBLIC_GREENPLUM_DATABASE", "cg-prd");
        map.put("PUBLIC_PRICE_CODIS_DATABASE", 0);
        map.put("PUBLIC_PRICE_REDIS_DATABASE_NEW", 0);
        map.put("PUBLIC_REDIS_EXCEL_DATABASE", 0);

        // 测试环境
        //map.put("PUBLIC_CODIS_DATABASE", 0);
        //map.put("PUBLIC_REDIS_DATABASE_NEW", 1);
        //map.put("PUBLIC_GREENPLUM_DATABASE", "cg-prd");
        //map.put("PUBLIC_PRICE_CODIS_DATABASE", 0);
        //map.put("PUBLIC_PRICE_REDIS_DATABASE_NEW", 0);
        //map.put("PUBLIC_REDIS_EXCEL_DATABASE", 0);
        return map;
    }

    private Map<String, Object> getNonProductionConfig(String env) {
        final Map<String, Object> map = new HashMap<>();
        // 测试环境 为了防止意外直接跟现网保持一致
        map.put("PUBLIC_CODIS_DATABASE", 0);
        map.put("PUBLIC_REDIS_DATABASE_NEW", 1);
        map.put("PUBLIC_GREENPLUM_DATABASE", env);
        map.put("PUBLIC_PRICE_CODIS_DATABASE", 0);
        map.put("PUBLIC_PRICE_REDIS_DATABASE_NEW", 0);
        map.put("PUBLIC_REDIS_EXCEL_DATABASE", 0);

        // map.put("PUBLIC_CODIS_DATABASE", 1);
        //map.put("PUBLIC_REDIS_DATABASE_NEW", 1);
        //map.put("PUBLIC_GREENPLUM_DATABASE", env);
        //map.put("PUBLIC_PRICE_CODIS_DATABASE", 1);
        //map.put("PUBLIC_PRICE_REDIS_DATABASE_NEW", 1);
        //map.put("PUBLIC_REDIS_EXCEL_DATABASE", 1);
        return map;
    }
}
