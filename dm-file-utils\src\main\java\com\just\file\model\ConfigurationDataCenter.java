package com.just.file.model;

import lombok.Builder;
import lombok.Data;
import lombok.Value;

import java.util.List;
import java.util.Map;

/**
 * 配置数据模型
 * 简化设计，直接包含数据
 *
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
@Builder
@Data
public class ConfigurationDataCenter {
    /**
     * 文件级别的元数据
     */
    Map<ConfigurationType, ConfigurationMetadata> metadataMap;
    /**
     * Nacos属性列表
     */
    List<NacosProperty> nacosProperties;


    /**
     * Nacos属性
     */
    @Value
    @Builder(toBuilder = true)
    public static class NacosProperty {
        /**
         * K8s环境变量名
         */
        String k8sEnv;

        /**
         * Nacos环境变量名
         */
        String nacosEnv;

        /**
         * Nacos文件名
         */
        String nacosFileName;

        /**
         * 配置类型
         */
        ConfigurationType type;
        /**
         * 文件名
         */
        String fileName;
        /**
         * 工作表名
         */
        String sheetName;
        /**
         * 环境类型
         */
        String environment;
        /**
         * 行号
         */
        int rowIndex;

        /**
         * 验证属性是否有效
         */
        public boolean isValid() {
            return k8sEnv != null && !k8sEnv.trim().isEmpty() &&
                    nacosEnv != null && !nacosEnv.trim().isEmpty();
        }
    }
} 