package com.just.git.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * Git提交请求
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GitCommitRequest {
    
    /**
     * 提交消息
     */
    private String message;
    
    /**
     * 提交者姓名
     */
    private String authorName;
    
    /**
     * 提交者邮箱
     */
    private String authorEmail;
    
    /**
     * 要添加的文件路径集合（相对于仓库根目录）
     */
    private Set<String> filesToAdd;
    
    /**
     * 要删除的文件路径集合（相对于仓库根目录）
     */
    private Set<String> filesToRemove;
    
    /**
     * 是否添加所有修改的文件
     */
    private boolean addAll = false;
    
    /**
     * 是否允许空提交
     */
    private boolean allowEmpty = false;
    
    /**
     * 是否修正上一次提交（amend）
     */
    private boolean amend = false;
    
    /**
     * 是否在提交后推送到远程
     */
    private boolean pushAfterCommit = false;
    
    /**
     * 推送的目标分支（如果pushAfterCommit=true）
     */
    private String pushToBranch;
    
    /**
     * 是否强制推送
     */
    private boolean forcePush = false;
    
    /**
     * 验证请求参数
     */
    public void validate() {
        if (message == null || message.trim().isEmpty()) {
            throw new IllegalArgumentException("提交消息不能为空");
        }
        
        if (message.trim().length() > 500) {
            throw new IllegalArgumentException("提交消息不能超过500个字符");
        }
        
        if (!addAll && (filesToAdd == null || filesToAdd.isEmpty()) && 
            (filesToRemove == null || filesToRemove.isEmpty())) {
            if (!allowEmpty) {
                throw new IllegalArgumentException("必须指定要提交的文件或设置addAll=true");
            }
        }
        
        if (authorName != null && authorName.trim().length() > 100) {
            throw new IllegalArgumentException("提交者姓名不能超过100个字符");
        }
        
        if (authorEmail != null && !isValidEmail(authorEmail)) {
            throw new IllegalArgumentException("提交者邮箱格式无效");
        }
        
        if (pushAfterCommit && pushToBranch != null && pushToBranch.trim().isEmpty()) {
            throw new IllegalArgumentException("推送分支名称不能为空");
        }
    }
    
    /**
     * 验证邮箱格式
     */
    private boolean isValidEmail(String email) {
        return email != null && 
               email.contains("@") && 
               email.length() > 3 && 
               !email.startsWith("@") && 
               !email.endsWith("@");
    }
    
    /**
     * 创建简单提交请求
     */
    public static GitCommitRequest createSimple(String message) {
        return GitCommitRequest.builder()
                .message(message)
                .addAll(true)
                .build();
    }
    
    /**
     * 创建带推送的提交请求
     */
    public static GitCommitRequest createWithPush(String message, String targetBranch) {
        return GitCommitRequest.builder()
                .message(message)
                .addAll(true)
                .pushAfterCommit(true)
                .pushToBranch(targetBranch)
                .build();
    }
    
    /**
     * 创建指定文件的提交请求
     */
    public static GitCommitRequest createForFiles(String message, Set<String> files) {
        return GitCommitRequest.builder()
                .message(message)
                .filesToAdd(files)
                .build();
    }
    
    /**
     * 获取有效的提交者信息
     */
    public String getEffectiveAuthorName(String defaultName) {
        return (authorName != null && !authorName.trim().isEmpty()) ? authorName : defaultName;
    }
    
    /**
     * 获取有效的提交者邮箱
     */
    public String getEffectiveAuthorEmail(String defaultEmail) {
        return (authorEmail != null && !authorEmail.trim().isEmpty()) ? authorEmail : defaultEmail;
    }
} 