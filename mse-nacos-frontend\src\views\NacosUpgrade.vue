<template>
  <div class="nacos-upgrade-page">
    <!-- 筛选条件表单区域 -->
    <a-card title="升级配置" class="form-section">
      <a-form
        ref="formRef"
        :model="upgradeForm"
        :rules="formRules"
        layout="vertical"
        @finish="handleSubmit"
      >
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="业务组" name="dmGroup" required>
              <a-select
                v-model:value="upgradeForm.dmGroup"
                placeholder="请选择业务组"
                show-search
                :filter-option="filterOption"
              >
                <a-select-option value="价格组">价格组 (PRICE)</a-select-option>
                <a-select-option value="综合组">综合组 (GENERAL)</a-select-option>
                <a-select-option value="数据组">数据组 (DATA)</a-select-option>
                <a-select-option value="国际落地组">国际落地组 (INTL)</a-select-option>
                <a-select-option value="架构组">架构组 (TA)</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          
          <a-col :span="8">
            <a-form-item label="环境" name="environment" required>
              <a-select
                v-model:value="upgradeForm.environment"
                placeholder="请选择环境"
                show-search
                :filter-option="filterOption"
              >
                <a-select-option value="dev">开发环境 (dev)</a-select-option>
                <a-select-option value="qa">测试环境 (qa)</a-select-option>
                <a-select-option value="uat">预发环境 (uat)</a-select-option>
                <a-select-option value="prd">生产环境 (prd)</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="服务名称" name="serviceName" required>
              <a-select
                v-model:value="upgradeForm.serviceName"
                placeholder="请输入或选择服务名称"
                show-search
                :loading="loadingProjects"
                :filter-option="false"
                @search="handleServiceSearch"
                @change="handleServiceChange"
                allow-clear
              >
                <a-select-option 
                  v-for="project in filteredProjects" 
                  :key="project.id"
                  :value="project.name"
                >
                  <div style="display: flex; flex-direction: column; align-items: flex-start;">
                    <span style="font-weight: 500;">{{ project.name }}</span>
                    <span style="font-size: 12px; color: #666;">{{ project.pathWithNamespace }}</span>
                  </div>
                </a-select-option>
                
                <!-- 后备静态选项（当GitLab API不可用时） -->
                <template v-if="gitlabProjects.length === 0 && !loadingProjects">
                  <a-select-option value="user-service">user-service</a-select-option>
                  <a-select-option value="order-service">order-service</a-select-option>
                  <a-select-option value="payment-service">payment-service</a-select-option>
                  <a-select-option value="product-service">product-service</a-select-option>
                </template>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="24">
          <a-col :span="6">
            <a-form-item label="升级注册中心" name="upgradeRegistry">
              <a-select
                v-model:value="upgradeForm.upgradeRegistry"
                placeholder="请选择是否升级注册中心"
              >
                <a-select-option :value="true">是</a-select-option>
                <a-select-option :value="false">否</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          
          <a-col :span="6">
            <a-form-item label="自动刷新配置" name="autoRefresh">
              <a-select
                v-model:value="upgradeForm.autoRefresh"
                placeholder="请选择是否自动刷新配置"
              >
                <a-select-option :value="true">是</a-select-option>
                <a-select-option :value="false">否</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          
          <a-col :span="6">
            <a-form-item label="是否发布" name="autoPublish">
              <a-select
                v-model:value="upgradeForm.autoPublish"
                placeholder="请选择是否自动发布"
              >
                <a-select-option :value="true">是</a-select-option>
                <a-select-option :value="false">否</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          
          <a-col :span="6">
            <a-form-item label="分支名称" name="branchName">
              <a-auto-complete
                v-model:value="upgradeForm.branchName"
                :options="branchOptions"
                placeholder="请选择或输入分支名称"
                :loading="loadingBranches"
                :filter-option="false"
                @search="handleBranchSearch"
                allow-clear
                :disabled="!selectedProjectId && projectBranches.length === 0"
              >
                <template #option="{ value, label, default: isDefault }">
                  <div style="display: flex; align-items: center; justify-content: space-between;">
                    <span>{{ label }}</span>
                    <a-tag v-if="isDefault" size="small" color="blue">默认</a-tag>
                  </div>
                </template>
              </a-auto-complete>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row>
          <a-col :span="24" style="text-align: center">
            <a-space>
              <a-button 
                type="primary" 
                html-type="submit" 
                :loading="upgrading"
                size="large"
              >
                开始升级
              </a-button>
              <a-button 
                @click="resetForm"
                size="large"
              >
                重置
              </a-button>
              <a-button 
                v-if="currentTaskId" 
                danger 
                @click="cancelUpgrade"
                size="large"
                :loading="cancelling"
              >
                取消升级
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 升级信息展示区域 -->
    <a-card title="升级进度" class="progress-section" v-if="showProgress">
      <div class="progress-container">
        <!-- 进度条 -->
        <div class="progress-header">
          <a-progress 
            :percent="progressPercent" 
            :status="progressStatus"
            stroke-color="#1890ff"
            trail-color="#f5f5f5"
          />
          <div class="progress-info">
            <a-tag :color="getStatusColor(upgradeStatus)">
              {{ getStatusText(upgradeStatus) }}
            </a-tag>
            <span class="task-id">任务ID: {{ currentTaskId }}</span>
            <span v-if="upgradeResult && upgradeResult.startTime && upgradeResult.endTime" class="duration">
              耗时: {{ calculateDuration(upgradeResult.startTime, upgradeResult.endTime) }}
            </span>
          </div>
        </div>

        <!-- 升级结果详情 -->
        <div v-if="upgradeResult" class="upgrade-result-section">
          <a-row :gutter="16" style="margin-bottom: 16px;">
            <a-col :span="24">
              <h4>升级结果详情</h4>
            </a-col>
          </a-row>
          
          <a-row :gutter="16" style="margin-bottom: 16px;">
            <a-col :span="8">
              <a-statistic title="服务名称" :value="upgradeResult.serviceName || '-'" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="环境" :value="upgradeResult.environment || '-'" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="当前步骤" :value="upgradeResult.currentStep || '-'" />
            </a-col>
          </a-row>

          <!-- 未解析的环境变量 -->
          <div v-if="unresolvedTableData.length > 0" 
               class="unresolved-section" style="margin-bottom: 16px;">
            <h4 style="color: #ff4d4f; margin-bottom: 12px;">
              <exclamation-circle-outlined /> 未解析的环境变量 ({{ unresolvedTableData.length }}个)
            </h4>
            <a-table 
              :columns="unresolvedColumns" 
              :data-source="unresolvedTableData" 
              :pagination="false"
              size="small"
              bordered
              class="unresolved-table"
            >
              <template #bodyCell="{ column, text, record }">
                <span v-if="column.key === 'variable'" class="unresolved-variable">
                  {{ record.k8sEnvVarName }}
                </span>
                <span v-else-if="column.key === 'lineNumber'" class="line-number">
                  {{ record.lineNumber > 0 ? record.lineNumber : '-' }}
                </span>
                <span v-else-if="column.key === 'originalLine'" class="original-line" :title="record.originalLine">
                  {{ record.originalLine }}
                </span>
                <span v-else-if="column.key === 'context'" class="context-info" :title="record.context">
                  {{ record.context }}
                </span>
                <span v-else>{{ text }}</span>
              </template>
            </a-table>
          </div>

        </div>
        
        <!-- 日志输出区域 -->
        <div class="log-container">
          <div class="log-header">
            <h4>升级日志</h4>
            <a-space>
              <a-button size="small" @click="scrollToBottom">
                滚动到底部
              </a-button>
              <a-button size="small" @click="clearLogs">
                清空日志
              </a-button>
            </a-space>
          </div>
          
          <div 
            ref="logAreaRef"
            class="log-content"
            :class="{ 'auto-scroll': autoScroll }"
          >
            <div 
              v-for="(log, index) in upgradeLogs" 
              :key="index"
              class="log-line"
              :class="getLogClass(log)"
            >
              <span class="log-time">[{{ formatTime(log.timestamp) }}]</span>
              <span class="log-level">[{{ log.level }}]</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
            
            <div v-if="upgradeLogs.length === 0" class="empty-logs">
              暂无日志信息
            </div>
          </div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick, onUnmounted, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { upgradeApi, gitlabApi } from '@/api'
import dayjs from 'dayjs'

// 表单相关
const formRef = ref()
const upgradeForm = reactive({
  dmGroup: '',
  environment: '',
  serviceName: '',
  upgradeRegistry: true,
  autoRefresh: true,
  autoPublish: true,
  branchName: 'master'
})

// GitLab项目相关
const gitlabProjects = ref([])
const filteredProjects = ref([])
const loadingProjects = ref(false)

// 分支相关
const projectBranches = ref([])
const loadingBranches = ref(false)
const selectedProjectId = ref('')

// 分支选项（用于auto-complete）
const branchOptions = computed(() => {
  const branches = projectBranches.value.map(branch => ({
    value: branch.name,
    label: branch.name,
    default: branch.default
  }))
  
  // 如果没有从项目获取到分支，提供默认选项
  if (branches.length === 0) {
    return [
      { value: 'master', label: 'master', default: true },
      { value: 'main', label: 'main', default: false },
      { value: 'develop', label: 'develop', default: false },
      { value: 'dev', label: 'dev', default: false },
      { value: 'release', label: 'release', default: false },
      { value: 'hotfix', label: 'hotfix', default: false }
    ]
  }
  
  return branches
})

const formRules = {
  dmGroup: [
    { required: true, message: '请选择业务组', trigger: 'change' }
  ],
  environment: [
    { required: true, message: '请选择环境', trigger: 'change' }
  ],
  serviceName: [
    { required: true, message: '请输入服务名称', trigger: 'blur' }
  ]
}

// 升级状态相关
const upgrading = ref(false)
const cancelling = ref(false)
const showProgress = ref(false)
const currentTaskId = ref('')
const upgradeStatus = ref('')
const progressPercent = ref(0)
const progressStatus = ref('active')

// 日志相关
const logAreaRef = ref()
const upgradeLogs = ref([])
const autoScroll = ref(true)

// 升级结果相关
const upgradeResult = ref(null)

// 未解析变量表格列定义
const unresolvedColumns = ref([
  {
    title: '变量名',
    key: 'variable',
    dataIndex: 'k8sEnvVarName',
    width: 200
  },
  {
    title: '行号',
    key: 'lineNumber',
    dataIndex: 'lineNumber',
    width: 80,
    align: 'center'
  },
  {
    title: '原始行内容',
    key: 'originalLine',
    dataIndex: 'originalLine',
    ellipsis: true
  },
  {
    title: '说明',
    key: 'context',
    dataIndex: 'context',
    width: 200,
    ellipsis: true
  }
])

// 未解析变量表格数据
const unresolvedTableData = computed(() => {
  if (!upgradeResult.value) {
    return []
  }
  
  // 首先尝试获取完整的UnresolvedEnvInfo对象
  const unresolvedEnvInfoObjects = upgradeResult.value.context?.unresolved_env_info_objects
  
  if (unresolvedEnvInfoObjects && Array.isArray(unresolvedEnvInfoObjects)) {
    // 使用完整的UnresolvedEnvInfo对象
    return unresolvedEnvInfoObjects.map((envInfo, index) => ({
      key: index,
      k8sEnvVarName: envInfo.k8sEnvVarName || envInfo.variable || '未知',
      lineNumber: envInfo.lineNumber || 0,
      originalLine: envInfo.originalLine || '无',
      context: envInfo.context || '需要在对应环境配置中添加此变量'
    }))
  }
  
  // 回退到简单的字符串数组（兼容旧格式）
  if (upgradeResult.value.unresolvedEnvVars && Array.isArray(upgradeResult.value.unresolvedEnvVars)) {
    return upgradeResult.value.unresolvedEnvVars.map((variable, index) => ({
      key: index,
      k8sEnvVarName: variable,
      lineNumber: 0,
      originalLine: '未知',
      context: '需要在对应环境配置中添加此变量'
    }))
  }
  
  return []
})

// 已处理消息集合，避免重复添加相同消息到日志
const processedMessages = new Set()

// 页面加载时获取GitLab项目列表
onMounted(() => {
  loadGitLabProjects()
})

// 加载GitLab项目列表
const loadGitLabProjects = async () => {
  try {
    loadingProjects.value = true
    
    // 搜索所有项目（空关键词返回所有项目）
    const response = await gitlabApi.searchProjects({ keyword: '' })
    
    if (response.success && response.data) {
      gitlabProjects.value = response.data
      filteredProjects.value = response.data
      console.log('GitLab项目加载成功，共', response.data.length, '个项目')
    } else {
      console.warn('GitLab项目加载失败:', response.message)
      message.warning('GitLab项目加载失败，将使用静态选项')
    }
  } catch (error) {
    console.error('GitLab项目加载失败:', error)
    message.error('GitLab项目加载失败，将使用静态选项')
  } finally {
    loadingProjects.value = false
  }
}

// 服务名称搜索过滤
const handleServiceSearch = (value) => {
  if (!value || value.trim() === '') {
    filteredProjects.value = gitlabProjects.value
  } else {
    const keyword = value.toLowerCase()
    filteredProjects.value = gitlabProjects.value.filter(project => {
      const name = project.name?.toLowerCase() || ''
      const path = project.path?.toLowerCase() || ''
      const pathWithNamespace = project.pathWithNamespace?.toLowerCase() || ''
      
      return name.includes(keyword) || 
             path.includes(keyword) || 
             pathWithNamespace.includes(keyword)
    })
  }
}

// 表单处理
const filterOption = (input, option) => {
  return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 分支搜索处理（用于auto-complete）
const handleBranchSearch = (value) => {
  // auto-complete组件会自动根据输入过滤选项
  // 这里可以添加额外的搜索逻辑，但通常不需要
  console.log('分支搜索:', value)
}

// 服务名称选择处理
const handleServiceChange = async (value) => {
  upgradeForm.serviceName = value
  
  // 如果选择的是GitLab项目，获取项目信息
  const selectedProject = gitlabProjects.value.find(p => 
    p.name === value || p.path === value || p.pathWithNamespace === value
  )
  
  if (selectedProject) {
    console.log('选择的GitLab项目:', selectedProject)
    
    // 确保项目ID存在且有效
    const projectId = selectedProject.projectId || selectedProject.id
    if (projectId && projectId !== 'undefined') {
      selectedProjectId.value = projectId
      console.log('项目ID:', projectId)
      
      // 获取项目分支列表
      await loadProjectBranches(projectId)
    } else {
      console.warn('项目ID无效:', selectedProject)
      // 清空分支列表
      projectBranches.value = []
      selectedProjectId.value = ''
      upgradeForm.branchName = 'master'
    }
  } else {
    console.log('未找到匹配的项目，value:', value)
    // 清空分支列表
    projectBranches.value = []
    selectedProjectId.value = ''
    upgradeForm.branchName = 'master'
  }
}

// 加载项目分支列表
const loadProjectBranches = async (projectId) => {
  try {
    loadingBranches.value = true
    
    // 检查项目ID是否有效
    if (!projectId || projectId === 'undefined' || projectId === 'null') {
      console.warn('项目ID无效，无法加载分支列表:', projectId)
      projectBranches.value = []
      upgradeForm.branchName = 'master'
      return
    }
    
    console.log('开始加载项目分支，项目ID:', projectId)
    
    // 尝试调用真实的GitLab分支API
    try {
      const response = await gitlabApi.getBranches(projectId)
      
      if (response.success && response.data && Array.isArray(response.data)) {
        // 处理API返回的分支数据
        projectBranches.value = response.data.map(branch => ({
          name: branch.name,
          default: branch.default || branch.name === 'master' || branch.name === 'main'
        }))
        
        // 设置默认分支
        const defaultBranch = projectBranches.value.find(b => b.default) || projectBranches.value[0]
        if (defaultBranch) {
          upgradeForm.branchName = defaultBranch.name
        }
        
        console.log('项目', projectId, '的分支列表加载成功，共', projectBranches.value.length, '个分支')
        return
      } else {
        console.warn('GitLab分支API返回数据无效:', response)
      }
    } catch (apiError) {
      console.warn('GitLab分支API调用失败，使用默认分支列表:', apiError.message)
    }
    
    // 如果API调用失败，使用默认分支列表
    projectBranches.value = [
      { name: 'master', default: true },
      { name: 'main', default: false },
      { name: 'develop', default: false },
      { name: 'dev', default: false },
      { name: 'release', default: false },
      { name: 'hotfix', default: false }
    ]
    
    // 设置默认分支
    const defaultBranch = projectBranches.value.find(b => b.default)
    if (defaultBranch) {
      upgradeForm.branchName = defaultBranch.name
    }
    
    console.log('使用默认分支列表，项目:', projectId)
    
  } catch (error) {
    console.error('加载项目分支列表失败:', error)
    message.warning('加载分支列表失败，将使用默认选项')
    
    // 回退到默认分支列表
    projectBranches.value = [
      { name: 'master', default: true },
      { name: 'main', default: false },
      { name: 'develop', default: false }
    ]
    upgradeForm.branchName = 'master'
    
  } finally {
    loadingBranches.value = false
  }
}

const handleSubmit = async () => {
  try {
    upgrading.value = true
    showProgress.value = true
    upgradeLogs.value = []
    upgradeResult.value = null // 重置升级结果
    
    // 添加开始日志
    addLog('INFO', '开始提交升级请求...')
    
    const response = await upgradeApi.upgradeService(upgradeForm)
    
    if (response.success && response.data) {
      currentTaskId.value = response.data.upgradeId
      upgradeStatus.value = response.data.status || 'RUNNING'
      
      // 保存完整的升级结果数据
      upgradeResult.value = response.data
      
      console.log('升级请求成功，任务ID:', currentTaskId.value, '状态:', upgradeStatus.value)
      console.log('升级结果数据:', response.data)
      
      addLog('SUCCESS', `升级任务已创建，任务ID: ${currentTaskId.value}`)
      addLog('INFO', '开始监控升级进度...')
      
      // 添加初始升级消息到日志
      if (response.data.messages && Array.isArray(response.data.messages)) {
        response.data.messages.forEach(message => {
          // 避免重复添加相同消息
          if (!processedMessages.has(message)) {
            processedMessages.add(message)
            
            // 根据消息内容判断日志级别
            let logLevel = 'INFO'
            if (typeof message === 'string') {
              const messageStr = message.toLowerCase()
              if (messageStr.includes('失败') || messageStr.includes('错误') || messageStr.includes('error')) {
                logLevel = 'ERROR'
              } else if (messageStr.includes('警告') || messageStr.includes('warning') || messageStr.includes('warn')) {
                logLevel = 'WARNING'
              } else if (messageStr.includes('成功') || messageStr.includes('完成') || messageStr.includes('success')) {
                logLevel = 'SUCCESS'
              }
            }
            addLog(logLevel, message)
          }
        })
      }
      
      // 如果有未解析的环境变量，显示警告
      if (response.data.unresolvedEnvVars && response.data.unresolvedEnvVars.length > 0) {
        addLog('WARN', `发现 ${response.data.unresolvedEnvVars.length} 个未解析的环境变量，请查看详情`)
      }
    } else {
      addLog('ERROR', '升级请求失败: ' + (response.message || '未知错误'))
      showProgress.value = false
    }
  } catch (error) {
    addLog('ERROR', '升级请求失败: ' + (error.message || '网络错误'))
    showProgress.value = false
  } finally {
    upgrading.value = false
  }
}

const resetForm = () => {
  formRef.value?.resetFields()
  upgradeForm.dmGroup = ''
  upgradeForm.environment = ''
  upgradeForm.serviceName = ''
  upgradeForm.upgradeRegistry = true
  upgradeForm.autoRefresh = true
  upgradeForm.autoPublish = true
  upgradeForm.branchName = 'master'
  
  // 重置项目过滤和分支列表
  filteredProjects.value = gitlabProjects.value
  projectBranches.value = []
  selectedProjectId.value = ''
  
  // 重置升级状态和结果
  showProgress.value = false
  upgradeResult.value = null
  currentTaskId.value = ''
  upgradeStatus.value = ''
  progressPercent.value = 0
  progressStatus.value = 'active'
  upgradeLogs.value = []
  
  // 清理已处理消息集合
  processedMessages.clear()
}

const cancelUpgrade = async () => {
  if (!currentTaskId.value) return
  
  try {
    cancelling.value = true
    addLog('INFO', '正在取消升级任务...')
    
    const response = await upgradeApi.cancelUpgrade(currentTaskId.value)
    
    if (response.success) {
      addLog('WARNING', '升级任务已取消')
      upgradeStatus.value = 'CANCELLED'
      progressStatus.value = 'exception'
    } else {
      addLog('ERROR', '取消升级失败: ' + (response.message || '未知错误'))
    }
  } catch (error) {
    addLog('ERROR', '取消升级失败: ' + (error.message || '网络错误'))
  } finally {
    cancelling.value = false
  }
}



// 日志处理
const addLog = (level, message, timestamp = null) => {
  upgradeLogs.value.push({
    level,
    message,
    timestamp: timestamp || Date.now()
  })
  
  // 自动滚动到底部
  if (autoScroll.value) {
    nextTick(() => {
      scrollToBottom()
    })
  }
}

const scrollToBottom = () => {
  if (logAreaRef.value) {
    logAreaRef.value.scrollTop = logAreaRef.value.scrollHeight
  }
}

const clearLogs = () => {
  upgradeLogs.value = []
}

// 样式和文本处理
const getStatusColor = (status) => {
  const colors = {
    PENDING: 'blue',
    RUNNING: 'processing',
    SUCCESS: 'success',
    FAILED: 'error',
    CANCELLED: 'warning'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    PENDING: '等待中',
    RUNNING: '升级中',
    SUCCESS: '成功',
    FAILED: '失败',
    CANCELLED: '已取消'
  }
  return texts[status] || '未知'
}

const getLogClass = (log) => {
  return {
    'log-error': log.level === 'ERROR',
    'log-warning': log.level === 'WARNING',
    'log-success': log.level === 'SUCCESS',
    'log-info': log.level === 'INFO'
  }
}

const formatTime = (timestamp) => {
  return dayjs(timestamp).format('HH:mm:ss')
}

// 计算持续时间
const calculateDuration = (startTime, endTime) => {
  if (!startTime || !endTime) return '未知'
  
  const start = dayjs(startTime)
  const end = dayjs(endTime)
  const duration = end.diff(start, 'second')
  
  if (duration < 60) {
    return `${duration}秒`
  } else if (duration < 3600) {
    const minutes = Math.floor(duration / 60)
    const seconds = duration % 60
    return `${minutes}分${seconds}秒`
  } else {
    const hours = Math.floor(duration / 3600)
    const minutes = Math.floor((duration % 3600) / 60)
    return `${hours}小时${minutes}分钟`
  }
}

// 清理定时器
onUnmounted(() => {
  // 已无需清理定时器
})
</script>

<style scoped>
.nacos-upgrade-page {
  max-width: 1200px;
  margin: 0 auto;
}

.form-section {
  margin-bottom: 24px;
}

.progress-section {
  min-height: 400px;
}

.progress-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.progress-header {
  margin-bottom: 24px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.task-id {
  color: #666;
  font-size: 12px;
}

.duration {
  color: #666;
  font-size: 12px;
  margin-left: 16px;
}

/* 升级结果详情区域 */
.upgrade-result-section {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.upgrade-result-section h4 {
  margin-bottom: 16px;
  color: #333;
}

/* 未解析变量区域 */
.unresolved-section {
  background: #fff2f0;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #ffccc7;
}

.unresolved-table {
  margin-top: 8px;
}

.unresolved-variable {
  color: #ff4d4f;
  font-weight: bold;
  font-family: 'Courier New', monospace;
}

.line-number {
  color: #666;
  font-weight: 500;
  text-align: center;
}

.original-line {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #333;
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 2px;
  word-break: break-all;
}

.context-info {
  color: #666;
  font-size: 12px;
}

.log-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.log-header h4 {
  margin: 0;
}

.log-content {
  flex: 1;
  background: #1e1e1e;
  color: #fff;
  padding: 16px;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  max-height: 400px;
  overflow-y: auto;
  min-height: 200px;
}

.log-line {
  margin-bottom: 4px;
  white-space: pre-wrap;
  word-break: break-all;
}

.log-line.log-error {
  color: #ff4d4f;
}

.log-line.log-warning {
  color: #faad14;
}

.log-line.log-success {
  color: #52c41a;
}

.log-line.log-info {
  color: #1890ff;
}

.log-time {
  color: #999;
  margin-right: 8px;
}

.log-level {
  font-weight: bold;
  margin-right: 8px;
  min-width: 60px;
  display: inline-block;
}

.log-message {
  word-break: break-word;
}

.empty-logs {
  text-align: center;
  color: #666;
  font-style: italic;
  margin-top: 50px;
}

.auto-scroll {
  scroll-behavior: smooth;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nacos-upgrade-page {
    padding: 0 16px;
  }
  
  .progress-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .log-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>