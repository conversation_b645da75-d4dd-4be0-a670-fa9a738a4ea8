package com.just.git.service;

import com.just.git.config.GitConfig;
import com.just.git.model.GitCredentials;
import com.just.git.model.GitLabProject;
import com.just.git.utils.GitUtils;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.LsRemoteCommand;
import org.eclipse.jgit.lib.Ref;
import org.eclipse.jgit.transport.CredentialsProvider;
import org.gitlab4j.api.GitLabApi;
import org.gitlab4j.api.GitLabApiException;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * Git项目服务实现类
 * 结合GitLab API和JGit实现项目信息获取
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@Service
public class GitProjectServiceImpl implements GitProjectService {

    private final GitConfig gitConfig;
    private final ExecutorService executorService;
    private GitLabApi gitLabApi;

    /**
     * 项目信息缓存
     * Key: projectId, Value: GitLabProject
     */
    private final Map<String, GitLabProject> projectCache = new ConcurrentHashMap<>();

    /**
     * 项目名称到ID的映射缓存
     */
    private final Map<String, String> nameToIdCache = new ConcurrentHashMap<>();

    public GitProjectServiceImpl(GitConfig gitConfig) {
        this.gitConfig = gitConfig;
        this.executorService = Executors.newFixedThreadPool(10);
        this.gitLabApi = new GitLabApi(gitConfig.getBaseUrl(), gitConfig.getGitCoreProperties().getToken());
    }

    @PostConstruct
    public void init() throws ExecutionException, InterruptedException {
        CompletableFuture<List<GitLabProject>> listCompletableFuture = loadAllProjects();
        listCompletableFuture.get();
    }

    @Override
    public CompletableFuture<List<GitLabProject>> loadAllProjects() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("开始加载所有Git项目信息...");
                // 第一步：通过GitLab API获取所有项目基础信息
                List<GitLabProject> projects = ********************();
                log.info("通过GitLab API获取到 {} 个项目", projects.size());
                updateCache(projects);
                return projects;
            } catch (Exception e) {
                log.error("加载项目信息失败", e);
                return Collections.emptyList();
            }
        }, executorService);
    }


    @Override
    public List<GitLabProject> ********************() {
        List<GitLabProject> projects = new ArrayList<>();

        try {
            log.info("通过GitLab4J API获取所有项目信息...");
            // 使用GitLab4J API获取所有项目
            List<org.gitlab4j.api.models.Project> gitlabProjects = gitLabApi.getProjectApi().getProjects();
            for (org.gitlab4j.api.models.Project gitlabProject : gitlabProjects) {
                GitLabProject project = **********************(gitlabProject);
                if (project != null) {
                    projects.add(project);
                }
            }
            log.info("通过GitLab4J API获取到 {} 个项目", projects.size());
        } catch (Exception e) {
            log.error("通过GitLab4J API获取项目信息失败", e);
        }

        return projects;
    }


    @Override
    public List<GitLabProject> searchProjectsByName(String projectName) {
        return searchProjectsByName(projectName, gitConfig.getDefaultCredentials());
    }

    @Override
    public List<GitLabProject> searchProjectsByName(String projectName, GitCredentials credentials) {
        if (!StringUtils.hasText(projectName)) {
            return Collections.emptyList();
        }

        String searchTerm = projectName.toLowerCase();

        return projectCache.values().stream()
                .filter(project ->
                        (project.getName() != null && project.getName().toLowerCase().contains(searchTerm)) ||
                                (project.getPath() != null && project.getPath().toLowerCase().contains(searchTerm)) ||
                                (project.getDescription() != null && project.getDescription().toLowerCase().contains(searchTerm))
                )
                .collect(Collectors.toList());
    }

    @Override
    public Optional<GitLabProject> getProjectById(String projectId) {
        return getProjectById(projectId, gitConfig.getDefaultCredentials());
    }

    @Override
    public Optional<GitLabProject> getProjectById(String projectId, GitCredentials credentials) {
        if (!StringUtils.hasText(projectId)) {
            return Optional.empty();
        }

        // 先从缓存查找
        GitLabProject cached = projectCache.get(projectId);
        if (cached != null) {
            return Optional.of(cached);
        }

        // 尝试通过名称映射查找
        String mappedId = nameToIdCache.get(projectId);
        if (mappedId != null) {
            cached = projectCache.get(mappedId);
            if (cached != null) {
                return Optional.of(cached);
            }
        }

        // 如果缓存中没有，尝试通过GitLab4J API获取
        try {
            log.debug("通过GitLab4J API获取项目详情，项目ID: {}", projectId);
            
            org.gitlab4j.api.models.Project gitlabProject = gitLabApi.getProjectApi().getProject(projectId);
            GitLabProject project = **********************(gitlabProject);

            if (project != null) {
                // 缓存结果
                projectCache.put(project.getPath(), project);
                nameToIdCache.put(project.getName(), project.getPath());
                return Optional.of(project);
            }

        } catch (Exception e) {
            log.debug("通过GitLab4J API获取项目失败: {}", projectId, e);
        }

        return Optional.empty();
    }

    @Override
    public boolean validateGitRepository(String gitUrl, GitCredentials credentials) {
        try {
            LsRemoteCommand lsRemoteCommand = Git.lsRemoteRepository().setRemote(gitUrl);

            CredentialsProvider credentialsProvider = GitUtils.createCredentialsProvider(credentials);
            if (credentialsProvider != null) {
                lsRemoteCommand.setCredentialsProvider(credentialsProvider);
            }
            Collection<Ref> refs = lsRemoteCommand.call();
            boolean isValid = refs != null && !refs.isEmpty();
            log.trace("Git仓库验证结果 [{}]: {}", gitUrl, isValid);
            return isValid;
        } catch (Exception e) {
            log.trace("验证Git仓库失败 [{}]: {}", gitUrl, e.getMessage());
            return false;
        }
    }


    @Override
    public boolean refreshCache() {
        try {
            log.info("开始刷新项目缓存...");
            projectCache.clear();
            nameToIdCache.clear();

            loadAllProjects().thenAccept(projects ->
                    log.info("项目缓存刷新完成，共 {} 个项目", projects.size())
            );

            return true;
        } catch (Exception e) {
            log.error("刷新项目缓存失败", e);
            return false;
        }
    }

    @Override
    public int getCachedProjectCount() {
        return projectCache.size();
    }

    @Override
    public List<GitLabProject> getCachedProjects() {
        return new ArrayList<>(projectCache.values());
    }

    @Override
    public List<GitLabProject> getProjectsByNamespace(String namespace, GitCredentials credentials) {
        if (!StringUtils.hasText(namespace)) {
            return Collections.emptyList();
        }

        return projectCache.values().stream()
                .filter(project -> project.getPath() != null && project.getPath().startsWith(namespace + "/"))
                .collect(Collectors.toList());
    }


    @Override
    public Optional<String> getLatestCommit(String projectId, String branch, GitCredentials credentials) {
        Optional<GitLabProject> projectOpt = getProjectById(projectId, credentials);
        return projectOpt.map(GitLabProject::getLastCommitId);
    }

    @Override
    public List<org.gitlab4j.api.models.Branch> listBranches(String projectId) throws GitLabApiException {
        try {
            log.info("通过GitLab4J API获取项目分支列表，项目ID: {}", projectId);
            
            // 使用GitLab4J API获取分支列表
            List<org.gitlab4j.api.models.Branch> branches = gitLabApi.getRepositoryApi().getBranches(projectId);
            
            log.info("成功获取项目 {} 的分支列表，共 {} 个分支", projectId, branches.size());
            return branches;
            
        } catch (GitLabApiException e) {
            log.error("通过GitLab4J API获取项目分支列表失败，项目ID: {}", projectId, e);
            throw e;
        }
    }

    /**
     * 将GitLab4J的Project对象转换为GitLabProject对象
     */
    private GitLabProject **********************(org.gitlab4j.api.models.Project gitlabProject) {
        try {
            return GitLabProject.builder()
                    .projectId(gitlabProject.getId().longValue())
                    .name(gitlabProject.getName())
                    .path(gitlabProject.getPath())
                    .pathWithNamespace(gitlabProject.getPathWithNamespace())
                    .description(gitlabProject.getDescription())
                    .httpUrlToRepo(gitlabProject.getHttpUrlToRepo())
                    .sshUrlToRepo(gitlabProject.getSshUrlToRepo())
                    .webUrl(gitlabProject.getWebUrl())
                    .defaultBranch(gitlabProject.getDefaultBranch())
                    .lastCommitId(gitlabProject.getLastActivityAt() != null ? gitlabProject.getLastActivityAt().toString() : null)
                    .exists(true)
                    .accessible(true)
                    .build();
        } catch (Exception e) {
            log.debug("转换GitLab项目对象失败", e);
            return null;
        }
    }

    /**
     * 更新缓存
     */
    private void updateCache(List<GitLabProject> projects) {
        for (GitLabProject project : projects) {
            if (StringUtils.hasText(project.getPath())) {
                projectCache.put(project.getPath(), project);
                if (StringUtils.hasText(project.getName())) {
                    nameToIdCache.put(project.getName(), project.getPath());
                }
            }
        }
    }

}