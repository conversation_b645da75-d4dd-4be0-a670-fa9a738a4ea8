package com.just.upgrade.service.Impl;

import com.just.git.model.GitRepository;
import com.just.upgrade.model.UpgradeRequest;
import com.just.upgrade.model.UpgradeStatus;
import lombok.extern.slf4j.Slf4j;

import java.nio.file.Path;

/**
 * 升级服务模版方法抽象类
 * 定义升级流程的标准模板，子类实现具体步骤
 */
@Slf4j
public abstract class UpgradeTemplate {

    /**
     * 模版方法：定义升级流程
     * 
     * @param request 升级请求
     * @return 升级状态
     */
    public final UpgradeStatus executeUpgradeNacos(UpgradeRequest request) {
        UpgradeStatus status = initializeUpgradeStatus(request);
        
        try {
            // 步骤1：读取Excel配置文件获取环境变量
            status = updateStep(status, 1, "正在读取Excel配置文件...");
            readExcelConfiguration(request, status);
            
            // 步骤2：拉取Git仓库并创建升级分支
            status = updateStep(status, 2, "正在拉取Git仓库并创建升级分支...");
            prepareGitRepository(request, status);
            
            // 步骤3：搜索application.properties文件
            status = updateStep(status, 3, "正在搜索application.properties文件...");
            searchApplicationProperties(request, status);
            
            // 步骤4：替换K8s环境变量为Nacos配置
            status = updateStep(status, 4, "正在将K8s环境变量替换为Nacos配置...");
            replaceK8sWithNacosConfig(request, status);
            
            // 步骤5：生成bootstrap和POM文件
            status = updateStep(status, 5, "正在生成bootstrap和POM文件...");
            generateBootstrapAndPom(request, status);

            // 步骤6：删除不需要的文件
            status = updateStep(status, 6, "正在删除不必要文件...");
            deleteUnnecessaryFiles(request, status);
            
            // 步骤6：处理Git提交和推送
            status = updateStep(status, 6, "正在提交代码并创建合并请求...");
            commitAndPush(request, status);

            status = updateStep(status, 7,"正在发布配置推送mse nacos");
            publishNacos(request, status);
            
            // 完成升级
            status = completeUpgrade(status);
            
        } catch (Exception e) {
            cleanupWorkspaceDirectory(status);
            status = handleUpgradeException(status, e);
        }
        
        return status;
    }

    private void deleteUnnecessaryFiles(UpgradeRequest request, UpgradeStatus status) {
        log.info("开始删除不必要的文件");
        
        try {
            // 获取application.properties路径
            Path applicationPropertiesPath = (Path) status.getContext().get("application_properties_path");
            
            if (applicationPropertiesPath == null) {
                status.getMessages().add(getCurrentTime() + " - 警告：无法获取application.properties路径，跳过文件删除");
                return;
            }
            
            // 获取application.properties所在的目录
            Path resourcesDir = applicationPropertiesPath.getParent();
            if (resourcesDir == null || !java.nio.file.Files.exists(resourcesDir)) {
                status.getMessages().add(getCurrentTime() + " - 警告：resources目录不存在，跳过文件删除");
                return;
            }
            
            int deletedCount = 0;
            
            // 遍历resources目录下所有以application开头的文件
            try (java.util.stream.Stream<Path> stream = java.nio.file.Files.list(resourcesDir)) {
                java.util.List<Path> filesToDelete = stream
                    .filter(java.nio.file.Files::isRegularFile)
                    .filter(path -> {
                        String fileName = path.getFileName().toString();
                        // 删除所有以application开头的文件，但保留application-nacos.properties
                        return fileName.startsWith("application") && 
                               !"application-nacos.properties".equals(fileName);
                    })
                    .collect(java.util.stream.Collectors.toList());
                
                // 删除文件
                for (Path fileToDelete : filesToDelete) {
                    try {
                        java.nio.file.Files.delete(fileToDelete);
                        status.getMessages().add(getCurrentTime() + " - 已删除：" + fileToDelete.getFileName());
                        deletedCount++;
                        log.info("删除不必要文件成功: {}", fileToDelete);
                    } catch (Exception e) {
                        status.getMessages().add(getCurrentTime() + " - 删除失败：" + fileToDelete.getFileName() + " - " + e.getMessage());
                        log.warn("删除不必要文件失败: {}", fileToDelete, e);
                    }
                }
            }
            
            status.getMessages().add(getCurrentTime() + " - 文件清理完成，共删除 " + deletedCount + " 个不必要文件");
            log.info("不必要文件删除完成，共删除 {} 个文件", deletedCount);
            
        } catch (Exception e) {
            status.getMessages().add(getCurrentTime() + " - 删除不必要文件失败: " + e.getMessage());
            log.error("删除不必要文件失败", e);
            // 不抛出异常，继续执行后续步骤
        }
    }


    /**
     * 初始化升级状态
     */
    protected UpgradeStatus initializeUpgradeStatus(UpgradeRequest request) {
        UpgradeStatus status = new UpgradeStatus();
        status.setUpgradeId(generateUpgradeId());
        status.setServiceName(request.getServiceName());
        status.setEnvironment(request.getEnvironment());
        status.setStatus(UpgradeStatus.Status.RUNNING);
        status.setProgress(0);
        status.setCurrentStep("初始化");
        status.setStartTime(java.time.LocalDateTime.now());
        return status;
    }

    /**
     * 更新步骤状态
     */
    protected UpgradeStatus updateStep(UpgradeStatus status, int step, String message) {
        status.setCurrentStep(step + "/7: " + message);
        status.setProgress((step - 1) * 100 / 7);
        status.getMessages().add(getCurrentTime() + " - " + message);
        log.info("升级进度[{}] - 步骤{}: {}", status.getUpgradeId(), step, message);
        return status;
    }

    /**
     * 完成升级
     */
    protected UpgradeStatus completeUpgrade(UpgradeStatus status) {
        status.setStatus(UpgradeStatus.Status.SUCCESS);
        status.setProgress(100);
        status.setCurrentStep("升级完成");
        status.setEndTime(java.time.LocalDateTime.now());
        status.getMessages().add(getCurrentTime() + " - 升级流程已完成");
        
        // 处理未解析的环境变量
        handleUnresolvedEnvVars(status);
        
        // 清理项目工作目录
        cleanupWorkspaceDirectory(status);
        
        log.info("升级完成[{}] - 服务: {}, 耗时: {}ms", 
                status.getUpgradeId(), 
                status.getServiceName(), 
                java.time.Duration.between(status.getStartTime(), status.getEndTime()).toMillis());
        GitRepository repository = (GitRepository) status.getContext().get("repository");
        repository.close();
        return status;
    }

    /**
     * 清理项目工作目录
     */
    protected void cleanupWorkspaceDirectory(UpgradeStatus status) {
        try {
            // 获取项目工作路径
            Path servicePath = (Path) status.getContext().get("upgrade_service_path");
            
            if (servicePath != null && java.nio.file.Files.exists(servicePath)) {
                log.info("开始清理项目工作目录: {}", servicePath);
                
                // 递归删除整个项目目录
                java.nio.file.Files.walk(servicePath)
                    .sorted(java.util.Comparator.reverseOrder()) // 先删除文件再删除目录
                    .forEach(path -> {
                        try {
                            java.nio.file.Files.delete(path);
                            log.debug("删除: {}", path);
                        } catch (Exception e) {
                            log.warn("删除文件失败: {}, 错误: {}", path, e.getMessage());
                        }
                    });
                
                // 验证目录是否已删除
                if (!java.nio.file.Files.exists(servicePath)) {
                    status.getMessages().add(getCurrentTime() + " - 项目工作目录已清理");
                    log.info("项目工作目录清理成功: {}", servicePath);
                } else {
                    status.getMessages().add(getCurrentTime() + " - 项目工作目录清理可能不完整");
                    log.warn("项目工作目录清理可能不完整: {}", servicePath);
                }
                
            } else {
                log.debug("项目工作路径不存在或为空，跳过清理: {}", servicePath);
            }
            
        } catch (Exception e) {
            status.getMessages().add(getCurrentTime() + " - 清理项目工作目录时出错: " + e.getMessage());
            log.error("清理项目工作目录失败", e);
            // 不抛出异常，因为清理失败不应该影响升级结果
        }
    }

    /**
     * 处理升级异常
     */
    protected UpgradeStatus handleUpgradeException(UpgradeStatus status, Exception e) {
        status.setStatus(UpgradeStatus.Status.FAILED);
        status.setEndTime(java.time.LocalDateTime.now());
        status.getMessages().add(getCurrentTime() + " - 升级失败: " + e.getMessage());
        log.error("升级失败[{}]", status.getUpgradeId(), e);
        return status;
    }

    /**
     * 生成升级ID
     */
    protected String generateUpgradeId() {
        return "UPG" + System.currentTimeMillis();
    }

    /**
     * 获取当前时间字符串
     */
    protected String getCurrentTime() {
        return java.time.LocalDateTime.now().format(
                java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    // 抽象方法 - 子类必须实现的具体步骤

    /**
     * 读取Excel配置文件获取环境变量
     */
    protected abstract void readExcelConfiguration(UpgradeRequest request, UpgradeStatus status) throws Exception;

    /**
     * 拉取Git仓库并创建升级分支
     */
    protected abstract void prepareGitRepository(UpgradeRequest request, UpgradeStatus status) throws Exception;

    /**
     * 搜索application.properties文件
     */
    protected abstract void searchApplicationProperties(UpgradeRequest request, UpgradeStatus status) throws Exception;

    /**
     * 将K8s环境变量替换为Nacos配置
     */
    protected abstract void replaceK8sWithNacosConfig(UpgradeRequest request, UpgradeStatus status) throws Exception;

    /**
     * 生成bootstrap和POM文件
     */
    protected abstract void generateBootstrapAndPom(UpgradeRequest request, UpgradeStatus status) throws Exception;

    /**
     * 提交代码并推送到远程分支
     */
    protected abstract void commitAndPush(UpgradeRequest request, UpgradeStatus status) throws Exception;

    /**
     *  发布配置到aliyun mse nacos
     */
    protected abstract void publishNacos(UpgradeRequest request, UpgradeStatus status);


    /**
     * 处理未解析的环境变量
     */
    protected abstract void handleUnresolvedEnvVars(UpgradeStatus status);
}