package com.just.template.core;

import com.just.template.exception.TemplateException;

import java.util.Map;

/**
 * 模板引擎接口
 * 使用类型安全的TypedTemplateContext
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
public interface TemplateEngine {
    
    /**
     * 处理模板
     * 
     * @param templateName 模板名称
     * @param context 类型安全的模板上下文
     * @return 处理后的内容
     * @throws TemplateException 模板处理异常
     */
    String process(String templateName, TypedTemplateContext context) throws TemplateException;
    
    /**
     * 处理字符串模板
     * 
     * @param templateContent 模板内容
     * @param context 类型安全的模板上下文
     * @return 处理后的内容
     * @throws TemplateException 模板处理异常
     */
    String processString(String templateContent, TypedTemplateContext context) throws TemplateException;
    
    /**
     * 处理模板（兼容Map参数）
     * 
     * @param templateName 模板名称
     * @param data 模板数据
     * @return 处理后的内容
     * @throws TemplateException 模板处理异常
     */
    default String process(String templateName, Map<String, Object> data) throws TemplateException {
        TypedTemplateContext context = TypedTemplateContext.empty();
        if (data != null) {
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                context.put(entry.getKey(), entry.getValue());
            }
        }
        return process(templateName, context);
    }
    
    /**
     * 检查模板是否存在
     * 
     * @param templateName 模板名称
     * @return 是否存在
     */
    boolean templateExists(String templateName);
} 