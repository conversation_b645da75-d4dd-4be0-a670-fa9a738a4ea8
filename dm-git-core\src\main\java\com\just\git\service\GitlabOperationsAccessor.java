package com.just.git.service;

import com.just.git.enums.EnvPathEnum;
import org.apache.commons.lang3.tuple.Pair;
import org.eclipse.jgit.api.AddCommand;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.RmCommand;
import org.eclipse.jgit.api.Status;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.lib.Ref;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.gitlab4j.api.GitLabApi;
import org.gitlab4j.api.GitLabApiException;
import org.gitlab4j.api.models.*;
import org.yaml.snakeyaml.Yaml;

import javax.annotation.Nullable;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.function.Predicate;

public class GitlabOperationsAccessor implements ******************** {

    private final GitLabApi gitLabApi;

    private final String serviceName;
    private final File gitFile;
    private final LogStore logStore;
    private final UsernamePasswordCredentialsProvider usernamePasswordCredentialsProvider;

    private Git git;

    public GitlabOperationsAccessor(String gitlabUrl,
                                    String gitlabToken,
                                    String serviceName,
                                    @Nullable Path path,
                                    @Nullable LogStore logStore) throws GitLabApiException, GitAPIException, IOException {
        this.gitLabApi = new GitLabApi(gitlabUrl, gitlabToken);
        usernamePasswordCredentialsProvider = new UsernamePasswordCredentialsProvider("PRIVATE-TOKEN", gitlabToken);
        this.serviceName = serviceName;
        if (logStore == null) {
            this.logStore = new LogStore();
        } else {
            this.logStore = logStore;
        }

        if (path == null) {
            File tempDir = Files.createTempDirectory("git-clone-").toFile();
            tempDir.deleteOnExit();
            gitFile = new File(tempDir, serviceName);
            gitFile.mkdirs();
        } else {
            this.gitFile = path.toFile();
        }
        this.gitClone();
    }

    public GitLabApi getGitLabApi() {
        return gitLabApi;
    }

    @Override
    public Git gitClone() throws GitLabApiException, GitAPIException, IOException {
        if (this.git != null) {
            return this.git;
        }
        int projectId = getProjectId(serviceName);
        Project project = gitLabApi.getProjectApi().getProject(projectId);
        // 获取克隆URL
        String cloneUrl = project.getHttpUrlToRepo().replace("git.innodealing.cn", "172.16.100.23");
        // 5. 执行clone
        if (isGitRepository(gitFile)) {
            return this.git = Git.open(gitFile);
        }
        return this.git = Git.cloneRepository()
                .setURI(cloneUrl)
                .setDirectory(gitFile)
                .setBranch("master")
                .setCredentialsProvider(usernamePasswordCredentialsProvider)
                .call();
    }

    @Override
    public void set(Git git) {
        this.git = git;
    }

    private String mvnPackage(Path path) throws Exception {
        ProcessBuilder processBuilder = new ProcessBuilder();
        processBuilder.directory(path.toFile());
        Map<String, String> environment = processBuilder.environment();
        final boolean isWindows = System.getProperty("os.name")
                .toLowerCase().contains("win");
        if (isWindows) {
            String mvnCmd = "";
            String[] split = (environment.get("Path") == null ? environment.get("PATH") : environment.get("Path")).split(";");
            for (String string : split) {
                if (string.contains("maven") && string.contains("bin")) {
                    mvnCmd = string;
                }
            }
            processBuilder.command(mvnCmd + "\\mvn.cmd", "clean", "package", "-DskipTests=true");
        } else {
            processBuilder.command("mvn", "clean", "package", "-DskipTests=true");
        }

        String cmdStr = String.join(" ", processBuilder.command());
        logStore.info(cmdStr);
        processBuilder.redirectErrorStream(true);
        Process process = processBuilder.start();

        StringBuilder mvnLog = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                logStore.info(line);
                mvnLog.append(line);
            }
        }
        process.waitFor();
        return mvnLog.toString();
    }

    @Override
    public Pair<Boolean, String> checkRunning(Path path, @Nullable EnvPathEnum env) throws Exception {
        logStore.setKey("mvn package");
        String mvnLog = mvnPackage(path);
        if (mvnLog.contains("ERROR")) {
            throw new Exception("mvn log contains ERROR");
        }

        logStore.setKey("java run " + (env == null ? "default" : env.getFilePath()));
        ProcessBuilder runBuilder = new ProcessBuilder();
        File file = path.toFile().toPath().resolve("target").toFile();
        File jarFile = null;
        for (File listFile : Objects.requireNonNull(file.listFiles())) {
            if (listFile.getName().endsWith(".jar")) {
                jarFile = listFile;
            }
        }
        runBuilder.directory(jarFile.getParentFile());

        runBuilder.redirectErrorStream(true);
        Map<String, String> environment = runBuilder.environment();
        environment.putAll(this.getDefaultEvnMap());
        if (env != null) {
            environment.putAll(this.getRemoteEvnMap(env));
        }
//        -XX:TieredStopAtLevel=1	跳过高阶 JIT，加快启动
//        -XX:+UseSerialGC	轻量级 GC，启动更快
        runBuilder.command("java", "-jar", "-XX:TieredStopAtLevel=1", "-XX:+UseSerialGC", jarFile.getName());
        String cmdStr = String.join(" ", runBuilder.command());
        logStore.info(cmdStr);
        Process start = runBuilder.start();

        StringBuilder output = new StringBuilder();
        boolean success = false;
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(start.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                logStore.info(line);
                output.append(line);
                if (line.contains("Tomcat started on port(s): 8080")) {
                    success = true;
                    break;
                }
            }
        }
        if (start.isAlive()) {
            // 强制终止
            start.destroy();
        }
        return Pair.of(success, output.toString());
    }

    @Override
    public Git switchBranch(String branchName) throws Exception {
        // 1. 检查 branchName 分支是否存在
        boolean branchExists = false;
        List<Ref> branches = git.branchList().call();
        for (Ref branch : branches) {
            if (branch.getName().equals("refs/heads/" + branchName)) {
                branchExists = true;
                break;
            }
        }

        // 2. 如果不存在，则创建并检出
        if (!branchExists) {
            logStore.info(branchName + " 分支不存在，正在创建...");
            git.branchCreate()
                    .setName(branchName)
                    .call();
        }

        // 3. 检出 branchName 分支（无论是否存在）
        git.checkout()
                .setName(branchName)
                .call();

        logStore.info("当前分支: " + branchName);
        return this.git = git;
    }

    @Override
    public MergeRequest commitAndPush(String title, String commitMessage) throws Exception {
        Status status = git.status().call();

        Predicate<String> shouldIgnore = file ->
                file.startsWith("PUBLIC_INTER_DMLOG_IS_UNDEFINED/") || file.equals("PUBLIC_INTER_DMLOG_IS_UNDEFINED");

        // 添加新增和修改的文件
        AddCommand add = git.add();
        for (String file : status.getAdded()) {
            if (!shouldIgnore.test(file)) {
                add.addFilepattern(file);
            }
        }
        for (String file : status.getModified()) {
            if (!shouldIgnore.test(file)) {
                add.addFilepattern(file);
            }
        }
        for (String file : status.getUntracked()) {
            if (!shouldIgnore.test(file)) {
                add.addFilepattern(file);
            }
        }
        add.call();

        // 删除已删除的文件和丢失的文件
        RmCommand rm = git.rm();
        boolean hasFilesToRemove = false;
        
        // 处理已标记为删除的文件
        for (String file : status.getRemoved()) {
            rm.addFilepattern(file);
            hasFilesToRemove = true;
        }
        
        // 处理在工作区中丢失但仍被Git跟踪的文件
        for (String file : status.getMissing()) {
            rm.addFilepattern(file);
            hasFilesToRemove = true;
            logStore.info("删除丢失的文件: " + file);
        }
        
        if (hasFilesToRemove) {
            rm.call();
        }

        git.commit().setMessage(commitMessage).call();

        git.push().setCredentialsProvider(usernamePasswordCredentialsProvider).call();
        String string = git.getRepository().getDirectory().toPath().getParent().getFileName().toString();
        int projectId = this.getProjectId(string);
        // this me
        int userid = 130;

        // path 可以为null
        List<Commit> commits = gitLabApi.getCommitsApi().getCommits(projectId);
        Optional<User> userOpt = getUser(commits);
        if (userOpt.isPresent()) {
            userid = userOpt.get().getId();
            logStore.info("将该merge request自动分配给代码提交最多的人 :" + userOpt.get().getName());
        }
        try {
            MergeRequest master = gitLabApi.getMergeRequestApi().createMergeRequest(projectId, git.getRepository().getBranch(), "master", title, commitMessage, userid);
            String webUrl = master.getWebUrl();
            logStore.info(webUrl);
            return master;
        } catch (Exception e) {
            if (!e.getMessage().contains("This merge request already exists")) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    private Optional<User> getUser(List<Commit> commits) throws GitLabApiException {
        // 2. 统计提交次数
        Map<String, Integer> authorCount = new HashMap<>();
        Map<String, String> emailMap = new HashMap<>();
        for (Commit commit : commits) {
            String name = commit.getAuthorName();
            String email = commit.getAuthorEmail();
            authorCount.put(name, authorCount.getOrDefault(name, 0) + 1);
            emailMap.put(name, email);
        }
        authorCount.remove("wz2cool");
        authorCount.remove("王喆");
        authorCount.remove("frank");

        // 3. 找出最多提交的人
        String topAuthor = Collections.max(authorCount.entrySet(), Map.Entry.comparingByValue()).getKey();
        String topEmail = emailMap.get(topAuthor);
        System.out.println("Top Committer: " + topAuthor + " <" + topEmail + ">");

        // 4. 查找 GitLab 用户（尝试通过邮箱）
        List<User> users = gitLabApi.getUserApi().findUsers(topAuthor); // 也可以用 email 搜索
        return users.stream().findFirst();
    }

    @Override
    public int getProjectId(String serviceName) throws GitLabApiException {
        List<Project> projects = gitLabApi.getProjectApi().getProjects(serviceName);
        for (Project project : projects) {
            if (project.getName().equals(serviceName)) {
                return project.getId();
            }
        }
        throw new IllegalArgumentException("not found project: " + serviceName);
    }

    public void createMergeRequestNote(Integer mergeRequestIid, String text) throws GitLabApiException {
        gitLabApi.getNotesApi().createMergeRequestNote(getProjectId(serviceName), mergeRequestIid, text);
    }

    @Override
    public int getProjectId() throws GitLabApiException {
        List<Project> projects = gitLabApi.getProjectApi().getProjects(serviceName);
        for (Project project : projects) {
            if (project.getName().equals(serviceName)) {
                return project.getId();
            }
        }
        throw new IllegalArgumentException("not found project: " + serviceName);
    }

    // 检查目录是否是 Git 仓库
    @Override
    public boolean isGitRepository(File dir) {
        return new File(dir, ".git").exists(); // 检查是否存在 .git 目录
    }

    @Override
    public boolean isGitRepository() {
        return new File(gitFile, ".git").exists(); // 检查是否存在 .git 目录
    }

    @Override
    public Map<String, String> getRemoteEvnMap(EnvPathEnum envPathEnum) throws Exception {
        int projectId = getProjectId("config-map-cloud");
        RepositoryFile master = gitLabApi.getRepositoryFileApi().getFile(projectId, envPathEnum.getFilePath(), "master");
        Yaml yaml = new Yaml();
        Map<String, Object> load = yaml.load(new String(Base64.getDecoder().decode(master.getContent())));
        Map<String, String> data = (Map<String, String>) load.get("data");
        data.putAll(this.getDefaultEvnMap());
        return data;
    }

    @Override
    public Map<String, String> getDefaultEvnMap() throws IOException {
        Map<String, String> data = new HashMap<>();
        Path resolve = Paths.get(System.getProperty("user.dir")).resolve("log").resolve(serviceName);
        Files.createDirectories(resolve);
        data.put("PUBLIC_DMLOG", resolve.toString());
        data.put("PUBLIC_INTER_DMLOG", resolve.toString());
        return data;
    }
}
