package com.just.nacos;

import com.just.nacos.model.NacosConfig;
import com.just.nacos.model.NacosEnvironment;
import com.just.nacos.service.NacosConfigService;
import com.just.nacos.service.NacosEnvironmentService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 改进版的NacosConfigService测试类
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class NacosConfigServiceImprovedTest {

    @Autowired
    private NacosConfigService nacosConfigService;
    
    @Autowired
    private NacosEnvironmentService nacosEnvironmentService;

    private static final String TEST_NAMESPACE = "dev";
    private static final String TEST_GROUP = "dev";
    private static final String TEST_DATA_ID = "improved-test-config.properties";
    private static final String TEST_CONTENT = "# 改进版测试配置文件\n" +
            "app.name=improved-test-application\n" +
            "app.version=2.0.0\n" +
            "app.environment=dev\n" +
            "app.profile=test\n" +
            "\n" +
            "# 数据库配置\n" +
            "spring.datasource.url=**********************************************" +
            "spring.datasource.username=test_user\n" +
            "spring.datasource.password=test_password\n" +
            "spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver\n" +
            "\n" +
            "# Redis配置\n" +
            "spring.redis.host=localhost\n" +
            "spring.redis.port=6379\n" +
            "spring.redis.database=1\n" +
            "spring.redis.timeout=3000\n" +
            "\n" +
            "# 服务配置\n" +
            "server.port=8080\n" +
            "management.endpoints.web.exposure.include=health,info,metrics";

    @BeforeEach
    public void setUp() {
        log.info("========== 测试环境准备 ==========");
        
        // 检查环境服务状态
        List<NacosEnvironment> environments = nacosEnvironmentService.listAvailableEnvironments();
        log.info("可用环境数量: {}", environments.size());
        
        for (NacosEnvironment env : environments) {
            log.info("环境: {} - 命名空间: {} - 状态: {}", 
                    env.getName(), env.getNamespace(), env.getStatus());
        }
        
        // 检查默认环境
        Optional<NacosEnvironment> defaultEnv = nacosEnvironmentService.getDefaultEnvironment();
        if (defaultEnv.isPresent()) {
            log.info("默认环境: {}", defaultEnv.get().getName());
        } else {
            log.warn("没有设置默认环境");
        }
    }

    @Test
    public void testEnvironmentSetup() {
        log.info("========== 测试环境设置 ==========");
        
        // 验证环境服务可用
        assertNotNull(nacosEnvironmentService, "环境服务应该已注入");
        
        // 验证至少有一个可用环境
        List<NacosEnvironment> environments = nacosEnvironmentService.listAvailableEnvironments();
        assertFalse(environments.isEmpty(), "应该至少有一个可用环境");
        
        // 验证dev环境存在
        Optional<NacosEnvironment> devEnv = nacosEnvironmentService.getEnvironment("dev");
        assertTrue(devEnv.isPresent(), "dev环境应该存在");
        assertEquals(TEST_NAMESPACE, devEnv.get().getNamespace(), "dev环境的命名空间应该正确");
        
        log.info("环境设置验证通过");
    }

    @Test
    public void testServiceDependencyInjection() {
        log.info("========== 测试服务依赖注入 ==========");
        
        assertNotNull(nacosConfigService, "配置服务应该已注入");
        assertNotNull(nacosEnvironmentService, "环境服务应该已注入");
        
        log.info("服务依赖注入验证通过");
    }

    @Test
    public void testPublishAndRetrieveConfig() {
        log.info("========== 测试发布和获取配置 ==========");
        
        try {
            // 1. 创建测试配置
            NacosConfig config = createTestConfig();
            
            // 2. 发布配置
            log.info("正在发布配置: {}", TEST_DATA_ID);
            boolean publishResult = nacosConfigService.publishConfig(config);
            
            if (publishResult) {
                log.info("配置发布成功");
                
                // 3. 尝试获取配置
                log.info("尝试获取已发布的配置");
                Optional<NacosConfig> retrievedConfig = nacosConfigService.getConfig(
                        TEST_DATA_ID, TEST_GROUP, TEST_NAMESPACE);
                
                if (retrievedConfig.isPresent()) {
                    log.info("配置获取成功: {}", retrievedConfig.get().getDataId());
                    assertEquals(TEST_DATA_ID, retrievedConfig.get().getDataId());
                } else {
                    log.warn("配置获取失败，可能是由于MSE API的延迟或权限问题");
                }
                
            } else {
                log.warn("配置发布失败，可能是由于MSE API权限或网络问题");
            }
            
        } catch (Exception e) {
            log.error("测试过程中出现异常", e);
            fail("测试不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testListConfigsWithErrorHandling() {
        log.info("========== 测试获取配置列表（带错误处理）==========");
        
        try {
            // 测试获取dev环境的配置列表
            List<NacosConfig> configs = nacosConfigService.listConfigs(1, 1000, TEST_GROUP, TEST_NAMESPACE);
            
            assertNotNull(configs, "配置列表不应该为null");
            log.info("成功获取配置列表，数量: {}", configs.size());
            
            // 显示前几个配置的信息
            configs.stream()
                    .limit(5)
                    .forEach(config -> log.info("配置: {} - 组: {} - 类型: {}", 
                            config.getDataId(), config.getGroup(), config.getType()));
            
            // 测试获取不存在环境的配置
            List<NacosConfig> nonExistentConfigs = nacosConfigService.listConfigs(
                    1, 10, null, "non-existent-namespace");
            assertNotNull(nonExistentConfigs, "即使环境不存在，也应该返回空列表而不是null");
            assertTrue(nonExistentConfigs.isEmpty(), "不存在的环境应该返回空配置列表");
            
        } catch (Exception e) {
            log.error("获取配置列表失败", e);
            fail("获取配置列表不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testConfigFormatValidation() {
        log.info("========== 测试配置格式验证 ==========");
        
        // 测试Properties格式
        testValidationCase("properties", "app.name=test\napp.version=1.0", true);
        testValidationCase("properties", "invalid=", false);
        
        // 测试JSON格式
        testValidationCase("json", "{\"name\":\"test\",\"version\":\"1.0\"}", true);
        testValidationCase("json", "{\"name\":\"test\"", false);
        
        // 测试YAML格式
        testValidationCase("yaml", "name: test\nversion: 1.0", true);
        testValidationCase("yaml", "name: test\n  invalid yaml", false);
        
        // 测试XML格式
        testValidationCase("xml", "<?xml version=\"1.0\"?><root><name>test</name></root>", true);
        testValidationCase("xml", "not xml content", false);
        
        log.info("配置格式验证测试完成");
    }

    @Test
    public void testBatchOperations() {
        log.info("========== 测试批量操作 ==========");
        
        try {
            // 创建多个测试配置
            List<NacosConfig> batchConfigs = Arrays.asList(
                    createTestConfig("batch-config-1.properties", "batch.config1=value1"),
                    createTestConfig("batch-config-2.properties", "batch.config2=value2"),
                    createTestConfig("batch-config-3.properties", "batch.config3=value3")
            );
            
            // 测试批量发布
            log.info("测试批量发布配置");
            int publishCount = nacosConfigService.batchPublishConfigs(batchConfigs);
            log.info("批量发布结果: {}/{}", publishCount, batchConfigs.size());
            
            // 测试批量删除
            log.info("测试批量删除配置");
            int deleteCount = nacosConfigService.batchDeleteConfigs(batchConfigs);
            log.info("批量删除结果: {}/{}", deleteCount, batchConfigs.size());
            
        } catch (Exception e) {
            log.error("批量操作测试失败", e);
            fail("批量操作不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testConfigExists() {
        log.info("========== 测试配置存在性检查 ==========");
        
        try {
            // 检查一个确实不存在的配置
            boolean notExists = nacosConfigService.configExists(
                    "definitely-non-existent-config-" + System.currentTimeMillis(), 
                    TEST_GROUP, TEST_NAMESPACE);
            assertFalse(notExists, "不存在的配置应该返回false");
            
            log.info("配置存在性检查测试通过");
            
        } catch (Exception e) {
            log.error("配置存在性检查失败", e);
            fail("配置存在性检查不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testErrorHandling() {
        log.info("========== 测试错误处理 ==========");
        
        try {
            // 测试空配置对象
            assertThrows(Exception.class, () -> {
                nacosConfigService.publishConfig(null);
            }, "发布空配置应该抛出异常");
            
            // 测试无效配置
            NacosConfig invalidConfig = NacosConfig.builder()
                    .dataId("")  // 空dataId
                    .content("test")
                    .build();
            
            assertThrows(Exception.class, () -> {
                nacosConfigService.publishConfig(invalidConfig);
            }, "发布无效配置应该抛出异常");
            
            log.info("错误处理测试通过");
            
        } catch (Exception e) {
            log.error("错误处理测试失败", e);
            fail("错误处理测试异常: " + e.getMessage());
        }
    }

    // === 辅助方法 ===

    private NacosConfig createTestConfig() {
        return createTestConfig(TEST_DATA_ID, TEST_CONTENT);
    }

    private NacosConfig createTestConfig(String dataId, String content) {
        return NacosConfig.builder()
                .dataId(dataId)
                .group(TEST_GROUP)
                .namespace(TEST_NAMESPACE)
                .content(content)
                .type("properties")
                .environment("dev")
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .status(NacosConfig.ConfigStatus.ACTIVE)
                .build();
    }

    private void testValidationCase(String type, String content, boolean shouldPass) {
        String result = nacosConfigService.validateConfigFormat(content, type);
        log.info("{} 格式验证 '{}': {}", type.toUpperCase(), 
                content.length() > 20 ? content.substring(0, 20) + "..." : content, result);
        
        if (shouldPass) {
            assertEquals("格式验证通过", result, type + " 格式应该验证通过");
        } else {
            assertTrue(result.contains("格式验证失败"), type + " 格式应该验证失败");
        }
    }
} 