package com.just.git.service;

import lombok.Setter;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.DecimalFormat;

public class LogStore {
    @Setter
    private String key = "";

    @Setter
    private double steps = 1;

    @Setter
    private int allSteps;

    private final StringBuilder log = new StringBuilder();
    private final DecimalFormat df = new DecimalFormat("0.##");

    public void info(String msg) {
        String l = String.format("【%s/%d %s】%s", df.format(steps), allSteps, key, msg);
        log.append(l).append("\n");
        System.out.println(l);
    }

    public void error(String msg) {
        String l = String.format("【%s/%d %s】%s", df.format(steps), allSteps, key, msg);
        log.append(l).append("\n");
        System.err.println(l);
    }

    public void store(Path path) throws IOException {
        Files.write(path, log.toString().getBytes());
    }

    @Override
    public String toString() {
        return log.toString();
    }
}
