# DM Git Core

dm-auto-utils项目的Git操作核心模块，基于JGit提供类型安全的Git操作服务。

## 模块概述

dm-git-core是一个现代化的Git操作库，旨在替换基于gitlab4j-api的混合实现方案。该模块提供了：

- **类型安全的Git操作API**
- **基于JGit的纯Java实现**
- **Spring Boot自动配置**
- **统一的异常处理机制**
- **丰富的配置选项**

## 核心特性

### 🎯 类型安全的操作模型
```java
// 克隆请求
GitCloneRequest cloneRequest = GitCloneRequest.builder()
    .serviceName("user-service")
    .localPath(Paths.get("/workspace/user-service"))
    .branch("develop")
    .credentials(GitCredentials.ofToken("your-token"))
    .build();

GitRepository repository = gitService.cloneRepository(cloneRequest);
```

### 🔧 支持多种认证方式
```java
// Token认证（推荐）
GitCredentials tokenAuth = GitCredentials.ofToken("ghp_xxxxxxxxxxxx");

// 用户名密码认证
GitCredentials userAuth = GitCredentials.ofUsernamePassword("username", "password");

// SSH密钥认证（规划中）
GitCredentials sshAuth = GitCredentials.ofSshKey("/path/to/private/key", "passphrase");
```

### 🌿 完整的分支操作
```java
// 切换分支
GitBranchRequest switchReq = GitBranchRequest.createSwitchRequest("feature-branch");
GitOperationResult result = gitService.branchOperation(repository, switchReq);

// 创建新分支
GitBranchRequest newBranchReq = GitBranchRequest.createNewBranchRequest("hotfix-123", "master");
GitOperationResult result = gitService.branchOperation(repository, newBranchReq);
```

### 📝 智能的提交操作
```java
// 简单提交
GitCommitRequest commitReq = GitCommitRequest.createSimple("fix: 修复用户登录问题");
GitOperationResult result = gitService.commit(repository, commitReq);

// 带推送的提交
GitCommitRequest commitWithPush = GitCommitRequest.createWithPush("feat: 新增功能", "develop");
GitOperationResult result = gitService.commit(repository, commitWithPush);
```

## 架构设计

```
dm-git-core/
├── config/                 # 配置管理
│   ├── GitConfig.java             # Git配置类
│   ├── GitCoreProperties.java     # Spring Boot配置属性
│   └── GitCoreAutoConfiguration.java  # 自动配置
├── exception/              # 异常定义
│   ├── GitOperationException.java # Git操作异常
│   └── GitErrorCode.java         # 错误码枚举
├── model/                  # 数据模型
│   ├── GitCredentials.java       # 认证信息
│   ├── GitCloneRequest.java      # 克隆请求
│   ├── GitBranchRequest.java     # 分支操作请求
│   ├── GitCommitRequest.java     # 提交请求
│   ├── GitRepository.java        # 仓库封装
│   └── GitOperationResult.java   # 操作结果
├── service/                # 服务层
│   ├── GitOperationService.java  # 服务接口
│   └── JGitOperationServiceImpl.java  # JGit实现
└── utils/                  # 工具类
    └── GitUtils.java             # Git工具函数
```

## 快速开始

### 1. 添加依赖

在你的`pom.xml`中添加：

```xml
<dependency>
    <groupId>com.just</groupId>
    <artifactId>dm-git-core</artifactId>
    <version>2.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 配置属性

在`application.yml`中配置：

```yaml
dm:
  git:
    base-url: http://gitlab.company.com
    default-branch: master
    default-timeout-seconds: 30
    workspace-directory: /tmp/git-workspace
    auto-create-workspace: true
    verbose-logging: false
    default-user-name: dm-auto-utils
    default-user-email: <EMAIL>
```

### 3. 使用服务

```java
@Autowired
private GitOperationService gitService;

@Autowired 
private GitConfig gitConfig;

public void exampleUsage() {
    // 配置认证
    GitCredentials credentials = GitCredentials.ofToken("your-token");
    
    // 创建克隆请求
    GitCloneRequest request = GitCloneRequest.builder()
        .serviceName("user-service")
        .localPath(gitConfig.getServiceWorkspacePath("user-service"))
        .branch("master")
        .credentials(credentials)
        .build();
    
    try {
        // 克隆仓库
        GitRepository repository = gitService.cloneRepository(request);
        
        // 检查状态
        boolean hasChanges = gitService.hasUncommittedChanges(repository);
        String currentBranch = gitService.getCurrentBranch(repository);
        
        // 清理资源
        gitService.closeRepository(repository);
        
    } catch (GitOperationException e) {
        log.error("Git操作失败: {}", e.getMessage(), e);
    }
}
```

## 配置选项

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `dm.git.base-url` | `http://gitlab.company.com` | Git服务器基础URL |
| `dm.git.default-branch` | `master` | 默认分支名称 |
| `dm.git.default-timeout-seconds` | `30` | 默认操作超时时间 |
| `dm.git.workspace-directory` | `~/git-workspace` | 本地工作目录 |
| `dm.git.auto-create-workspace` | `true` | 是否自动创建工作目录 |
| `dm.git.verbose-logging` | `false` | 是否启用详细日志 |
| `dm.git.default-user-name` | `dm-auto-utils` | 默认提交者姓名 |
| `dm.git.default-user-email` | `<EMAIL>` | 默认提交者邮箱 |

## 异常处理

所有Git操作都可能抛出`GitOperationException`，该异常包含：

- **错误码**: `GitErrorCode`枚举值
- **操作类型**: 失败的操作名称
- **详细消息**: 可读的错误描述
- **原始异常**: JGit的底层异常

```java
try {
    GitRepository repo = gitService.cloneRepository(request);
} catch (GitOperationException e) {
    GitErrorCode errorCode = e.getErrorCode();
    String operation = e.getOperation();
    
    switch (errorCode) {
        case AUTHENTICATION_FAILED:
            log.error("认证失败，请检查Token: {}", e.getMessage());
            break;
        case NETWORK_ERROR:
            log.error("网络连接失败: {}", e.getMessage());
            break;
        case REPOSITORY_NOT_FOUND:
            log.error("仓库不存在: {}", e.getMessage());
            break;
        default:
            log.error("Git操作失败: {}", e.getMessage());
    }
}
```

## 最佳实践

### 1. 资源管理
```java
GitRepository repository = null;
try {
    repository = gitService.cloneRepository(request);
    // ... 使用仓库
} finally {
    // 始终关闭资源
    gitService.closeRepository(repository);
}
```

### 2. 认证管理
```java
// 推荐使用Token认证
GitCredentials credentials = GitCredentials.ofToken(tokenFromConfig);

// 验证认证信息
if (!credentials.isValid()) {
    throw new IllegalArgumentException("无效的认证信息");
}
```

### 3. 错误处理
```java
// 验证请求参数
request.validate();

// 验证仓库状态
List<String> issues = gitService.validateRepository(repository);
if (!issues.isEmpty()) {
    log.warn("仓库验证警告: {}", String.join(", ", issues));
}
```

## 技术栈

- **JGit 5.13.1**: Eclipse官方Git Java实现
- **Spring Boot 2.3.12**: 自动配置和依赖注入
- **Lombok**: 减少样板代码
- **SLF4J**: 统一日志接口
- **JUnit 5**: 单元测试框架

## 与原有实现的对比

| 特性 | gitlab4j-api + JGit | dm-git-core |
|------|---------------------|-------------|
| API一致性 | 混合API，不一致 | 统一的类型安全API |
| 认证方式 | 复杂配置 | 简化的认证模型 |
| 异常处理 | 分散的异常类型 | 统一的异常体系 |
| 依赖管理 | 多个外部依赖 | 单一JGit依赖 |
| 配置方式 | 硬编码配置 | Spring Boot配置 |
| 测试支持 | 难以模拟 | 完整的测试支持 |

## 迁移指南

从旧的GitlabOperationsAccessor迁移到dm-git-core：

### 旧实现
```java
GitlabOperationsAccessor accessor = new GitlabOperationsAccessor();
accessor.cloneRepository("service-name", "/path", "branch");
```

### 新实现
```java
GitCloneRequest request = GitCloneRequest.builder()
    .serviceName("service-name")
    .localPath(Paths.get("/path"))
    .branch("branch")
    .credentials(credentials)
    .build();

GitRepository repository = gitService.cloneRepository(request);
```

## 开发计划

### v2.0.0 (当前版本)
- ✅ 基础Git操作（克隆、分支、提交、推送、拉取）
- ✅ 类型安全的API设计
- ✅ Spring Boot自动配置
- ✅ 基础测试覆盖

### v2.1.0 (计划中)
- 🔄 SSH密钥认证支持
- 🔄 高级合并策略
- 🔄 Git Hooks支持
- 🔄 并发操作支持

### v2.2.0 (计划中)  
- 🔄 Git LFS支持
- 🔄 子模块操作
- 🔄 性能优化
- 🔄 监控指标

## 贡献指南

1. Fork项目
2. 创建特性分支: `git checkout -b feature/amazing-feature`
3. 提交更改: `git commit -m 'Add some amazing feature'`
4. 推送分支: `git push origin feature/amazing-feature`
5. 创建Pull Request

## 许可证

本项目采用MIT许可证 - 查看[LICENSE](../LICENSE)文件了解详情。

## 联系方式

- 项目地址: https://gitlab.company.com/microservice/dm-auto-utils
- 问题反馈: https://gitlab.company.com/microservice/dm-auto-utils/issues
- 开发团队: <EMAIL> 