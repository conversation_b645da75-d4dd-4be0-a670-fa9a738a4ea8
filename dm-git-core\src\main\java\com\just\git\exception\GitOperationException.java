package com.just.git.exception;

/**
 * Git操作异常
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
public class GitOperationException extends Exception {
    
    private final GitErrorCode errorCode;
    private final String operation;
    
    public GitOperationException(GitErrorCode errorCode, String operation, String message) {
        super(message);
        this.errorCode = errorCode;
        this.operation = operation;
    }
    
    public GitOperationException(GitErrorCode errorCode, String operation, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.operation = operation;
    }
    
    public GitErrorCode getErrorCode() {
        return errorCode;
    }
    
    public String getOperation() {
        return operation;
    }
    
    @Override
    public String toString() {
        return String.format("GitOperationException{errorCode=%s, operation='%s', message='%s'}", 
                           errorCode, operation, getMessage());
    }
} 