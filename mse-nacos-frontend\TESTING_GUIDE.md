# 配置组下拉框功能测试指南

## 测试环境
- 开发服务器：http://localhost:3003/
- 测试页面：Nacos配置推送页面

## 测试场景

### 1. 搜索表单中的配置组测试

#### 测试步骤
1. 打开浏览器访问 http://localhost:3003/
2. 导航到"Nacos配置推送"页面
3. 找到"配置组"下拉框

#### 预期功能
- ✅ 点击下拉框显示预设配置组选项
- ✅ 输入关键字可以搜索配置组
- ✅ 可以输入自定义配置组名称
- ✅ 支持清空选择

#### 测试用例
```
测试用例1：选择预设配置组
1. 点击配置组下拉框
2. 选择"DEFAULT_GROUP"
3. 验证选择成功

测试用例2：搜索配置组
1. 点击配置组下拉框
2. 输入"SEATA"
3. 验证显示"SEATA_GROUP"选项
4. 选择该选项

测试用例3：自定义输入
1. 点击配置组下拉框
2. 输入"MY_CUSTOM_GROUP"
3. 按回车或点击其他地方
4. 验证自定义值被接受

测试用例4：清空选择
1. 选择任意配置组
2. 点击清空按钮（x图标）
3. 验证选择被清空
```

### 2. 添加配置模态框中的配置组测试

#### 测试步骤
1. 在配置列表页面点击"添加配置"按钮
2. 在弹出的模态框中找到"配置组"字段

#### 预期功能
- ✅ 与搜索表单保持一致的交互体验
- ✅ 支持选择和自定义输入
- ✅ 编辑模式下字段被禁用

#### 测试用例
```
测试用例1：新增配置时选择配置组
1. 点击"添加配置"按钮
2. 在配置组字段选择"public"
3. 填写其他必填字段
4. 验证可以正常保存

测试用例2：新增配置时输入自定义配置组
1. 点击"添加配置"按钮
2. 在配置组字段输入"TEST_GROUP_001"
3. 填写其他必填字段
4. 验证自定义配置组被接受

测试用例3：编辑模式下配置组禁用
1. 选择现有配置进行编辑
2. 验证配置组字段被禁用
3. 验证无法修改配置组值
```

### 3. 动态配置组管理测试

#### 测试步骤
1. 加载现有配置列表
2. 观察配置组选项的变化

#### 预期功能
- ✅ 系统自动从现有配置中提取配置组
- ✅ 新的配置组自动添加到选项列表
- ✅ 配置组选项去重和排序

#### 测试用例
```
测试用例1：自动提取配置组
1. 确保配置列表中有不同的配置组
2. 刷新页面或重新加载配置
3. 打开配置组下拉框
4. 验证所有配置组都出现在选项中

测试用例2：自定义配置组持久化
1. 在搜索框输入自定义配置组"TEMP_GROUP"
2. 执行搜索操作
3. 再次打开配置组下拉框
4. 验证"TEMP_GROUP"出现在选项列表中

测试用例3：配置组排序
1. 打开配置组下拉框
2. 验证选项按字母顺序排列
3. 验证没有重复选项
```

### 4. 用户体验测试

#### 测试重点
- 响应速度
- 界面友好性
- 操作便捷性

#### 测试用例
```
测试用例1：搜索响应速度
1. 在配置组字段快速输入字符
2. 验证搜索结果实时更新
3. 验证无明显延迟

测试用例2：键盘操作
1. 使用Tab键导航到配置组字段
2. 使用方向键选择选项
3. 使用回车键确认选择
4. 验证键盘操作流畅

测试用例3：移动端适配
1. 使用移动设备或开发者工具模拟
2. 测试配置组下拉框的触摸操作
3. 验证在小屏幕上的显示效果
```

## 常见问题排查

### 问题1：下拉框选项不显示
**可能原因**：
- groupOptions计算属性未正确计算
- 数据加载失败

**排查步骤**：
1. 打开浏览器开发者工具
2. 检查Console是否有错误信息
3. 检查Network面板API请求状态
4. 验证Vue DevTools中的数据状态

### 问题2：自定义输入不生效
**可能原因**：
- mode="combobox"属性缺失
- handleGroupSearch方法未正确处理

**排查步骤**：
1. 检查a-select组件的mode属性
2. 验证@search事件绑定
3. 检查handleGroupSearch方法逻辑

### 问题3：配置组重复显示
**可能原因**：
- 去重逻辑失效
- Set数据结构使用错误

**排查步骤**：
1. 检查groupOptions计算属性中的去重逻辑
2. 验证customGroups的Set操作
3. 检查extractGroupsFromConfigs方法

## 性能测试

### 测试指标
- 下拉框打开速度：< 100ms
- 搜索响应时间：< 50ms
- 选项渲染时间：< 200ms

### 测试方法
1. 使用浏览器Performance面板
2. 记录用户操作的时间消耗
3. 分析性能瓶颈

## 兼容性测试

### 浏览器兼容性
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### 设备兼容性
- 桌面端：1920x1080及以上分辨率
- 平板端：768px-1024px宽度
- 移动端：320px-768px宽度

## 测试报告模板

```
测试日期：____
测试人员：____
测试环境：____

功能测试结果：
□ 搜索表单配置组 - 通过/失败
□ 添加配置模态框 - 通过/失败
□ 动态配置组管理 - 通过/失败
□ 用户体验 - 通过/失败

发现问题：
1. ____
2. ____

建议改进：
1. ____
2. ____

总体评价：____
```

## 自动化测试建议

### E2E测试用例
```javascript
// 使用Cypress或Playwright
describe('配置组下拉框测试', () => {
  it('应该支持选择预设配置组', () => {
    cy.visit('/nacos/config')
    cy.get('[data-testid="group-select"]').click()
    cy.contains('DEFAULT_GROUP').click()
    cy.get('[data-testid="group-select"]').should('contain', 'DEFAULT_GROUP')
  })
  
  it('应该支持自定义输入配置组', () => {
    cy.visit('/nacos/config')
    cy.get('[data-testid="group-select"]').type('CUSTOM_GROUP{enter}')
    cy.get('[data-testid="group-select"]').should('contain', 'CUSTOM_GROUP')
  })
})
```

通过以上测试指南，可以全面验证配置组下拉框的功能完整性和用户体验质量。
