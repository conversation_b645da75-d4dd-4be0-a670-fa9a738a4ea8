package com.just.git;

import com.just.git.config.GitConfig;
import com.just.git.config.GitCoreAutoConfiguration;
import com.just.git.model.*;
import com.just.git.service.GitOperationService;
import com.just.git.service.GitProjectService;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;


/**
 * Git基本操作测试
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {GitCoreAutoConfiguration.class})
public class GitProjectServiceTest {

    @Resource
    private GitProjectService projectService;

    @Autowired
    private GitConfig gitConfig;


    @Test
    public void testGetAllProjects() throws ExecutionException, InterruptedException {
        demonstrateLoadAllProjects();
    }

    /**
     * 演示异步加载所有项目
     */
    private  void demonstrateLoadAllProjects() throws ExecutionException, InterruptedException {
        log.info("\n--- 演示1：异步加载所有项目 ---");

        CompletableFuture<List<GitLabProject>> future = projectService.loadAllProjects();
        List<GitLabProject> gitLabProjects = future.get();
        future.thenAccept(projects -> {
            log.info("成功加载 {} 个项目", projects.size());

            // 显示前5个项目的信息
            projects.stream()
                    .limit(5)
                    .forEach(project -> {
                        log.info("项目: {} | 路径: {} | URL: {}",
                                project.getName(),
                                project.getPath(),
                                project.getWebUrl()
                        );
                    });
        }).exceptionally(throwable -> {
            log.error("加载项目失败", throwable);
            return null;
        });

        // 等待异步操作完成（在实际应用中不建议这样做）
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
} 