# MSE Nacos 管理前端

基于 Vue 3 + Ant Design Vue 的 MSE Nacos 管理前端界面，用于集成 dm-upgrade-center 模块的 REST API 接口。

## 🚀 技术栈

- **前端框架**: Vue 3 (Composition API + `<script setup>`)
- **UI 组件库**: Ant Design Vue 4.x
- **HTTP 客户端**: Axios
- **构建工具**: Vite
- **路由**: Vue Router 4
- **日期处理**: Day.js

## 📁 项目结构

```
mse-nacos-frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API 接口封装
│   │   ├── index.js       # API 方法定义
│   │   └── request.js     # Axios 配置和拦截器
│   ├── router/            # 路由配置
│   │   └── index.js       # 路由定义
│   ├── views/             # 页面组件
│   │   ├── NacosUpgrade.vue  # Nacos 服务升级页面
│   │   └── NacosConfig.vue   # Nacos 配置管理页面
│   ├── App.vue            # 主应用组件
│   └── main.js            # 应用入口
├── index.html             # HTML 模板
├── package.json           # 项目配置
├── vite.config.js         # Vite 配置
└── README.md              # 项目说明
```

## 🎯 功能特性

### 1. Nacos 服务升级页面 (`/upgrade`)

**主要功能**:
- 🔧 **升级配置表单**: 支持环境选择、服务名称、业务组(dmGroup)等配置
- 📊 **实时进度监控**: 可视化进度条和状态显示
- 📝 **升级日志展示**: 黑色背景的实时日志输出
- ⏯️ **任务控制**: 支持开始升级、取消升级等操作

**表单字段**:
- **业务组(dmGroup)**: 价格组/综合组/数据组/国际落地组/架构组（必填，第一优先级）
- **环境**: dev/qa/uat/prd环境选择（必填，第二优先级）
- **服务名称**: 动态加载GitLab项目列表，支持实时搜索和过滤（必填，第三优先级）
- **升级注册中心**: 是否升级注册中心配置
- **自动刷新配置**: 是否自动刷新配置
- **是否发布**: 是否自动发布到环境（新增）
- **分支名称**: 根据选择的服务动态加载GitLab分支，默认master分支

**接口对接**:
- `POST /api/upgrade/nacos/upgrade` - 提交升级任务
- `GET /api/upgrade/nacos/status/{taskId}` - 获取升级状态
- `POST /api/upgrade/nacos/cancel/{taskId}` - 取消升级任务
- `POST /api/v1/git/gitlab/projects/search` - 搜索GitLab项目
- `GET /api/v1/git/gitlab/projects/{projectId}` - 获取项目详情
- `GET /api/v1/git/gitlab/projects/{projectId}/branches` - 获取项目分支列表

**页面布局**:
- 上半部分：筛选条件表单区域
- 下半部分：升级信息展示区域（居中显示）

### 2. Nacos 配置管理页面 (`/config`)

**主要功能**:
- 🔍 **配置搜索**: 支持按环境、命名空间、配置组、文件名称等条件搜索
- 📋 **配置列表**: 分页展示配置信息
- 👁️ **配置查看**: 查看完整的配置内容和基本信息
- ✏️ **配置编辑**: 在线编辑配置内容，支持格式化和验证

**搜索字段** (按优先级排序):
- **环境**: dev/qa/uat/prd环境选择 (默认:dev)
- **命名空间**: public/dev/qa/uat/prd等 (默认:public)
- **配置组**: public/DEFAULT_GROUP/dev/qa/uat/prd等 (默认:public)
- **文件名称**: 支持文件名模糊搜索

**接口对接**:
- `GET /api/upgrade/nacos/configs/{dataId}` - 获取单个配置详情
- `PUT /api/upgrade/nacos/configs/{dataId}` - 更新配置内容
- `POST /api/upgrade/nacos/config/search` - 搜索配置
- `POST /api/upgrade/nacos/config/list` - 获取配置列表

## 🛠️ 开发和部署

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
cd mse-nacos-frontend
npm install
```

### 开发运行

```bash
npm run dev
```

访问地址: http://localhost:3000

### 构建部署

```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## ⚙️ 配置说明

### API 代理配置

项目配置了开发环境的 API 代理，将前端请求代理到后端服务：

```javascript
// vite.config.js
server: {
  port: 3000,
  proxy: {
    '/api': {
      target: 'http://localhost:8081/upgrade-center',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, '/api')
    }
  }
}
```

### 后端服务要求

确保 dm-upgrade-center 后端服务正常运行：
- 服务地址: http://localhost:8081/upgrade-center
- API 前缀: /api/upgrade

## 🎨 界面特性

### 设计风格
- 采用 Ant Design Vue 现代化设计语言
- 响应式布局，支持移动端适配
- 统一的颜色主题和交互反馈

### 用户体验
- 实时状态更新和进度反馈
- 友好的错误提示和加载状态
- 直观的操作流程和导航

### 组件特色
- **升级页面**: 专业的日志输出界面，类似控制台体验
- **配置页面**: 强大的搜索和编辑功能，支持多种配置格式
- **导航侧边栏**: 清晰的模块分类和状态指示

## 📡 API 集成

### 请求拦截器
- 自动添加请求头和超时配置
- 统一的请求日志记录

### 响应拦截器
- 统一的 ApiResponse 格式处理
- 自动错误提示和状态码处理
- 网络异常和超时处理

### 错误处理
- 网络错误自动重试机制
- 用户友好的错误信息显示
- 开发环境详细错误日志

## 🚦 使用流程

### 服务升级流程
1. 选择环境和服务配置
2. 填写必要的升级参数
3. 点击"开始升级"提交任务
4. 实时监控升级进度和日志
5. 任务完成后查看结果

### 配置管理流程
1. 选择环境和搜索条件
2. 查询配置列表
3. 点击"查看"或"编辑"操作配置
4. 编辑完成后保存到远程 Nacos

## 🐛 故障排除

### 常见问题

1. **API 请求失败**
   - 检查后端服务是否正常运行
   - 确认代理配置是否正确
   - 查看浏览器开发者工具网络面板

2. **页面加载异常**
   - 清除浏览器缓存
   - 检查控制台错误信息
   - 确认依赖包是否正确安装

3. **升级任务无响应**
   - 检查 WebSocket 连接状态
   - 确认后端任务处理是否正常
   - 查看后端服务日志

### 开发调试

```bash
# 查看详细的构建信息
npm run build -- --debug

# 分析打包文件大小
npm run build -- --report
```

## 📝 更新日志

### v1.3.0 (2024-07-31)
- ✨ **新增是否发布字段**：添加autoPublish字段控制是否自动发布到环境
- 🔄 **字段顺序优化**：调整表单字段顺序为业务组→环境→服务名称，符合业务逻辑
- 🌿 **智能分支加载**：选择服务时自动加载该项目的GitLab分支列表，默认选择master
- 🎯 **分支选择增强**：动态显示项目真实分支，支持默认分支标识
- 🔧 **容错机制完善**：API不可用时智能回退到静态分支选项
- 📐 **布局优化**：第二排调整为4个字段各占6列，布局更均衡

### v1.2.0 (2024-07-31)
- 🚀 **服务名称动态加载**：集成GitLab API，动态加载所有项目列表
- 🔍 **实时搜索过滤**：支持根据项目名称、路径等多维度实时搜索
- 💪 **容错机制**：API不可用时自动回退到静态选项
- 🎨 **用户体验优化**：显示项目名称和路径，加载状态指示
- 🔧 **API路径更新**：适配新的GitLabController路径结构

### v1.1.0 (2024-07-31)
- ✨ **升级页面优化**：添加业务组(dmGroup)字段支持，包含价格组/综合组/数据组/国际落地组/架构组
- 🗑️ **移除冗余字段**：移除Git仓库URL字段，简化表单结构
- 🔧 **分支选择优化**：分支名称改为下拉选择，支持常用分支快速选择
- 🔍 **搜索功能增强**：配置页面添加namespace字段，调整字段顺序为环境→命名空间→配置组→文件名称
- 📝 **标签优化**：将"配置ID"改为"文件名称"，更符合业务逻辑
- ⚙️ **默认值调整**：group默认值从"DEFAULT_GROUP"改为"public"

### v1.0.0 (2024-07-31)
- ✨ 初始版本发布
- 🎯 完成 Nacos 服务升级功能
- 🔧 完成 Nacos 配置管理功能
- 🎨 完成响应式界面设计
- 📡 完成 REST API 集成

## 🤝 贡献指南

1. Fork 项目仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件至开发团队
- 查看项目文档和 Wiki