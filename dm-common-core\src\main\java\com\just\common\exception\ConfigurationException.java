package com.just.common.exception;

import com.just.common.constants.ErrorCodes;
import lombok.Getter;

/**
 * 配置异常
 * 用于配置相关的异常处理
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
@Getter
public class ConfigurationException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private final String errorCode;
    
    /**
     * 错误消息
     */
    private final String errorMessage;
    
    /**
     * 配置名称
     */
    private final String configName;
    
    /**
     * 配置值
     */
    private final Object configValue;
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     */
    public ConfigurationException(String message) {
        super(message);
        this.errorCode = ErrorCodes.CONFIG_ERROR;
        this.errorMessage = message;
        this.configName = null;
        this.configValue = null;
    }
    
    /**
     * 构造函数
     * 
     * @param configName 配置名称
     * @param message 错误消息
     */
    public ConfigurationException(String configName, String message) {
        super(message);
        this.errorCode = ErrorCodes.CONFIG_ERROR;
        this.errorMessage = message;
        this.configName = configName;
        this.configValue = null;
    }
    
    /**
     * 构造函数
     * 
     * @param configName 配置名称
     * @param configValue 配置值
     * @param message 错误消息
     */
    public ConfigurationException(String configName, Object configValue, String message) {
        super(message);
        this.errorCode = ErrorCodes.CONFIG_ERROR;
        this.errorMessage = message;
        this.configName = configName;
        this.configValue = configValue;
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param configName 配置名称
     * @param message 错误消息
     */
    public ConfigurationException(String errorCode, String configName, String message) {
        super(message);
        this.errorCode = errorCode;
        this.errorMessage = message;
        this.configName = configName;
        this.configValue = null;
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原因异常
     */
    public ConfigurationException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = ErrorCodes.CONFIG_ERROR;
        this.errorMessage = message;
        this.configName = null;
        this.configValue = null;
    }
    
    /**
     * 构造函数
     * 
     * @param configName 配置名称
     * @param message 错误消息
     * @param cause 原因异常
     */
    public ConfigurationException(String configName, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = ErrorCodes.CONFIG_ERROR;
        this.errorMessage = message;
        this.configName = configName;
        this.configValue = null;
    }
    
    // ======================== 便捷方法 ========================
    
    /**
     * 创建配置缺失异常
     * 
     * @param configName 配置名称
     * @return 配置异常
     */
    public static ConfigurationException configMissing(String configName) {
        return new ConfigurationException(configName, "配置缺失: " + configName);
    }
    
    /**
     * 创建配置值无效异常
     * 
     * @param configName 配置名称
     * @param configValue 配置值
     * @param expectedFormat 期望格式
     * @return 配置异常
     */
    public static ConfigurationException invalidConfigValue(String configName, Object configValue, String expectedFormat) {
        return new ConfigurationException(configName, configValue, 
                String.format("配置值无效: %s = %s，期望格式: %s", configName, configValue, expectedFormat));
    }
    
    /**
     * 创建配置文件不存在异常
     * 
     * @param configFile 配置文件路径
     * @return 配置异常
     */
    public static ConfigurationException configFileNotFound(String configFile) {
        return new ConfigurationException(ErrorCodes.FILE_NOT_FOUND, configFile, 
                "配置文件不存在: " + configFile);
    }
    
    /**
     * 创建配置文件解析异常
     * 
     * @param configFile 配置文件路径
     * @param cause 原因异常
     * @return 配置异常
     */
    public static ConfigurationException configParseError(String configFile, Throwable cause) {
        return new ConfigurationException(configFile, "配置文件解析失败: " + configFile, cause);
    }
    
    /**
     * 创建环境配置错误异常
     * 
     * @param environment 环境名称
     * @param reason 错误原因
     * @return 配置异常
     */
    public static ConfigurationException envConfigError(String environment, String reason) {
        return new ConfigurationException(ErrorCodes.CONFIG_ERROR, environment, 
                String.format("环境配置错误: %s，原因: %s", environment, reason));
    }
    
    /**
     * 创建Nacos配置错误异常
     * 
     * @param dataId 配置ID
     * @param group 配置组
     * @param reason 错误原因
     * @return 配置异常
     */
    public static ConfigurationException nacosConfigError(String dataId, String group, String reason) {
        return new ConfigurationException(ErrorCodes.NACOS_CONFIG_GET_ERROR, 
                String.format("%s@%s", dataId, group), 
                String.format("Nacos配置错误: %s@%s，原因: %s", dataId, group, reason));
    }
    
    /**
     * 创建模板配置错误异常
     * 
     * @param templateName 模板名称
     * @param reason 错误原因
     * @return 配置异常
     */
    public static ConfigurationException templateConfigError(String templateName, String reason) {
        return new ConfigurationException(ErrorCodes.TEMPLATE_CONFIG_ERROR, templateName, 
                String.format("模板配置错误: %s，原因: %s", templateName, reason));
    }
    
    /**
     * 创建Git配置错误异常
     * 
     * @param configName 配置名称
     * @param reason 错误原因
     * @return 配置异常
     */
    public static ConfigurationException gitConfigError(String configName, String reason) {
        return new ConfigurationException(ErrorCodes.GIT_CONFIG_ERROR, configName, 
                String.format("Git配置错误: %s，原因: %s", configName, reason));
    }
    
    @Override
    public String toString() {
        return String.format("ConfigurationException{errorCode='%s', configName='%s', configValue=%s, errorMessage='%s'}", 
                errorCode, configName, configValue, errorMessage);
    }
}