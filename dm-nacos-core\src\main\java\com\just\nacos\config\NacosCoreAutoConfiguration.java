package com.just.nacos.config;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.just.nacos.service.AliyunMseActionService;
import com.just.nacos.service.impl.AliyunMseActionServiceImpl;
import com.just.nacos.service.NacosConfigService;
import com.just.nacos.service.impl.NacosConfigServiceImpl;
import com.just.nacos.service.NacosEnvironmentService;
import com.just.nacos.service.impl.NacosEnvironmentServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * Nacos核心模块自动配置类
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(NacosCoreProperties.class)
@ConditionalOnProperty(prefix = "dm.nacos", name = "enabled", havingValue = "true", matchIfMissing = true)
public class NacosCoreAutoConfiguration {

    /**
     * 配置Nacos ConfigService
     */
    @Bean
    @ConditionalOnMissingBean
    public ConfigService nacosConfigService(NacosCoreProperties nacosCoreProperties) throws NacosException {
        log.info("初始化Nacos ConfigService");
        Properties properties = new Properties();
        // 这里使用默认配置，实际使用时需要配置真实的Nacos服务器地址
        properties.setProperty("serverAddr", "127.0.0.1:8848");
        properties.setProperty("namespace", "");
        return NacosFactory.createConfigService(properties);
    }

    /**
     * 配置Nacos环境管理服务
     */
    @Bean
    @ConditionalOnMissingBean
    public NacosEnvironmentService nacosEnvironmentService(NacosCoreProperties nacosCoreProperties) {
        log.info("初始化Nacos环境管理服务");
        return new NacosEnvironmentServiceImpl(nacosCoreProperties);
    }

    /**
     * 配置阿里云MSE Action服务
     */
    @Bean
    @ConditionalOnMissingBean
    public AliyunMseActionService aliyunMseActionService(NacosEnvironmentService nacosEnvironmentService) {
        log.info("初始化阿里云MSE Action服务");
        return new AliyunMseActionServiceImpl(nacosEnvironmentService);
    }

    /**
     * 配置Nacos配置管理服务
     */
    @Bean
    @ConditionalOnMissingBean
    public NacosConfigService nacosConfigManagementService(
            AliyunMseActionService aliyunMseActionService,
            NacosCoreProperties nacosCoreProperties) {
        log.info("初始化Nacos配置管理服务");
        return new NacosConfigServiceImpl(aliyunMseActionService, nacosCoreProperties);
    }

}