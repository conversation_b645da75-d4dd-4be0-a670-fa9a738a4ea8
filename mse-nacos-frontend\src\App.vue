<template>
  <a-config-provider :locale="zhCN">
    <div id="app">
      <a-layout style="min-height: 100vh">
        <!-- 侧边栏 -->
        <a-layout-sider
          v-model:collapsed="collapsed"
          :trigger="null"
          collapsible
          width="256"
          style="background: #fff; box-shadow: 2px 0 6px rgba(0,21,41,.35)"
        >
          <div class="logo">
            <h3 v-if="!collapsed">MSE Nacos</h3>
            <h3 v-else>MSE</h3>
          </div>
          
          <a-menu
            v-model:selectedKeys="selectedKeys"
            v-model:openKeys="openKeys"
            mode="inline"
            style="border-right: 0"
            @click="handleMenuClick"
          >
            <a-menu-item key="upgrade">
              <template #icon>
                <UploadOutlined />
              </template>
              服务升级
            </a-menu-item>
            <a-menu-item key="config">
              <template #icon>
                <SettingOutlined />
              </template>
              配置管理
            </a-menu-item>
          </a-menu>
        </a-layout-sider>

        <!-- 主内容区 -->
        <a-layout>
          <!-- 顶部导航 -->
          <a-layout-header style="background: #fff; padding: 0; box-shadow: 0 1px 4px rgba(0,21,41,.08)">
            <div style="display: flex; align-items: center; height: 64px; padding: 0 24px">
              <MenuUnfoldOutlined
                v-if="collapsed"
                class="trigger"
                @click="() => (collapsed = !collapsed)"
              />
              <MenuFoldOutlined
                v-else
                class="trigger"
                @click="() => (collapsed = !collapsed)"
              />
              <div style="flex: 1; text-align: center">
                <h2 style="margin: 0; color: #1890ff">{{ pageTitle }}</h2>
              </div>
            </div>
          </a-layout-header>

          <!-- 内容区域 -->
          <a-layout-content style="margin: 24px; padding: 24px; background: #fff; min-height: 280px">
            <router-view />
          </a-layout-content>
        </a-layout>
      </a-layout>
    </div>
  </a-config-provider>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import zhCN from 'ant-design-vue/locale/zh_CN'
import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  UploadOutlined,
  SettingOutlined,
} from '@ant-design/icons-vue'

const router = useRouter()

const collapsed = ref(false)
const selectedKeys = ref(['upgrade'])
const openKeys = ref([])

const pageTitle = computed(() => {
  const key = selectedKeys.value[0]
  const titles = {
    upgrade: 'Nacos服务升级',
    config: 'Nacos配置管理'
  }
  return titles[key] || 'MSE Nacos管理平台'
})

const handleMenuClick = ({ key }) => {
  selectedKeys.value = [key]
  if (key === 'upgrade') {
    router.push('/upgrade')
  } else if (key === 'config') {
    router.push('/config')
  }
}

// 根据路由更新菜单选中状态
watch(() => router.currentRoute.value.path, (newPath) => {
  if (newPath.includes('upgrade')) {
    selectedKeys.value = ['upgrade']
  } else if (newPath.includes('config')) {
    selectedKeys.value = ['config']
  }
}, { immediate: true })
</script>

<style scoped>
.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
}

.logo h3 {
  color: #1890ff;
  font-weight: bold;
  margin: 0;
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #1890ff;
}
</style>