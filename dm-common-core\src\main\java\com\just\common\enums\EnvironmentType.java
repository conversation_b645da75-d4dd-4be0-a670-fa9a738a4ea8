package com.just.common.enums;

import lombok.Getter;

/**
 * 环境类型枚举
 * 对应现有的EnvPathEnum，但更加通用化
 * 支持dev/qa/uat/prd四个标准环境
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
@Getter
public enum EnvironmentType {
    
    /**
     * 开发环境
     */
    DEV("dev", "开发环境", "development"),
    
    /**
     * 测试环境  
     */
    QA("qa", "测试环境", "quality assurance"),
    
    /**
     * 预发环境
     */
    UAT("uat", "预发环境", "user acceptance testing"),
    
    /**
     * 生产环境
     */
    PRD("prd", "生产环境", "production");
    
    /**
     * 环境代码
     */
    private final String code;
    
    /**
     * 环境描述
     */
    private final String description;
    
    /**
     * 环境全称
     */
    private final String fullName;
    
    EnvironmentType(String code, String description, String fullName) {
        this.code = code;
        this.description = description;
        this.fullName = fullName;
    }
    
    /**
     * 根据代码查找环境类型
     * 
     * @param code 环境代码
     * @return 环境类型，未找到返回null
     */
    public static EnvironmentType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (EnvironmentType env : values()) {
            if (env.getCode().equalsIgnoreCase(code.trim())) {
                return env;
            }
        }
        return null;
    }
    
    /**
     * 验证环境代码是否有效
     * 
     * @param code 环境代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return fromCode(code) != null;
    }
    
    /**
     * 获取所有支持的环境代码
     * 
     * @return 环境代码数组
     */
    public static String[] getAllCodes() {
        EnvironmentType[] envs = values();
        String[] codes = new String[envs.length];
        for (int i = 0; i < envs.length; i++) {
            codes[i] = envs[i].getCode();
        }
        return codes;
    }
    
    /**
     * 判断是否为生产环境
     * 
     * @return 是否为生产环境
     */
    public boolean isProduction() {
        return this == PRD;
    }
    
    /**
     * 判断是否为开发环境
     * 
     * @return 是否为开发环境
     */
    public boolean isDevelopment() {
        return this == DEV;
    }
}