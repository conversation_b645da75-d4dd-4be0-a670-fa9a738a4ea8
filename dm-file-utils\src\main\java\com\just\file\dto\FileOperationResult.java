package com.just.file.dto;

import com.just.common.enums.OperationStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件操作结果DTO
 * 统一封装所有文件操作的结果
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileOperationResult implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 操作是否成功
     */
    private boolean success;
    
    /**
     * 操作状态
     */
    private OperationStatus status;
    
    /**
     * 操作类型
     */
    private String operationType;
    
    /**
     * 目标文件路径
     */
    private String filePath;
    
    /**
     * 操作消息
     */
    private String message;
    
    /**
     * 错误详情
     */
    private String errorDetail;
    
    /**
     * 处理的文件数量
     */
    private int processedCount;
    
    /**
     * 失败的文件数量
     */
    private int failedCount;
    
    /**
     * 操作开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 操作结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 操作耗时（毫秒）
     */
    private long duration;
    
    /**
     * 附加数据
     */
    private Object data;
    
    /**
     * 失败的文件列表
     */
    private List<String> failedFiles;
    
    /**
     * 创建成功的操作结果
     * 
     * @param operationType 操作类型
     * @param filePath 文件路径
     * @param message 消息
     * @return 操作结果
     */
    public static FileOperationResult success(String operationType, String filePath, String message) {
        return FileOperationResult.builder()
                .success(true)
                .status(OperationStatus.SUCCESS)
                .operationType(operationType)
                .filePath(filePath)
                .message(message)
                .endTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建成功的操作结果（带数据）
     * 
     * @param operationType 操作类型
     * @param filePath 文件路径
     * @param message 消息
     * @param data 数据
     * @return 操作结果
     */
    public static FileOperationResult success(String operationType, String filePath, String message, Object data) {
        return FileOperationResult.builder()
                .success(true)
                .status(OperationStatus.SUCCESS)
                .operationType(operationType)
                .filePath(filePath)
                .message(message)
                .data(data)
                .endTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建失败的操作结果
     * 
     * @param operationType 操作类型
     * @param filePath 文件路径
     * @param message 消息
     * @param errorDetail 错误详情
     * @return 操作结果
     */
    public static FileOperationResult failure(String operationType, String filePath, String message, String errorDetail) {
        return FileOperationResult.builder()
                .success(false)
                .status(OperationStatus.FAILED)
                .operationType(operationType)
                .filePath(filePath)
                .message(message)
                .errorDetail(errorDetail)
                .endTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建失败的操作结果（带异常）
     * 
     * @param operationType 操作类型
     * @param filePath 文件路径
     * @param message 消息
     * @param throwable 异常
     * @return 操作结果
     */
    public static FileOperationResult failure(String operationType, String filePath, String message, Throwable throwable) {
        String errorDetail = throwable != null ? throwable.getMessage() : "未知错误";
        return failure(operationType, filePath, message, errorDetail);
    }
    
    /**
     * 设置操作开始时间
     * 
     * @return 当前对象（支持链式调用）
     */
    public FileOperationResult start() {
        this.startTime = LocalDateTime.now();
        this.status = OperationStatus.RUNNING;
        return this;
    }
    
    /**
     * 设置操作结束时间并计算耗时
     * 
     * @return 当前对象（支持链式调用）
     */
    public FileOperationResult finish() {
        this.endTime = LocalDateTime.now();
        if (this.startTime != null) {
            this.duration = java.time.Duration.between(this.startTime, this.endTime).toMillis();
        }
        return this;
    }
    
    /**
     * 增加处理文件数量
     * 
     * @param count 数量
     * @return 当前对象（支持链式调用）
     */
    public FileOperationResult addProcessedCount(int count) {
        this.processedCount += count;
        return this;
    }
    
    /**
     * 增加失败文件数量
     * 
     * @param count 数量
     * @return 当前对象（支持链式调用）
     */
    public FileOperationResult addFailedCount(int count) {
        this.failedCount += count;
        return this;
    }
    
    /**
     * 添加失败文件
     * 
     * @param failedFile 失败的文件路径
     * @return 当前对象（支持链式调用）
     */
    public FileOperationResult addFailedFile(String failedFile) {
        if (this.failedFiles == null) {
            this.failedFiles = new java.util.ArrayList<>();
        }
        this.failedFiles.add(failedFile);
        this.failedCount = this.failedFiles.size();
        return this;
    }
    
    /**
     * 获取成功率
     * 
     * @return 成功率（0-1）
     */
    public double getSuccessRate() {
        if (processedCount == 0) {
            return 0.0;
        }
        return (double) (processedCount - failedCount) / processedCount;
    }
    
    /**
     * 获取格式化的耗时
     * 
     * @return 格式化耗时字符串
     */
    public String getFormattedDuration() {
        if (duration < 1000) {
            return duration + "ms";
        } else if (duration < 60000) {
            return String.format("%.2fs", duration / 1000.0);
        } else {
            long minutes = duration / 60000;
            long seconds = (duration % 60000) / 1000;
            return String.format("%dm %ds", minutes, seconds);
        }
    }
}