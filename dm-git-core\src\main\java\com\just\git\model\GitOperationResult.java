package com.just.git.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Git操作结果
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GitOperationResult {
    
    /**
     * 操作是否成功
     */
    private boolean success;
    
    /**
     * 操作类型
     */
    private String operation;
    
    /**
     * 结果消息
     */
    private String message;
    
    /**
     * 错误消息（如果失败）
     */
    private String errorMessage;
    
    /**
     * 操作开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 操作结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 操作耗时（毫秒）
     */
    private long durationMs;
    
    /**
     * 关联的Git仓库
     */
    private GitRepository repository;
    
    /**
     * 提交ID
     */
    private String commitId;
    
    /**
     * 分支信息
     */
    private String branchInfo;
    
    /**
     * 创建成功结果
     */
    public static GitOperationResult success(String operation, String message) {
        return GitOperationResult.builder()
                .success(true)
                .operation(operation)
                .message(message)
                .endTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建失败结果
     */
    public static GitOperationResult failure(String operation, String errorMessage) {
        return GitOperationResult.builder()
                .success(false)
                .operation(operation)
                .errorMessage(errorMessage)
                .endTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 设置分支信息
     */
    public GitOperationResult withBranchInfo(String currentBranch, String targetBranch) {
        this.branchInfo = String.format("from %s to %s", currentBranch, targetBranch);
        return this;
    }
    
    /**
     * 标记操作结束
     */
    public GitOperationResult markEnd() {
        this.endTime = LocalDateTime.now();
        if (this.startTime != null) {
            this.durationMs = java.time.Duration.between(this.startTime, this.endTime).toMillis();
        }
        return this;
    }
    
    /**
     * 设置提交ID
     */
    public void setCommitId(String commitId) {
        this.commitId = commitId;
    }
}