package com.just.common.enums;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * EnvironmentType枚举测试类
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
public class EnvironmentTypeTest {
    
    @Test
    public void testFromCode() {
        // 测试有效的代码
        assertEquals(EnvironmentType.DEV, EnvironmentType.fromCode("dev"));
        assertEquals(EnvironmentType.QA, EnvironmentType.fromCode("qa"));
        assertEquals(EnvironmentType.UAT, EnvironmentType.fromCode("uat"));
        assertEquals(EnvironmentType.PRD, EnvironmentType.fromCode("prd"));
        
        // 测试大小写不敏感
        assertEquals(EnvironmentType.DEV, EnvironmentType.fromCode("DEV"));
        assertEquals(EnvironmentType.DEV, EnvironmentType.fromCode("Dev"));
        
        // 测试带空格的情况
        assertEquals(EnvironmentType.DEV, EnvironmentType.fromCode(" dev "));
        
        // 测试无效的代码
        assertNull(EnvironmentType.fromCode("invalid"));
        assertNull(EnvironmentType.fromCode(""));
        assertNull(EnvironmentType.fromCode(null));
    }
    
    @Test
    public void testIsValidCode() {
        // 测试有效的代码
        assertTrue(EnvironmentType.isValidCode("dev"));
        assertTrue(EnvironmentType.isValidCode("qa"));
        assertTrue(EnvironmentType.isValidCode("uat"));
        assertTrue(EnvironmentType.isValidCode("prd"));
        
        // 测试大小写不敏感
        assertTrue(EnvironmentType.isValidCode("DEV"));
        assertTrue(EnvironmentType.isValidCode("Dev"));
        
        // 测试无效的代码
        assertFalse(EnvironmentType.isValidCode("invalid"));
        assertFalse(EnvironmentType.isValidCode(""));
        assertFalse(EnvironmentType.isValidCode(null));
    }
    
    @Test
    public void testGetAllCodes() {
        String[] allCodes = EnvironmentType.getAllCodes();
        
        assertNotNull(allCodes);
        assertEquals(4, allCodes.length);
        
        // 验证包含所有环境代码
        assertEquals("dev", allCodes[0]);
        assertEquals("qa", allCodes[1]);
        assertEquals("uat", allCodes[2]);
        assertEquals("prd", allCodes[3]);
    }
    
    @Test
    public void testIsProduction() {
        assertTrue(EnvironmentType.PRD.isProduction());
        assertFalse(EnvironmentType.DEV.isProduction());
        assertFalse(EnvironmentType.QA.isProduction());
        assertFalse(EnvironmentType.UAT.isProduction());
    }
    
    @Test
    public void testIsDevelopment() {
        assertTrue(EnvironmentType.DEV.isDevelopment());
        assertFalse(EnvironmentType.QA.isDevelopment());
        assertFalse(EnvironmentType.UAT.isDevelopment());
        assertFalse(EnvironmentType.PRD.isDevelopment());
    }
    
    @Test
    public void testGetters() {
        // 测试DEV环境
        assertEquals("dev", EnvironmentType.DEV.getCode());
        assertEquals("开发环境", EnvironmentType.DEV.getDescription());
        assertEquals("development", EnvironmentType.DEV.getFullName());
        
        // 测试PRD环境
        assertEquals("prd", EnvironmentType.PRD.getCode());
        assertEquals("生产环境", EnvironmentType.PRD.getDescription());
        assertEquals("production", EnvironmentType.PRD.getFullName());
    }
}