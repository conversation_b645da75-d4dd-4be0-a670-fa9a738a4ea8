package com.just.upgrade.utils;

import java.util.ArrayList;
import java.util.List;

public class EnvironmentReplaceUtils {

    public static List<String> extractEnvironmentVariables(String line) {
        List<String> envVars = new ArrayList<>();
        int startIndex = 0;
        while (true) {
            int start = line.indexOf("${", startIndex);
            if (start == -1) {
                break;
            }
            int end = line.indexOf("}", start);
            if (end == -1) {
                break;
            }
            String envVar = line.substring(start + 2, end);
            envVars.add(envVar);
            startIndex = end + 1;
        }
        return envVars;
    }
}
