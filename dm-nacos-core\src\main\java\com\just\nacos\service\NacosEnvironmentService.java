package com.just.nacos.service;

import com.just.nacos.model.NacosEnvironment;

import java.util.List;
import java.util.Optional;

/**
 * Nacos环境管理服务接口
 *
 * <AUTHOR>
 * @since 2.0.0
 */
public interface NacosEnvironmentService {

    /**
     * 创建环境
     *
     * @param environment 环境信息
     * @return 是否成功
     */
    boolean createEnvironment(NacosEnvironment environment);

    /**
     * 更新环境
     *
     * @param environment 环境信息
     * @return 是否成功
     */
    boolean updateEnvironment(NacosEnvironment environment);

    /**
     * 删除环境
     *
     * @param environmentName 环境名称
     * @return 是否成功
     */
    boolean deleteEnvironment(String environmentName);

    /**
     * 获取环境详情
     *
     * @param environmentName 环境名称
     * @return 环境信息
     */
    Optional<NacosEnvironment> getEnvironment(String environmentName);

    /**
     * 列出所有环境
     *
     * @return 环境列表
     */
    List<NacosEnvironment> listEnvironments();

    /**
     * 列出可用环境
     *
     * @return 可用环境列表
     */
    List<NacosEnvironment> listAvailableEnvironments();

    /**
     * 搜索环境
     *
     * @param keyword 关键词
     * @return 匹配的环境列表
     */
    List<NacosEnvironment> searchEnvironments(String keyword);

    /**
     * 检查环境是否存在
     *
     * @param environmentName 环境名称
     * @return 是否存在
     */
    boolean environmentExists(String environmentName);

    /**
     * 检查环境是否可用
     *
     * @param environmentName 环境名称
     * @return 是否可用
     */
    boolean isEnvironmentAvailable(String environmentName);

    /**
     * 激活环境
     *
     * @param environmentName 环境名称
     * @return 是否成功
     */
    boolean activateEnvironment(String environmentName);

    /**
     * 停用环境
     *
     * @param environmentName 环境名称
     * @return 是否成功
     */
    boolean deactivateEnvironment(String environmentName);

    /**
     * 设置默认环境
     *
     * @param environmentName 环境名称
     * @return 是否成功
     */
    boolean setDefaultEnvironment(String environmentName);

    /**
     * 获取默认环境
     *
     * @return 默认环境
     */
    Optional<NacosEnvironment> getDefaultEnvironment();

    /**
     * 测试环境连接
     *
     * @param environmentName 环境名称
     * @return 连接测试结果
     */
    String testEnvironmentConnection(String environmentName);

    /**
     * 初始化环境（创建基础配置）
     *
     * @param environmentName 环境名称
     * @return 初始化结果
     */
    String initializeEnvironment(String environmentName);

    /**
     * 清理环境（删除所有配置）
     *
     * @param environmentName 环境名称
     * @param confirm 确认标志
     * @return 清理结果
     */
    String cleanupEnvironment(String environmentName, boolean confirm);

    /**
     * 复制环境配置
     *
     * @param sourceEnv 源环境
     * @param targetEnv 目标环境
     * @param overwrite 是否覆盖已存在的配置
     * @return 复制结果
     */
    String copyEnvironmentConfig(String sourceEnv, String targetEnv, boolean overwrite);

    /**
     * 同步环境配置
     *
     * @param sourceEnv 源环境
     * @param targetEnvs 目标环境列表
     * @param includePatterns 包含模式（配置名称匹配）
     * @param excludePatterns 排除模式（配置名称匹配）
     * @return 同步结果
     */
    String syncEnvironmentConfig(String sourceEnv, List<String> targetEnvs, 
                                List<String> includePatterns, List<String> excludePatterns);

    /**
     * 获取环境配置统计
     *
     * @param environmentName 环境名称
     * @return 统计信息
     */
    String getEnvironmentStatistics(String environmentName);

    /**
     * 备份环境配置
     *
     * @param environmentName 环境名称
     * @param backupPath 备份路径
     * @return 备份结果
     */
    String backupEnvironmentConfig(String environmentName, String backupPath);

    /**
     * 恢复环境配置
     *
     * @param environmentName 环境名称
     * @param backupPath 备份路径
     * @param overwrite 是否覆盖已存在的配置
     * @return 恢复结果
     */
    String restoreEnvironmentConfig(String environmentName, String backupPath, boolean overwrite);

    /**
     * 验证环境配置
     *
     * @param environment 环境信息
     * @return 验证结果
     */
    String validateEnvironmentConfig(NacosEnvironment environment);

    /**
     * 获取环境命名空间列表
     *
     * @param environmentName 环境名称
     * @return 命名空间列表
     */
    List<String> getEnvironmentNamespaces(String environmentName);

    /**
     * 获取环境配置组列表
     *
     * @param environmentName 环境名称
     * @param namespace 命名空间
     * @return 配置组列表
     */
    List<String> getEnvironmentGroups(String environmentName, String namespace);

    /**
     * 监控环境状态
     *
     * @param environmentName 环境名称
     * @return 监控信息
     */
    String monitorEnvironmentStatus(String environmentName);

    /**
     * 设置环境变量
     *
     * @param environmentName 环境名称
     * @param key 变量键
     * @param value 变量值
     * @return 是否成功
     */
    boolean setEnvironmentVariable(String environmentName, String key, String value);

    /**
     * 获取环境变量
     *
     * @param environmentName 环境名称
     * @param key 变量键
     * @return 变量值
     */
    Optional<String> getEnvironmentVariable(String environmentName, String key);

    /**
     * 删除环境变量
     *
     * @param environmentName 环境名称
     * @param key 变量键
     * @return 是否成功
     */
    boolean removeEnvironmentVariable(String environmentName, String key);

    /**
     * 批量设置环境变量
     *
     * @param environmentName 环境名称
     * @param variables 变量映射
     * @return 设置成功的变量数量
     */
    int setEnvironmentVariables(String environmentName, java.util.Map<String, String> variables);
}