package com.just.upgrade.controller;

import com.just.nacos.model.NacosConfig;
import com.just.nacos.service.NacosConfigService;
import com.just.upgrade.model.*;
import com.just.upgrade.service.GitLabProjectService;
import com.just.upgrade.service.NacosUpgradeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 升级中心控制器
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/upgrade")
@Validated
public class UpgradeCenterController {

    @Autowired
    private NacosUpgradeService nacosUpgradeService;
    @Autowired
    private NacosConfigService nacosConfigService;

    @Autowired
    private GitLabProjectService ********************;

    /**
     * 升级Nacos服务
     *
     * @param request 升级请求
     * @return 任务ID
     */
    @PostMapping("/nacos/upgrade")
    public ApiResponse<UpgradeStatus> upgradeNacosService(@Valid @RequestBody UpgradeRequest request) {
        try {
            log.info("收到升级请求: environment={}, serviceName={}, upgradeRegistry={}, autoRefresh={}", 
                request.getEnvironment(), request.getServiceName(), request.getUpgradeRegistry(), request.getAutoRefresh());

            UpgradeStatus upgradeStatus = nacosUpgradeService.upgradeService(request);
            return ApiResponse.success(upgradeStatus, "升级任务已启动");

        } catch (Exception e) {
            log.error("启动升级任务失败", e);
            return ApiResponse.error("启动升级任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取升级状态
     *
     * @param taskId 任务ID
     * @return 升级状态
     */
    @GetMapping("/nacos/status/{taskId}")
    public ApiResponse<UpgradeStatus> getUpgradeStatus(@PathVariable String taskId) {
        try {
            log.info("查询升级状态，任务ID: {}", taskId);

            UpgradeStatus status = nacosUpgradeService.getUpgradeStatus(taskId);

            if (status != null) {
                return ApiResponse.success(status, "查询状态成功");
            } else {
                return ApiResponse.notFound("任务不存在");
            }

        } catch (Exception e) {
            log.error("查询升级状态失败，任务ID: " + taskId, e);
            return ApiResponse.error("查询升级状态失败: " + e.getMessage());
        }
    }

    /**
     * 取消升级任务
     *
     * @param taskId 任务ID
     * @return 操作结果
     */
    @PostMapping("/nacos/cancel/{taskId}")
    public ApiResponse<Boolean> cancelUpgrade(@PathVariable String taskId) {
        try {
            log.info("取消升级任务，任务ID: {}", taskId);

            boolean success = nacosUpgradeService.cancelUpgrade(taskId);

            String message = success ? "升级任务已取消" : "取消升级任务失败";
            return ApiResponse.success(success, message);

        } catch (Exception e) {
            log.error("取消升级任务失败，任务ID: " + taskId, e);
            return ApiResponse.error("取消升级任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取单个远程Nacos文件
     *
     * @param request 搜索请求
     * @return Nacos配置
     */
    @PostMapping("/nacos/config/single")
    public ApiResponse<NacosConfig> getSingleNacosConfig(@Valid @RequestBody NacosSearchRequest request) {
        try {
            log.info("获取单个Nacos配置: environment={}, dataId={}, group={}", 
                request.getEnvironment(), request.getDataId(), request.getGroup());

            NacosConfig config = nacosUpgradeService.getSingleConfig(request);

            if (config != null) {
                return ApiResponse.success(config, "配置获取成功");
            } else {
                return ApiResponse.notFound("配置不存在");
            }

        } catch (Exception e) {
            log.error("获取单个Nacos配置失败", e);
            return ApiResponse.error("获取Nacos配置失败: " + e.getMessage());
        }
    }

    /**
     * 模糊搜索远程Nacos文件
     *
     * @param request 搜索请求
     * @return Nacos配置列表
     */
    @PostMapping("/nacos/config/search")
    public ApiResponse<List<NacosConfig>> searchNacosConfigs(@Valid @RequestBody NacosSearchRequest request) {
        try {
            log.info("模糊搜索Nacos配置: environment={}, fileName={}", 
                request.getEnvironment(), request.getFileName());

            List<NacosConfig> configs = nacosUpgradeService.searchConfigs(request);

            return ApiResponse.success(configs, 
                String.format("搜索成功，共找到%d个配置", configs.size()));

        } catch (Exception e) {
            log.error("模糊搜索Nacos配置失败", e);
            return ApiResponse.error("搜索Nacos配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取环境的所有Nacos文件信息
     *
     * @param request 搜索请求
     * @return Nacos配置列表
     */
    @PostMapping("/nacos/config/list")
    public ApiResponse<List<NacosConfig>> listAllNacosConfigs(@Valid @RequestBody NacosSearchRequest request) {
        try {
            log.info("获取环境所有Nacos配置: environment={}, namespace={}, keyword={}", 
                request.getEnvironment(), request.getNamespace(), request.getKeyword());

            List<NacosConfig> configs = nacosUpgradeService.listAllConfigs(request);

            return ApiResponse.success(configs, 
                String.format("获取成功，共%d个配置", configs.size()));

        } catch (Exception e) {
            log.error("获取环境所有Nacos配置失败", e);
            return ApiResponse.error("获取Nacos配置失败: " + e.getMessage());
        }
    }
    /**
     * 查询单个Nacos配置详情
     *
     * @param dataId 配置ID
     * @param group 配置组
     * @param environment 环境名称
     * @param namespace 命名空间（可选）
     * @return 配置详情
     */
    @GetMapping("/nacos/configs/{dataId}")
    public ApiResponse<NacosConfig> getNacosConfigDetail(
            @PathVariable String dataId,
            @RequestParam String group,
            @RequestParam String environment,
            @RequestParam(required = false) String namespace) {
        try {
            log.info("查询Nacos配置详情: dataId={}, group={}, environment={}, namespace={}", 
                dataId, group, environment, namespace);

            // 构建搜索请求
            NacosSearchRequest request = new NacosSearchRequest();
            request.setDataId(dataId);
            request.setGroup(group);
            request.setEnvironment(environment);
            request.setNamespace(namespace);

            NacosConfig config = nacosUpgradeService.getSingleConfig(request);

            if (config != null) {
                return ApiResponse.success(config, "配置详情获取成功");
            } else {
                return ApiResponse.notFound("配置不存在");
            }

        } catch (Exception e) {
            log.error("查询Nacos配置详情失败: dataId={}, group={}, environment={}", dataId, group, environment, e);
            return ApiResponse.error("查询配置详情失败: " + e.getMessage());
        }
    }

    /**
     * 更新Nacos配置并推送到远程
     *
     * @param dataId 配置ID
     * @param request 更新请求
     * @return 更新结果
     */
    @PutMapping("/nacos/configs/{dataId}")
    public ApiResponse<NacosConfig> updateNacosConfig(
            @PathVariable String dataId,
            @Valid @RequestBody NacosConfigUpdateRequest request) {
        try {
            log.info("更新Nacos配置: dataId={}, group={}, environment={}", 
                dataId, request.getGroup(), request.getEnvironment());

            // 验证dataId匹配
            if (!dataId.equals(request.getDataId())) {
                return ApiResponse.badRequest("路径中的dataId与请求体中的dataId不匹配");
            }

            // 设置默认值
            request.setDefaults();

            // 构建NacosConfig对象
            NacosConfig newConfig = NacosConfig.builder()
                    .dataId(dataId)
                    .group(request.getGroup())
                    .namespace(request.getNamespace())
                    .content(request.getContent())
                    .type(request.getType() != null ? request.getType() : "properties")
                    .environment(request.getEnvironment())
                    .appName(request.getAppName())
                    .createTime(java.time.LocalDateTime.now())
                    .updateTime(java.time.LocalDateTime.now())
                    .status(com.just.nacos.model.NacosConfig.ConfigStatus.ACTIVE)
                    .build();
            // 更新配置
            boolean success = nacosConfigService.publishConfig(newConfig);
            if (success) {
                log.info("Nacos配置更新成功: dataId={}, group={}, environment={}", dataId, request.getGroup(), request.getEnvironment());
                return ApiResponse.success(newConfig, "配置更新成功");
            } else {
                return ApiResponse.error("配置更新失败");
            }

        } catch (Exception e) {
            log.error("更新Nacos配置失败: dataId={}", dataId, e);
            return ApiResponse.error("更新配置失败: " + e.getMessage());
        }
    }
}