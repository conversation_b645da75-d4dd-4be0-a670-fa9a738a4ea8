package com.just.upgrade.config;

import com.just.file.config.ExcelConfigProperties;
import com.just.nacos.config.NacosCoreProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * 升级中心自动配置
 * 简化设计，只配置必要的组件
 * 
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@EnableAsync
@EnableConfigurationProperties(ExcelConfigProperties.class)
public class UpgradeCenterAutoConfiguration {

    /**
     * 配置预加载异步执行器
     */
    @Bean("configPreloadExecutor")
    @ConditionalOnMissingBean(name = "configPreloadExecutor")
    public Executor configPreloadExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(4);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("ConfigPreload-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        
        log.info("配置预加载执行器初始化完成: core={}, max={}, queue={}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }

    /**
     * 配置升级异步执行器
     */
    @Bean("upgradeExecutor")
    @ConditionalOnMissingBean(name = "upgradeExecutor")
    public Executor upgradeExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(3);
        executor.setMaxPoolSize(6);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("Upgrade-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        
        log.info("升级任务执行器初始化完成: core={}, max={}, queue={}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }

    @Bean
    public NacosCoreProperties nacosCoreProperties() {
        return new NacosCoreProperties();
    }
} 