package com.just.nacos.enums;

/**
     * 环境状态枚举
     */
    public enum EnvironmentStatus {
        ACTIVE("激活"),
        INACTIVE("未激活"),
        MAINTENANCE("维护中"),
        DEPRECATED("已废弃");

        private final String description;

        EnvironmentStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }