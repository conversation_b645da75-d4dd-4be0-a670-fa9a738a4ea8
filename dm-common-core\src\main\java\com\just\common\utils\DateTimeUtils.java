package com.just.common.utils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;

/**
 * 日期时间工具类
 * 基于Java 8的时间API，提供常用的日期时间操作
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
public final class DateTimeUtils {
    
    private DateTimeUtils() {
        // 私有构造函数，防止实例化
    }
    
    // ======================== 常用格式化器 ========================
    
    /**
     * 标准日期时间格式：yyyy-MM-dd HH:mm:ss
     */
    public static final DateTimeFormatter STANDARD_DATETIME = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 标准日期格式：yyyy-MM-dd
     */
    public static final DateTimeFormatter STANDARD_DATE = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    
    /**
     * 标准时间格式：HH:mm:ss
     */
    public static final DateTimeFormatter STANDARD_TIME = DateTimeFormatter.ofPattern("HH:mm:ss");
    
    /**
     * 紧凑日期时间格式：yyyyMMddHHmmss
     */
    public static final DateTimeFormatter COMPACT_DATETIME = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    
    /**
     * 紧凑日期格式：yyyyMMdd
     */
    public static final DateTimeFormatter COMPACT_DATE = DateTimeFormatter.ofPattern("yyyyMMdd");
    
    /**
     * ISO日期时间格式（带毫秒）：yyyy-MM-dd'T'HH:mm:ss.SSS
     */
    public static final DateTimeFormatter ISO_DATETIME_WITH_MILLIS = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
    
    /**
     * 中文日期时间格式：yyyy年MM月dd日 HH:mm:ss
     */
    public static final DateTimeFormatter CHINESE_DATETIME = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm:ss");
    
    /**
     * 中文日期格式：yyyy年MM月dd日
     */
    public static final DateTimeFormatter CHINESE_DATE = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
    
    /**
     * 系统默认时区
     */
    public static final ZoneId DEFAULT_ZONE = ZoneId.systemDefault();
    
    /**
     * 东八区时区
     */
    public static final ZoneId ASIA_SHANGHAI = ZoneId.of("Asia/Shanghai");
    
    /**
     * UTC时区
     */
    public static final ZoneId UTC = ZoneId.of("UTC");
    
    // ======================== 获取当前时间方法 ========================
    
    /**
     * 获取当前LocalDateTime
     * 
     * @return 当前LocalDateTime
     */
    public static LocalDateTime now() {
        return LocalDateTime.now();
    }
    
    /**
     * 获取当前LocalDate
     * 
     * @return 当前LocalDate
     */
    public static LocalDate today() {
        return LocalDate.now();
    }
    
    /**
     * 获取当前LocalTime
     * 
     * @return 当前LocalTime
     */
    public static LocalTime currentTime() {
        return LocalTime.now();
    }
    
    /**
     * 获取当前时间戳（毫秒）
     * 
     * @return 时间戳
     */
    public static long currentTimeMillis() {
        return System.currentTimeMillis();
    }
    
    /**
     * 获取当前时间戳（秒）
     * 
     * @return 时间戳
     */
    public static long currentTimeSeconds() {
        return Instant.now().getEpochSecond();
    }
    
    // ======================== 格式化方法 ========================
    
    /**
     * 格式化LocalDateTime为字符串
     * 
     * @param dateTime LocalDateTime
     * @param formatter 格式化器
     * @return 格式化后的字符串
     */
    public static String format(LocalDateTime dateTime, DateTimeFormatter formatter) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(formatter);
    }
    
    /**
     * 格式化LocalDate为字符串
     * 
     * @param date LocalDate
     * @param formatter 格式化器
     * @return 格式化后的字符串
     */
    public static String format(LocalDate date, DateTimeFormatter formatter) {
        if (date == null) {
            return null;
        }
        return date.format(formatter);
    }
    
    /**
     * 格式化LocalTime为字符串
     * 
     * @param time LocalTime
     * @param formatter 格式化器
     * @return 格式化后的字符串
     */
    public static String format(LocalTime time, DateTimeFormatter formatter) {
        if (time == null) {
            return null;
        }
        return time.format(formatter);
    }
    
    /**
     * 使用标准格式格式化LocalDateTime
     * 
     * @param dateTime LocalDateTime
     * @return 格式化后的字符串
     */
    public static String formatStandard(LocalDateTime dateTime) {
        return format(dateTime, STANDARD_DATETIME);
    }
    
    /**
     * 使用标准格式格式化LocalDate
     * 
     * @param date LocalDate
     * @return 格式化后的字符串
     */
    public static String formatStandard(LocalDate date) {
        return format(date, STANDARD_DATE);
    }
    
    /**
     * 使用紧凑格式格式化LocalDateTime
     * 
     * @param dateTime LocalDateTime
     * @return 格式化后的字符串
     */
    public static String formatCompact(LocalDateTime dateTime) {
        return format(dateTime, COMPACT_DATETIME);
    }
    
    /**
     * 使用中文格式格式化LocalDateTime
     * 
     * @param dateTime LocalDateTime
     * @return 格式化后的字符串
     */
    public static String formatChinese(LocalDateTime dateTime) {
        return format(dateTime, CHINESE_DATETIME);
    }
    
    // ======================== 解析方法 ========================
    
    /**
     * 解析字符串为LocalDateTime
     * 
     * @param dateTimeStr 日期时间字符串
     * @param formatter 格式化器
     * @return LocalDateTime
     * @throws DateTimeParseException 解析失败时抛出
     */
    public static LocalDateTime parseDateTime(String dateTimeStr, DateTimeFormatter formatter) {
        if (StringUtils.isBlank(dateTimeStr)) {
            return null;
        }
        return LocalDateTime.parse(dateTimeStr, formatter);
    }
    
    /**
     * 解析字符串为LocalDate
     * 
     * @param dateStr 日期字符串
     * @param formatter 格式化器
     * @return LocalDate
     * @throws DateTimeParseException 解析失败时抛出
     */
    public static LocalDate parseDate(String dateStr, DateTimeFormatter formatter) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        return LocalDate.parse(dateStr, formatter);
    }
    
    /**
     * 解析字符串为LocalTime
     * 
     * @param timeStr 时间字符串
     * @param formatter 格式化器
     * @return LocalTime
     * @throws DateTimeParseException 解析失败时抛出
     */
    public static LocalTime parseTime(String timeStr, DateTimeFormatter formatter) {
        if (StringUtils.isBlank(timeStr)) {
            return null;
        }
        return LocalTime.parse(timeStr, formatter);
    }
    
    /**
     * 使用标准格式解析LocalDateTime
     * 
     * @param dateTimeStr 日期时间字符串
     * @return LocalDateTime
     */
    public static LocalDateTime parseStandardDateTime(String dateTimeStr) {
        return parseDateTime(dateTimeStr, STANDARD_DATETIME);
    }
    
    /**
     * 使用标准格式解析LocalDate
     * 
     * @param dateStr 日期字符串
     * @return LocalDate
     */
    public static LocalDate parseStandardDate(String dateStr) {
        return parseDate(dateStr, STANDARD_DATE);
    }
    
    /**
     * 智能解析日期时间字符串（尝试多种格式）
     * 
     * @param dateTimeStr 日期时间字符串
     * @return LocalDateTime
     */
    public static LocalDateTime parseSmartDateTime(String dateTimeStr) {
        if (StringUtils.isBlank(dateTimeStr)) {
            return null;
        }
        
        DateTimeFormatter[] formatters = {
            STANDARD_DATETIME,
            COMPACT_DATETIME,
            ISO_DATETIME_WITH_MILLIS,
            DateTimeFormatter.ISO_LOCAL_DATE_TIME,
            CHINESE_DATETIME
        };
        
        for (DateTimeFormatter formatter : formatters) {
            try {
                return LocalDateTime.parse(dateTimeStr, formatter);
            } catch (DateTimeParseException ignored) {
                // 继续尝试下一个格式
            }
        }
        
        throw new DateTimeParseException("无法解析日期时间字符串: " + dateTimeStr, dateTimeStr, 0);
    }
    
    // ======================== 转换方法 ========================
    
    /**
     * LocalDateTime转换为时间戳（毫秒）
     * 
     * @param dateTime LocalDateTime
     * @return 时间戳
     */
    public static long toTimestamp(LocalDateTime dateTime) {
        if (dateTime == null) {
            return 0;
        }
        return dateTime.atZone(DEFAULT_ZONE).toInstant().toEpochMilli();
    }
    
    /**
     * LocalDateTime转换为时间戳（秒）
     * 
     * @param dateTime LocalDateTime
     * @return 时间戳
     */
    public static long toTimestampSeconds(LocalDateTime dateTime) {
        if (dateTime == null) {
            return 0;
        }
        return dateTime.atZone(DEFAULT_ZONE).toInstant().getEpochSecond();
    }
    
    /**
     * 时间戳（毫秒）转换为LocalDateTime
     * 
     * @param timestamp 时间戳
     * @return LocalDateTime
     */
    public static LocalDateTime fromTimestamp(long timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), DEFAULT_ZONE);
    }
    
    /**
     * 时间戳（秒）转换为LocalDateTime
     * 
     * @param timestampSeconds 时间戳
     * @return LocalDateTime
     */
    public static LocalDateTime fromTimestampSeconds(long timestampSeconds) {
        return LocalDateTime.ofInstant(Instant.ofEpochSecond(timestampSeconds), DEFAULT_ZONE);
    }
    
    /**
     * Date转换为LocalDateTime
     * 
     * @param date Date对象
     * @return LocalDateTime
     */
    public static LocalDateTime fromDate(Date date) {
        if (date == null) {
            return null;
        }
        return LocalDateTime.ofInstant(date.toInstant(), DEFAULT_ZONE);
    }
    
    /**
     * LocalDateTime转换为Date
     * 
     * @param dateTime LocalDateTime
     * @return Date对象
     */
    public static Date toDate(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return Date.from(dateTime.atZone(DEFAULT_ZONE).toInstant());
    }
    
    // ======================== 计算方法 ========================
    
    /**
     * 计算两个日期之间的天数差
     * 
     * @param start 开始日期
     * @param end 结束日期
     * @return 天数差
     */
    public static long daysBetween(LocalDate start, LocalDate end) {
        if (start == null || end == null) {
            return 0;
        }
        return ChronoUnit.DAYS.between(start, end);
    }
    
    /**
     * 计算两个时间之间的小时差
     * 
     * @param start 开始时间
     * @param end 结束时间
     * @return 小时差
     */
    public static long hoursBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return ChronoUnit.HOURS.between(start, end);
    }
    
    /**
     * 计算两个时间之间的分钟差
     * 
     * @param start 开始时间
     * @param end 结束时间
     * @return 分钟差
     */
    public static long minutesBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return ChronoUnit.MINUTES.between(start, end);
    }
    
    /**
     * 计算两个时间之间的秒数差
     * 
     * @param start 开始时间
     * @param end 结束时间
     * @return 秒数差
     */
    public static long secondsBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return ChronoUnit.SECONDS.between(start, end);
    }
    
    // ======================== 特殊时间获取方法 ========================
    
    /**
     * 获取今天的开始时间（00:00:00）
     * 
     * @return 今天开始时间
     */
    public static LocalDateTime startOfToday() {
        return LocalDate.now().atStartOfDay();
    }
    
    /**
     * 获取今天的结束时间（23:59:59.999）
     * 
     * @return 今天结束时间
     */
    public static LocalDateTime endOfToday() {
        return LocalDate.now().atTime(LocalTime.MAX);
    }
    
    /**
     * 获取本周的开始时间（周一00:00:00）
     * 
     * @return 本周开始时间
     */
    public static LocalDateTime startOfWeek() {
        return LocalDate.now().with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).atStartOfDay();
    }
    
    /**
     * 获取本周的结束时间（周日23:59:59.999）
     * 
     * @return 本周结束时间
     */
    public static LocalDateTime endOfWeek() {
        return LocalDate.now().with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY)).atTime(LocalTime.MAX);
    }
    
    /**
     * 获取本月的开始时间（1号00:00:00）
     * 
     * @return 本月开始时间
     */
    public static LocalDateTime startOfMonth() {
        return LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay();
    }
    
    /**
     * 获取本月的结束时间（最后一天23:59:59.999）
     * 
     * @return 本月结束时间
     */
    public static LocalDateTime endOfMonth() {
        return LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()).atTime(LocalTime.MAX);
    }
    
    /**
     * 获取本年的开始时间（1月1日00:00:00）
     * 
     * @return 本年开始时间
     */
    public static LocalDateTime startOfYear() {
        return LocalDate.now().with(TemporalAdjusters.firstDayOfYear()).atStartOfDay();
    }
    
    /**
     * 获取本年的结束时间（12月31日23:59:59.999）
     * 
     * @return 本年结束时间
     */
    public static LocalDateTime endOfYear() {
        return LocalDate.now().with(TemporalAdjusters.lastDayOfYear()).atTime(LocalTime.MAX);
    }
    
    // ======================== 判断方法 ========================
    
    /**
     * 判断是否为周末
     * 
     * @param date 日期
     * @return 是否为周末
     */
    public static boolean isWeekend(LocalDate date) {
        if (date == null) {
            return false;
        }
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        return dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY;
    }
    
    /**
     * 判断是否为工作日
     * 
     * @param date 日期
     * @return 是否为工作日
     */
    public static boolean isWeekday(LocalDate date) {
        return !isWeekend(date);
    }
    
    /**
     * 判断是否为闰年
     * 
     * @param year 年份
     * @return 是否为闰年
     */
    public static boolean isLeapYear(int year) {
        return Year.of(year).isLeap();
    }
    
    /**
     * 判断时间是否在范围内
     * 
     * @param time 时间
     * @param start 开始时间
     * @param end 结束时间
     * @return 是否在范围内
     */
    public static boolean isBetween(LocalDateTime time, LocalDateTime start, LocalDateTime end) {
        if (time == null || start == null || end == null) {
            return false;
        }
        return !time.isBefore(start) && !time.isAfter(end);
    }
}