package com.just.file.model;

import lombok.Builder;
import lombok.Value;

import java.util.Collections;
import java.util.List;

/**
 * 配置处理结果
 * 包含配置数据和处理元数据，避免重复读取文件
 * 
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
@Value
@Builder(toBuilder = true)
public class ProcessingResult {
    
    /**
     * 配置数据列表
     */
    List<ConfigurationDataCenter.NacosProperty> configurations;
    
    /**
     * 处理元数据
     */
    ConfigurationMetadata metadata;
    
    /**
     * 配置类型
     */
    ConfigurationType type;
    
    /**
     * 处理是否成功
     */
    boolean success;
    
    /**
     * 错误信息（当success为false时）
     */
    String errorMessage;
    
    /**
     * 创建成功的处理结果
     */
    public static ProcessingResult success(ConfigurationType type, 
                                         List<ConfigurationDataCenter.NacosProperty> configurations,
                                         ConfigurationMetadata metadata) {
        return ProcessingResult.builder()
            .type(type)
            .configurations(configurations)
            .metadata(metadata)
            .success(true)
            .build();
    }
    
    /**
     * 创建失败的处理结果
     */
    public static ProcessingResult failure(ConfigurationType type, String errorMessage) {
        return ProcessingResult.builder()
            .type(type)
            .configurations(Collections.emptyList())
            .success(false)
            .errorMessage(errorMessage)
            .build();
    }
} 