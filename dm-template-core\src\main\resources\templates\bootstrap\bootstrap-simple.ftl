# Bootstrap Properties - Auto Generated

spring.application.name=${serviceName!"unknown-service"}
# Nacos Config Properties
spring.cloud.nacos.config.server-addr=${nacosServerAddr!"${NACOS_ADDR}"}
spring.cloud.nacos.config.namespace=${nacosNamespace!"${NACOS_NAMESPACE}"}
spring.cloud.nacos.config.access-key=${nacosAccessKey}
spring.cloud.nacos.config.secret-key=${nacosSecretKey}

<#if enableDiscovery>
# Nacos Discovery Properties
spring.cloud.nacos.discovery.server-addr=${nacosServerAddr!"${NACOS_ADDR}"}
spring.cloud.nacos.discovery.namespace=${nacosNamespace!"${NACOS_NAMESPACE}"}
spring.cloud.nacos.discovery.access-key=${nacosAccessKey!"${NACOS_ACCESS_KEY}"}
spring.cloud.nacos.discovery.secret-key=${nacosSecretKey!"${NACOS_SECRET_KEY}"}
</#if>

spring.cloud.nacos.config.extension-configs[0].data-id=${serviceName!"unknown-service"}.properties
spring.cloud.nacos.config.extension-configs[0].group=${serviceName!"unknown-service"}
spring.cloud.nacos.config.extension-configs[0].refresh=${refresh?string("true", "false")}

<#if extensionConfigs?has_content>
# Extension Configs
<#list extensionConfigs as extConfig>
spring.cloud.nacos.config.shared-configs[${extConfig_index}].data-id=${extConfig.dataId}
spring.cloud.nacos.config.shared-configs[${extConfig_index}].group=${extConfig.group!"DEFAULT_GROUP"}
spring.cloud.nacos.config.shared-configs[${extConfig_index}].refresh=${extConfig.refresh?string("true", "false")}
</#list>
</#if>