<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <charset>UTF-8</charset>
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - [%tid] - message:%msg%n</pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="ROLLIN" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 指定当前日志文件名 -->
        <file>${PUBLIC_DMLOG}/app.log</file>

        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <charset>UTF-8</charset>
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - [%tid] - message:%msg%n</pattern>
            </layout>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 归档日志文件的命名模式 -->
            <fileNamePattern>${PUBLIC_DMLOG}/app.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 单个文件最大大小 -->
            <maxFileSize>200MB</maxFileSize>
            <!-- 日志保留天数 -->
            <maxHistory>1</maxHistory>
            <!-- 所有日志文件总大小上限 -->
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <root level="info">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="ROLLIN"/>
    </root>
</configuration>