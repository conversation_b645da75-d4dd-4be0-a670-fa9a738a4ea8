package com.just.nacos.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Nacos配置信息模型
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NacosConfig {

    /**
     * 配置ID
     */
    private String dataId;

    /**
     * 配置组
     */
    private String group;

    /**
     * 命名空间
     */
    private String namespace;

    /**
     * 配置内容
     */
    private String content;

    /**
     * 配置类型
     */
    private String type;

    /**
     * 环境名称
     */
    private String environment;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 配置状态
     */
    private ConfigStatus status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改者
     */
    private String modifier;

    /**
     * 扩展属性
     */
    private Map<String, Object> metadata;

    /**
     * 配置状态枚举
     */
    public enum ConfigStatus {
        ACTIVE("活跃"),
        INACTIVE("非活跃"),
        DEPRECATED("已废弃");

        private final String description;

        ConfigStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 复制配置（用于创建新配置）
     *
     * @return 配置副本
     */
    public NacosConfig copy() {
        return NacosConfig.builder()
                .dataId(this.dataId)
                .group(this.group)
                .namespace(this.namespace)
                .content(this.content)
                .type(this.type)
                .environment(this.environment)
                .appName(this.appName)
                .status(this.status)
                .metadata(this.metadata != null ? new HashMap<>(this.metadata) : null)
                .build();
    }

    /**
     * 检查配置是否有效
     *
     * @return 是否有效
     */
    public boolean isValid() {
        return dataId != null && !dataId.trim().isEmpty() &&
               content != null && !content.trim().isEmpty();
    }

    /**
     * 获取配置的完整标识
     *
     * @return 配置标识
     */
    public String getFullIdentifier() {
        return String.format("%s/%s/%s", namespace, group, dataId);
    }
} 