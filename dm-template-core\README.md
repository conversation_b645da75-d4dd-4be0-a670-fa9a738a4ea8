# DM Template Core

简洁但严谨的模板引擎核心模块，专注于配置文件模板生成。

## 功能特点

- 🎯 **专注性**: 只做模板生成，不包含复杂的业务逻辑
- 🔧 **严谨性**: 完善的参数验证和异常处理
- 🚀 **简洁性**: 清晰的接口设计，易于使用和扩展
- 🔄 **可扩展**: 支持多种模板类型和自定义模板引擎

## 核心架构

```
dm-template-core/
├── core/                          # 核心接口
│   ├── Template.java              # 模板接口
│   ├── TemplateEngine.java        # 模板引擎接口
│   ├── TemplateContext.java       # 模板上下文
│   ├── TemplateType.java          # 模板类型枚举
│   └── TemplateException.java     # 模板异常
├── engine/                        # 引擎实现
│   └── FreeMarkerTemplateEngine.java # FreeMarker实现
├── factory/                       # 工厂类
│   └── DefaultTemplateFactory.java   # 默认模板工厂
├── model/                         # 数据模型
│   └── BootstrapConfigModel.java     # Bootstrap配置模型
└── config/                        # 配置类
    └── TemplateAutoConfiguration.java # Spring Boot自动配置
```

## 支持的模板类型

1. **BOOTSTRAP** - bootstrap.properties配置模板
2. **APPLICATION_NACOS** - application-nacos.properties配置模板  
3. **POM** - pom.xml文件模板

## 快速开始

### 1. 基本使用

```java
// 创建模板工厂
DefaultTemplateFactory factory = new DefaultTemplateFactory();

// 创建模板上下文
TemplateContext context = new TemplateContext()
    .put("applicationName", "my-service")
    .put("nacosServerAddr", "127.0.0.1:8848")
    .put("enableDiscovery", true);

// 生成模板
String result = factory.generate(TemplateType.BOOTSTRAP, context);
```

### 2. 使用字符串模板

```java
// 创建模板引擎
FreeMarkerTemplateEngine engine = new FreeMarkerTemplateEngine();

// 创建上下文
TemplateContext context = new TemplateContext()
    .put("name", "World")
    .put("enabled", true);

// 处理模板字符串
String template = "Hello ${name}! <#if enabled>Feature is enabled.</#if>";
String result = engine.processString(template, context);
```

### 3. Spring Boot集成

```java
@Autowired
private DefaultTemplateFactory templateFactory;

public String generateConfig() {
    TemplateContext context = new TemplateContext()
        .put("serviceName", "my-service")
        .put("refresh", "true");
        
    return templateFactory.generate(TemplateType.APPLICATION_NACOS, context);
}
```

## 模板参数说明

### Bootstrap模板参数

- `applicationName`: 应用名称
- `nacosServerAddr`: Nacos服务器地址
- `nacosNamespace`: Nacos命名空间
- `nacosAccessKey`: Nacos访问密钥
- `nacosSecretKey`: Nacos密钥Secret
- `enableDiscovery`: 是否启用服务发现
- `extensionConfigs`: 扩展配置列表

### Application Nacos模板参数

- `serviceName`: 服务名称
- `profilesActive`: 激活的配置文件
- `refresh`: 是否启用刷新 ("true"/"false")
- `register`: 是否启用注册 ("true"/"false")
- `additionalProperties`: 额外属性Map

## 自定义模板

### 1. 添加新的模板类型

```java
// 在TemplateType枚举中添加新类型
CUSTOM("custom", "自定义模板")

// 在DefaultTemplateFactory中添加映射
mapping.put(TemplateType.CUSTOM, "custom/custom-template.ftl");
```

### 2. 创建模板文件

在 `src/main/resources/templates/custom/` 目录下创建 `custom-template.ftl`:

```freemarker
# Custom Template
service.name=${serviceName!"default-service"}
<#if enabled>
service.enabled=true
</#if>
```

## 异常处理

模块提供了完善的异常处理机制：

```java
try {
    String result = factory.generate(TemplateType.BOOTSTRAP, context);
} catch (TemplateException e) {
    // 模板异常，包含详细的错误信息
    System.err.println("模板处理失败: " + e.getMessage());
    System.err.println("模板名称: " + e.getTemplateName());
}
```

## 测试

运行简单测试：

```bash
cd dm-template-core
mvn test
```

或者直接运行测试类：

```bash
mvn exec:java -Dexec.mainClass="com.just.template.SimpleTemplateTest"
```

## 依赖

- FreeMarker 2.3.31
- Spring Boot (用于自动配置)
- Spring Framework (Util组件)

## 设计原则

1. **单一职责**: 只负责模板生成，不包含业务逻辑
2. **接口清晰**: 简洁明了的API设计
3. **参数验证**: 严格的参数校验和错误处理
4. **扩展性**: 支持自定义模板类型和引擎
5. **Spring集成**: 提供Spring Boot自动配置支持

## 版本

当前版本: 2.0.0-SNAPSHOT

该模块是DM Auto Utils项目重构的一部分，专注于提供高质量的模板生成能力。 