package com.just.template.exception;

/**
 * 模板异常
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
public class TemplateException extends Exception {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 模板名称
     */
    private final String templateName;
    
    public TemplateException(String message) {
        super(message);
        this.templateName = null;
    }
    
    public TemplateException(String message, Throwable cause) {
        super(message, cause);
        this.templateName = null;
    }
    
    public TemplateException(String templateName, String message) {
        super(String.format("Template [%s]: %s", templateName, message));
        this.templateName = templateName;
    }
    
    public TemplateException(String templateName, String message, Throwable cause) {
        super(String.format("Template [%s]: %s", templateName, message), cause);
        this.templateName = templateName;
    }
    
    public String getTemplateName() {
        return templateName;
    }
} 