package com.just.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 通用配置属性
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
@Data
@ConfigurationProperties(prefix = "dm.common")
public class CommonProperties {
    
    /**
     * 应用名称
     */
    private String appName = "DM Auto Utils";
    
    /**
     * 应用版本
     */
    private String appVersion = "2.0.0";
    
    /**
     * 应用描述
     */
    private String appDescription = "DM自动化运维工具";
    
    /**
     * 默认字符编码
     */
    private String charset = "UTF-8";
    
    /**
     * 默认时区
     */
    private String timezone = "Asia/Shanghai";
    
    /**
     * 默认分页大小
     */
    private int defaultPageSize = 20;
    
    /**
     * 最大分页大小
     */
    private int maxPageSize = 1000;
    
    /**
     * 日志配置
     */
    private Logging logging = new Logging();
    
    /**
     * 任务配置
     */
    private Task task = new Task();
    
    /**
     * 缓存配置
     */
    private Cache cache = new Cache();
    
    /**
     * 日志配置
     */
    @Data
    public static class Logging {
        
        /**
         * 是否启用请求日志
         */
        private boolean enableRequestLog = true;
        
        /**
         * 是否启用响应日志
         */
        private boolean enableResponseLog = true;
        
        /**
         * 是否启用SQL日志
         */
        private boolean enableSqlLog = false;
        
        /**
         * 最大日志长度
         */
        private int maxLogLength = 10000;
        
        /**
         * 敏感字段（会被掩码处理）
         */
        private String[] sensitiveFields = {"password", "token", "secret", "key"};
    }
    
    /**
     * 任务配置
     */
    @Data
    public static class Task {
        
        /**
         * 默认超时时间（毫秒）
         */
        private long defaultTimeout = 300000; // 5分钟
        
        /**
         * 最大重试次数
         */
        private int maxRetries = 3;
        
        /**
         * 重试间隔（毫秒）
         */
        private long retryInterval = 1000;
        
        /**
         * 异步任务线程池大小
         */
        private int asyncThreadPoolSize = 10;
        
        /**
         * 异步任务队列大小
         */
        private int asyncQueueSize = 1000;
    }
    
    /**
     * 缓存配置
     */
    @Data
    public static class Cache {
        
        /**
         * 是否启用缓存
         */
        private boolean enabled = true;
        
        /**
         * 默认缓存过期时间（秒）
         */
        private long defaultExpiration = 3600; // 1小时
        
        /**
         * 最大缓存条目数
         */
        private int maxEntries = 10000;
        
        /**
         * 缓存前缀
         */
        private String prefix = "dm:cache:";
    }
}