<template>
  <div class="nacos-config-page">
    <!-- 搜索和操作区域 -->
    <a-card class="search-section">
      <a-form
        ref="searchFormRef"
        :model="searchForm"
        layout="inline"
        @finish="handleSearch"
      >
        <a-form-item label="环境" name="environment">
          <a-select
            v-model:value="searchForm.environment"
            placeholder="请选择环境"
            style="width: 120px"
            show-search
            :filter-option="filterOption"
          >
            <a-select-option value="dev">开发环境</a-select-option>
            <a-select-option value="qa">测试环境</a-select-option>
            <a-select-option value="uat">预发环境</a-select-option>
            <a-select-option value="prd">生产环境</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="命名空间" name="namespace">
          <a-select
            v-model:value="searchForm.namespace"
            placeholder="请选择命名空间"
            style="width: 150px"
            show-search
            allow-clear
            :filter-option="filterOption"
          >
            <a-select-option value="public">public</a-select-option>
            <a-select-option value="dev">dev</a-select-option>
            <a-select-option value="qa">qa</a-select-option>
            <a-select-option value="uat">uat</a-select-option>
            <a-select-option value="prd">prd</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="配置组" name="group">
          <a-select
            v-model:value="searchForm.group"
            placeholder="请选择配置组"
            style="width: 150px"
            show-search
            allow-clear
            :filter-option="filterOption"
          >
            <a-select-option value="public">public</a-select-option>
            <a-select-option value="DEFAULT_GROUP">DEFAULT_GROUP</a-select-option>
            <a-select-option value="dev">dev</a-select-option>
            <a-select-option value="qa">qa</a-select-option>
            <a-select-option value="uat">uat</a-select-option>
            <a-select-option value="prd">prd</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="关键字(dataId/group)" name="dataId">
          <a-input
            v-model:value="searchForm.dataId"
            placeholder="请输入关键字（支持dataId、group模糊搜索）"
            style="width: 240px"
            allow-clear
          />
        </a-form-item>
        
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="searching">
              <template #icon>
                <SearchOutlined />
              </template>
              搜索
            </a-button>
            <a-button @click="resetSearch">
              重置
            </a-button>
            <a-button @click="loadAllConfigs" :loading="loading">
              <template #icon>
                <ReloadOutlined />
              </template>
              刷新
            </a-button>
            <a-button type="primary" @click="showCreateModal">
              <template #icon>
                <PlusOutlined />
              </template>
              添加配置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 配置列表 -->
    <a-card title="配置列表" class="config-list-section">
      <a-table
        :columns="columns"
        :data-source="configList"
        :loading="loading"
        :pagination="false"
        row-key="dataId"
        @change="handleTableChange"
        size="middle"
      >
        <!-- 配置类型 -->
        <template #configType="{ text }">
          <a-tag :color="getTypeColor(text)">
            {{ getTypeText(text) }}
          </a-tag>
        </template>
        
        <!-- 操作 -->
        <template #action="{ record }">
          <a-space>
            <a-button 
              type="link" 
              size="small" 
              @click="viewConfig(record)"
            >
              查看
            </a-button>
            <a-button 
              type="link" 
              size="small" 
              @click="editConfig(record)"
            >
              编辑
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 配置详情/编辑/创建模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="getModalTitle()"
      width="80%"
      :mask-closable="false"
      @cancel="handleModalCancel"
    >
      <div class="config-modal-content">
        <!-- 基本信息 -->
        <div v-if="modalMode === 'view'" class="config-info">
          <a-descriptions
            title="基本信息"
            :column="2"
            bordered
            size="small"
          >
            <a-descriptions-item label="文件名称">
              {{ currentConfig.dataId }}
            </a-descriptions-item>
            <a-descriptions-item label="配置组">
              {{ currentConfig.group }}
            </a-descriptions-item>
            <a-descriptions-item label="环境">
              <a-tag :color="getEnvColor(searchForm.environment)">
                {{ getEnvText(searchForm.environment) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="配置类型">
              <a-tag :color="getTypeColor(currentConfig.type)">
                {{ getTypeText(currentConfig.type) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="命名空间">
              {{ currentConfig.namespace || 'public' }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 创建/编辑时的基本信息表单 -->
        <div v-else class="config-form-section">
          <h4>基本信息</h4>
          <a-form :model="configForm" layout="vertical">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="文件名称" required>
                  <a-input 
                    v-model:value="configForm.dataId" 
                    placeholder="请输入配置文件名称（如application.properties）"
                    :disabled="modalMode === 'edit'"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="配置组" required>
                  <a-select 
                    v-model:value="configForm.group" 
                    placeholder="请选择配置组"
                    :disabled="modalMode === 'edit'"
                  >
                    <a-select-option value="DEFAULT_GROUP">DEFAULT_GROUP</a-select-option>
                    <a-select-option value="public">public</a-select-option>
                    <a-select-option value="dev">dev</a-select-option>
                    <a-select-option value="qa">qa</a-select-option>
                    <a-select-option value="uat">uat</a-select-option>
                    <a-select-option value="prd">prd</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="环境" required>
                  <a-select 
                    v-model:value="configForm.environment" 
                    placeholder="请选择环境"
                    :disabled="modalMode === 'edit'"
                  >
                    <a-select-option value="dev">开发环境</a-select-option>
                    <a-select-option value="qa">测试环境</a-select-option>
                    <a-select-option value="uat">预发环境</a-select-option>
                    <a-select-option value="prd">生产环境</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="配置类型" required>
                  <a-select 
                    v-model:value="configForm.type" 
                    placeholder="请选择配置类型"
                  >
                    <a-select-option value="text">TEXT</a-select-option>
                    <a-select-option value="json">JSON</a-select-option>
                    <a-select-option value="xml">XML</a-select-option>
                    <a-select-option value="yaml">YAML</a-select-option>
                    <a-select-option value="html">HTML</a-select-option>
                    <a-select-option value="properties">Properties</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="命名空间" required>
                  <a-select 
                    v-model:value="configForm.namespace" 
                    placeholder="请选择命名空间"
                  >
                    <a-select-option value="public">public</a-select-option>
                    <a-select-option value="dev">dev</a-select-option>
                    <a-select-option value="qa">qa</a-select-option>
                    <a-select-option value="uat">uat</a-select-option>
                    <a-select-option value="prd">prd</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>

        <!-- 配置内容 -->
        <div class="config-content-section">
          <div class="content-header">
            <h4>配置内容</h4>
            <a-space v-if="modalMode !== 'view'">
              <a-button size="small" @click="formatContent">
                格式化
              </a-button>
              <a-button size="small" @click="validateContent">
                验证格式
              </a-button>
            </a-space>
          </div>
          
          <div class="content-editor">
            <a-textarea
              v-if="modalMode !== 'view'"
              v-model:value="editContent"
              :rows="20"
              placeholder="请输入配置内容"
              :disabled="updating"
              class="config-textarea"
            />
            <pre v-else class="config-display">{{ currentConfig.content }}</pre>
          </div>
        </div>
      </div>
      
      <!-- 模态框底部按钮 -->
      <template #footer>
        <a-space>
          <a-button @click="handleModalCancel">
            取消
          </a-button>
          <a-button 
            v-if="modalMode === 'edit'" 
            type="primary" 
            @click="handleConfigUpdate"
            :loading="updating"
          >
            发布配置
          </a-button>
          <a-button 
            v-if="modalMode === 'create'" 
            type="primary" 
            @click="handleConfigCreate"
            :loading="updating"
          >
            创建配置
          </a-button>
        </a-space>
      </template>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { configApi } from '@/api'

// 搜索表单
const searchFormRef = ref()
const searchForm = reactive({
  environment: 'dev',
  namespace: 'public',
  group: '',
  dataId: ''
})

// 表格数据
const configList = ref([])
const loading = ref(false)
const searching = ref(false)

// 分页配置（取消分页，直接显示所有数据）
const pagination = reactive({
  current: 1,
  pageSize: 1000,
  total: 0,
  showSizeChanger: false,
  showQuickJumper: false,
  showTotal: (total) => `共 ${total} 项`
})

// 表格列配置
const columns = [
  {
    title: 'Data ID',
    dataIndex: 'dataId',
    key: 'dataId',
    width: 300,
    ellipsis: true
  },
  {
    title: '配置组',
    dataIndex: 'group',
    key: 'group',
    width: 150
  },
  {
    title: '命名空间',
    dataIndex: 'namespace',
    key: 'namespace',
    width: 150
  },
  {
    title: '配置类型',
    dataIndex: 'type',
    key: 'type',
    width: 120,
    slots: { customRender: 'configType' }
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    slots: { customRender: 'action' }
  }
]

// 模态框相关
const modalVisible = ref(false)
const modalMode = ref('view') // 'view'、'edit' 或 'create'
const currentConfig = ref({})
const editContent = ref('')
const updating = ref(false)

// 配置表单（用于创建和编辑时的基本信息）
const configForm = reactive({
  dataId: '',
  group: 'DEFAULT_GROUP',
  environment: 'dev',
  type: 'properties',
  namespace: 'public'
})

// 页面加载时获取配置列表
onMounted(() => {
  loadAllConfigs()
})

// 搜索处理
const filterOption = (input, option) => {
  return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const handleSearch = async () => {
  try {
    searching.value = true
    
    // 直接使用搜索接口，不再区分精确查询和模糊搜索
    await searchConfigs()
  } catch (error) {
    console.error('搜索失败:', error)
    message.error('搜索失败: ' + (error.message || '未知错误'))
  } finally {
    searching.value = false
  }
}

const searchSingleConfig = async () => {
  try {
    const params = {
      group: searchForm.group || 'public',
      environment: searchForm.environment || 'dev',
      namespace: searchForm.namespace || 'public'
    }
    
    const response = await configApi.getConfigDetail(searchForm.dataId, params)
    
    if (response.success && response.data) {
      configList.value = [response.data]
      pagination.total = 1
      pagination.current = 1
    } else {
      configList.value = []
      pagination.total = 0
      message.info('未找到匹配的配置')
    }
  } catch (error) {
    configList.value = []
    pagination.total = 0
    message.error('查询配置失败')
  }
}

const searchConfigs = async () => {
  try {
    const searchData = {
      ...searchForm,
      keyword: searchForm.dataId, // 使用dataId作为keyword
      pageNum: 1,
      pageSize: 1000
    }
    
    console.log('搜索请求数据:', searchData)
    const response = await configApi.searchConfigs(searchData)
    console.log('搜索响应数据:', response)
    
    if (response.success && response.data) {
      // 后端直接返回数组，前端显示所有数据
      const configArray = Array.isArray(response.data) ? response.data : []
      configList.value = configArray
      pagination.total = configArray.length
      message.success(`搜索成功，找到 ${configArray.length} 个配置`)
    } else {
      configList.value = []
      pagination.total = 0
      message.info('未找到匹配的配置')
    }
  } catch (error) {
    console.error('搜索配置失败:', error)
    configList.value = []
    pagination.total = 0
    message.error('搜索配置失败: ' + (error.message || '未知错误'))
  }
}

const resetSearch = () => {
  searchFormRef.value?.resetFields()
  searchForm.environment = 'dev'
  searchForm.namespace = 'public'
  searchForm.group = 'public'
  searchForm.dataId = ''
  loadAllConfigs()
}

const loadAllConfigs = async () => {
  try {
    loading.value = true
    
    const requestData = {
      environment: searchForm.environment || 'dev', // 使用当前选择的环境
      namespace: searchForm.namespace || 'public',
      group: searchForm.group || 'public',
      pageNum: 1,
      pageSize: 1000
    }
    
    console.log('加载配置请求数据:', requestData)
    const response = await configApi.getAllConfigs(requestData)
    console.log('加载配置响应数据:', response)
    
    if (response.success && response.data) {
      // 后端直接返回数组，前端显示所有数据
      const configArray = Array.isArray(response.data) ? response.data : []
      configList.value = configArray
      pagination.total = configArray.length
      message.success(`加载成功，共 ${configArray.length} 个配置`)
    } else {
      configList.value = []
      pagination.total = 0
      message.info('暂无配置数据')
    }
  } catch (error) {
    console.error('加载配置列表失败:', error)
    configList.value = []
    pagination.total = 0
    message.error('加载配置失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 表格操作（移除分页处理）
const handleTableChange = () => {
  // 由于已取消分页，此方法不再需要处理分页逻辑
}

// 显示创建配置模态框
const showCreateModal = () => {
  // 重置配置表单
  configForm.dataId = ''
  configForm.group = 'DEFAULT_GROUP'
  configForm.environment = searchForm.environment || 'dev'
  configForm.type = 'properties'
  configForm.namespace = 'public'
  
  // 清空编辑内容
  editContent.value = ''
  currentConfig.value = {}
  
  // 设置模态框模式并显示
  modalMode.value = 'create'
  modalVisible.value = true
}

// 获取模态框标题
const getModalTitle = () => {
  const titles = {
    'view': '配置详情',
    'edit': '编辑配置',
    'create': '添加配置'
  }
  return titles[modalMode.value] || '配置详情'
}

// 配置操作
const viewConfig = async (record) => {
  try {
    loading.value = true
    
    const params = {
      group: record.group || 'public',
      environment: searchForm.environment || 'dev', // 使用页面筛选项的环境
      namespace: record.namespace || 'public'
    }
    
    const response = await configApi.getConfigDetail(record.dataId, params)
    
    if (response.success && response.data) {
      currentConfig.value = response.data
      modalMode.value = 'view'
      modalVisible.value = true
    } else {
      message.error('获取配置详情失败')
    }
  } catch (error) {
    message.error('获取配置详情失败')
  } finally {
    loading.value = false
  }
}

const editConfig = async (record) => {
  try {
    loading.value = true
    
    const params = {
      group: record.group || 'public',
      environment: searchForm.environment || 'dev', // 使用页面筛选项的环境
      namespace: record.namespace || 'public'
    }
    
    const response = await configApi.getConfigDetail(record.dataId, params)
    
    if (response.success && response.data) {
      currentConfig.value = response.data
      editContent.value = response.data.content || ''
      modalMode.value = 'edit'
      modalVisible.value = true
    } else {
      message.error('获取配置详情失败')
    }
  } catch (error) {
    message.error('获取配置详情失败')
  } finally {
    loading.value = false
  }
}

const handleConfigUpdate = async () => {
  if (!editContent.value.trim()) {
    message.warning('配置内容不能为空')
    return
  }
  
  try {
    updating.value = true
    
    const updateData = {
      dataId: currentConfig.value.dataId,
      group: currentConfig.value.group,
      namespace: currentConfig.value.namespace,
      content: editContent.value,
      type: currentConfig.value.type,
      environment: searchForm.environment || 'dev' // 使用页面筛选项的环境
    }
    
    const response = await configApi.updateConfig(currentConfig.value.dataId, updateData)
    
    if (response.success) {
      message.success('配置发布成功')
      modalVisible.value = false
      loadAllConfigs() // 刷新列表
    } else {
      message.error('配置发布失败: ' + (response.message || '未知错误'))
    }
  } catch (error) {
    message.error('配置发布失败: ' + (error.message || '网络错误'))
  } finally {
    updating.value = false
  }
}

const handleModalCancel = () => {
  modalVisible.value = false
  currentConfig.value = {}
  editContent.value = ''
}

// 内容处理
const formatContent = () => {
  try {
    if (currentConfig.value.type === 'json') {
      const parsed = JSON.parse(editContent.value)
      editContent.value = JSON.stringify(parsed, null, 2)
      message.success('JSON格式化成功')
    } else if (currentConfig.value.type === 'yaml') {
      // YAML格式化（简单处理）
      message.info('YAML格式化功能待完善')
    } else {
      message.info('当前类型不支持格式化')
    }
  } catch (error) {
    message.error('格式化失败: ' + error.message)
  }
}

const validateContent = () => {
  try {
    if (currentConfig.value.type === 'json') {
      JSON.parse(editContent.value)
      message.success('JSON格式验证通过')
    } else if (currentConfig.value.type === 'properties') {
      // 简单的properties格式验证
      const lines = editContent.value.split('\n')
      const invalidLines = lines
        .filter((line, index) => {
          line = line.trim()
          return line && !line.startsWith('#') && !line.includes('=')
        })
        .map((line, index) => index + 1)
      
      if (invalidLines.length > 0) {
        message.error(`Properties格式错误，行号: ${invalidLines.join(', ')}`)
      } else {
        message.success('Properties格式验证通过')
      }
    } else {
      message.success('内容格式验证通过')
    }
  } catch (error) {
    message.error('格式验证失败: ' + error.message)
  }
}

// 样式和文本处理
const getTypeColor = (type) => {
  const colors = {
    'json': 'blue',
    'properties': 'green',
    'yaml': 'orange',
    'xml': 'purple',
    'text': 'default'
  }
  return colors[type] || 'default'
}

const getTypeText = (type) => {
  const texts = {
    'json': 'JSON',
    'properties': 'Properties',
    'yaml': 'YAML',
    'xml': 'XML',
    'text': 'Text'
  }
  return texts[type] || type?.toUpperCase() || 'UNKNOWN'
}

const getEnvColor = (env) => {
  const colors = {
    'dev': 'blue',
    'qa': 'cyan',
    'uat': 'orange',
    'prd': 'red'
  }
  return colors[env] || 'default'
}

const getEnvText = (env) => {
  const texts = {
    'dev': '开发',
    'qa': '测试',
    'uat': '预发',
    'prd': '生产'
  }
  return texts[env] || env?.toUpperCase() || '未知'
}
</script>

<style scoped>
.nacos-config-page {
  max-width: 1400px;
  margin: 0 auto;
}

.search-section {
  margin-bottom: 24px;
}

.config-list-section {
  min-height: 400px;
}

.config-modal-content {
  max-height: 70vh;
  overflow-y: auto;
}

.config-info {
  margin-bottom: 24px;
}

.config-content-section {
  margin-top: 24px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.content-header h4 {
  margin: 0;
}

.content-editor {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.config-textarea {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  border: none;
  resize: none;
}

.config-textarea:focus {
  box-shadow: none;
}

.config-display {
  background: #f5f5f5;
  padding: 16px;
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 400px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nacos-config-page {
    padding: 0 16px;
  }
  
  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>