package com.just.template.core;

/**
 * 模板类型枚举
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
public enum TemplateType {
    
    /**
     * Bootstrap配置模板
     */
    BOOTSTRAP("bootstrap", "bootstrap配置文件模板"),
    
    /**
     * Application Nacos配置模板  
     */
    APPLICATION_NACOS("application-nacos", "application-nacos配置文件模板"),
    
    /**
     * POM文件模板
     */
    POM("pom", "pom.xml文件模板");
    
    private final String code;
    private final String description;
    
    TemplateType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取模板类型
     * 
     * @param code 类型代码
     * @return 模板类型
     */
    public static TemplateType fromCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (TemplateType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        
        throw new IllegalArgumentException("Unknown template type code: " + code);
    }
} 