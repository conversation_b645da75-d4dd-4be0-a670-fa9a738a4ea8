package com.just.git.config;

import com.just.git.model.GitCredentials;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Git配置
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GitConfig {
    
    /**
     * Git服务器基础URL
     */
    private String baseUrl = "http://gitlab.company.com";
    
    /**
     * 默认Git认证信息
     */
    private GitCredentials defaultCredentials;
    
    /**
     * 本地工作目录
     */
    private Path workspaceDirectory = Paths.get(System.getProperty("user.home"), "git-workspace");
    
    /**
     * 是否自动创建工作目录
     */
    private boolean autoCreateWorkspace = true;
    
    /**
     * 是否启用详细日志记录
     */
    private boolean verboseLogging = false;
    
    /**
     * 默认用户名
     */
    private String defaultUserName = "admin";
    
    /**
     * 默认用户邮箱
     */
    private String defaultUserEmail = "<EMAIL>";
    
    /**
     * 默认分支名称
     */
    private String defaultBranch = "master";
    
    /**
     * 默认超时时间（秒）
     */
    private int defaultTimeoutSeconds = 30;

    private GitCoreProperties gitCoreProperties;
    
    /**
     * 验证配置有效性
     */
    public void validate() {
        if (baseUrl == null || baseUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("baseUrl不能为空");
        }
        
        if (workspaceDirectory == null) {
            throw new IllegalArgumentException("workspaceDirectory不能为空");
        }
        
        if (defaultBranch == null || defaultBranch.trim().isEmpty()) {
            throw new IllegalArgumentException("defaultBranch不能为空");
        }
        
        if (defaultTimeoutSeconds <= 0) {
            throw new IllegalArgumentException("defaultTimeoutSeconds必须大于0");
        }
    }



    /**
     * 获取服务仓库URL
     */
    public String getServiceRepositoryUrl(String serviceName) {
        return String.format("%s/microservice/%s.git", baseUrl.replaceAll("/$", ""), serviceName);
    }
}