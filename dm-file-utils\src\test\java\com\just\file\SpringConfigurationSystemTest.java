package com.just.file;

import com.just.common.enums.EnvironmentType;
import com.just.file.config.ExcelConfigProperties;
import com.just.file.config.FileUtilsAutoConfiguration;
import com.just.file.model.ConfigurationDataCenter;
import com.just.file.model.ConfigurationType;
import com.just.file.service.ConfigurationDataFactory;
import com.just.file.service.ExcelConfigService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.CacheManager;
import org.springframework.test.context.TestPropertySource;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Spring配置系统集成测试
 * 测试重构后的Excel配置服务的完整功能
 * 
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
@SpringBootTest(classes = {
    FileUtilsAutoConfiguration.class
})
@EnableConfigurationProperties(ExcelConfigProperties.class)
@TestPropertySource(properties = {
    "spring.cache.type=simple",
    "dm.file.supportedEnvironments=dev,qa,uat,prod",
    "dm.file.nacos.enabled=true",
    "dm.file.url.enabled=true"
})
public class SpringConfigurationSystemTest {

    @Autowired
    private ExcelConfigService excelConfigService;

    @Autowired
    private ConfigurationDataFactory configurationDataFactory;

    @Autowired
    private ExcelConfigProperties configProperties;

    @Autowired
    private CacheManager cacheManager;

    @Test
    public void testConfigurationProperties() {
        assertNotNull(configProperties);

        // 测试默认文件配置
        assertTrue(configProperties.getFileConfig("nacos").isPresent());
        assertTrue(configProperties.getFileConfig("url").isPresent());
    }

    @Test
    public void testCacheManager() {
        assertNotNull(cacheManager);
        // 简单缓存类型可能没有预定义的缓存名称
        assertNotNull(cacheManager);
    }

    @Test
    public void testExcelConfigService() {
        assertNotNull(excelConfigService);
    }

    @Test
    public void testConfigurationDataFactory() {
        assertNotNull(configurationDataFactory);
    }

    @Test
    public void testGetAllNacosConfigurations() {
        // 测试获取所有Nacos配置
        List<ConfigurationDataCenter.NacosProperty> configs = excelConfigService.readAllNacosConfigs();
        
        assertNotNull(configs);
        // 即使文件不存在，也应该返回空列表而不是null
        assertTrue(configs.isEmpty() || !configs.isEmpty());
    }

    @Test
    public void testGetAllUrlConfigurations() {
        // 测试获取所有URL配置
        List<ConfigurationDataCenter.NacosProperty> configs = excelConfigService.readAllUrlMappings();
        
        assertNotNull(configs);
        // 即使文件不存在，也应该返回空列表而不是null
        assertTrue(configs.isEmpty() || !configs.isEmpty());
    }

    @Test
    public void testGetUrlConfigurationsByEnvironment() {
        // 测试按环境获取URL配置
        List<ConfigurationDataCenter.NacosProperty> devConfigs = excelConfigService.readUrlMappings("dev");
        List<ConfigurationDataCenter.NacosProperty> qaConfigs = excelConfigService.readUrlMappings("qa");
        
        assertNotNull(devConfigs);
        assertNotNull(qaConfigs);
    }

    @Test
    public void testGetConfigurationsByType() {
        // 测试按类型获取配置
        List<ConfigurationDataCenter.NacosProperty> nacosConfigs = 
            excelConfigService.readConfigurations(ConfigurationType.NACOS);
        List<ConfigurationDataCenter.NacosProperty> urlConfigs = 
            excelConfigService.readConfigurations(ConfigurationType.URL_MAPPING);
        
        assertNotNull(nacosConfigs);
        assertNotNull(urlConfigs);
    }

    @Test
    public void testGetConfigurationsByTypeAndEnvironment() {
        // 测试按类型和环境获取配置
        List<ConfigurationDataCenter.NacosProperty> configs = 
            excelConfigService.readConfigurations(ConfigurationType.URL_MAPPING, "dev");
        
        assertNotNull(configs);
    }

    @Test
    public void testRefreshOperations() {
        // 测试刷新操作
        assertDoesNotThrow(() -> {
            excelConfigService.refreshAllConfigurations();
            excelConfigService.refreshConfiguration(ConfigurationType.NACOS);
            excelConfigService.refreshConfiguration(ConfigurationType.URL_MAPPING);
        });
    }

    @Test
    public void testConfigurationDataFactoryMethods() {
        // 测试配置数据工厂的方法
        assertDoesNotThrow(() -> {
            configurationDataFactory.getAllNacosConfigurations();
            configurationDataFactory.getAllUrlMappings();
            configurationDataFactory.getUrlMappings("dev");
            configurationDataFactory.getSupportedEnvironments(ConfigurationType.NACOS);
            configurationDataFactory.getStatistics();
        });
    }

    @Test
    public void testConfigurationTypes() {
        // 测试配置类型枚举
        assertEquals(ConfigurationType.NACOS, ConfigurationType.fromCode("nacos"));
        assertEquals(ConfigurationType.URL_MAPPING, ConfigurationType.fromCode("url"));
        
        assertNotNull(ConfigurationType.NACOS.getCode());
        assertNotNull(ConfigurationType.URL_MAPPING.getCode());
    }

    @Test
    public void testErrorHandling() {
        // 测试错误处理 - 这些调用不应该抛出异常
        assertDoesNotThrow(() -> {
            // 即使配置文件不存在，也应该优雅处理
            excelConfigService.readAllNacosConfigs();
            excelConfigService.readAllUrlMappings();
            excelConfigService.readUrlMappings("nonexistent");
        });
    }

    @Test
    public void testConfigurationValidation() {
        // 测试配置验证
        assertThrows(IllegalArgumentException.class, () -> {
            ConfigurationType.fromCode("invalid_type");
        });
    }

    @Test
    public void testEnvironmentTypeIntegration() {
        // 测试与现有EnvironmentType枚举的集成
        assertTrue(EnvironmentType.isValidCode("dev"));
        assertTrue(EnvironmentType.isValidCode("qa"));
        assertTrue(EnvironmentType.isValidCode("uat"));
        assertTrue(EnvironmentType.isValidCode("prod"));
    }

    @Test
    public void testServiceIntegration() {
        // 测试服务集成 - 验证所有服务都正确注入和初始化
        assertNotNull(excelConfigService);
        assertNotNull(configurationDataFactory);
        assertNotNull(configProperties);
        
        // 验证服务之间的依赖关系正常
        assertDoesNotThrow(() -> {
            excelConfigService.readAllNacosConfigs();
            configurationDataFactory.getStatistics();
        });
    }

    @Test
    public void testStatistics() {
        // 测试统计信息
        assertDoesNotThrow(() -> {
            Map<String, Object> stats = configurationDataFactory.getStatistics();
            assertNotNull(stats);
            assertTrue(stats.containsKey("totalConfigurations"));
            assertTrue(stats.containsKey("configurationsByType"));
            assertTrue(stats.containsKey("configurationsByEnvironment"));
        });
    }
} 