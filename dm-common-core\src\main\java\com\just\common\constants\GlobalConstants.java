package com.just.common.constants;

/**
 * 全局常量定义
 * 包含项目中使用的所有常量
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
public final class GlobalConstants {
    
    private GlobalConstants() {
        // 私有构造函数，防止实例化
    }
    
    // ======================== Git相关常量 ========================
    
    /**
     * 默认分支前缀
     */
    public static final String DEFAULT_BRANCH_PREFIX = "auto-upgrade-";
    
    /**
     * Nacos升级分支名
     */
    public static final String NACOS_UPGRADE_BRANCH = "nacos-upgrade";
    
    /**
     * 配置服务器升级分支名
     */
    public static final String CONFIG_SERVER_BRANCH = "auto-upgrade-config-server";
    
    /**
     * 默认主分支名
     */
    public static final String DEFAULT_MAIN_BRANCH = "master";
    
    // ======================== 环境相关常量 ========================
    
    /**
     * 支持的环境列表
     */
    public static final String[] SUPPORTED_ENVIRONMENTS = {"dev", "qa", "uat", "prd"};
    
    /**
     * 默认环境
     */
    public static final String DEFAULT_ENVIRONMENT = "dev";
    
    // ======================== 配置文件常量 ========================
    
    /**
     * Bootstrap配置文件名
     */
    public static final String BOOTSTRAP_PROPERTIES = "bootstrap.properties";
    
    /**
     * Application配置文件名
     */
    public static final String APPLICATION_PROPERTIES = "application.properties";
    
    /**
     * Application Nacos配置文件名
     */
    public static final String APPLICATION_NACOS_PROPERTIES = "application-nacos.properties";
    
    /**
     * POM文件名
     */
    public static final String POM_XML = "pom.xml";
    
    /**
     * Logback配置文件名
     */
    public static final String LOGBACK_XML = "logback.xml";
    
    /**
     * Logback Spring配置文件名
     */
    public static final String LOGBACK_SPRING_XML = "logback-spring.xml";
    
    // ======================== 路径相关常量 ========================
    
    /**
     * Maven源代码路径
     */
    public static final String MAVEN_SRC_MAIN_PATH = "src/main";
    
    /**
     * Maven资源路径
     */
    public static final String MAVEN_RESOURCES_PATH = "src/main/resources";
    
    /**
     * Maven Java源码路径
     */
    public static final String MAVEN_JAVA_PATH = "src/main/java";
    
    /**
     * Maven测试路径
     */
    public static final String MAVEN_TEST_PATH = "src/test/java";
    
    /**
     * Internal包路径
     */
    public static final String INTERNAL_PACKAGE_PATH = "internal";
    
    // ======================== Nacos相关常量 ========================
    
    /**
     * Nacos配置组名
     */
    public static final String NACOS_DEFAULT_GROUP = "DEFAULT_GROUP";
    
    /**
     * Nacos配置超时时间（毫秒）
     */
    public static final int NACOS_CONFIG_TIMEOUT = 5000;
    
    /**
     * Nacos地址环境变量名
     */
    public static final String NACOS_ADDR_VAR = "NACOS_ADDR";
    
    /**
     * Nacos命名空间环境变量名
     */
    public static final String NACOS_NAMESPACE_VAR = "NACOS_NAMESPACE";
    
    /**
     * Nacos访问密钥环境变量名
     */
    public static final String NACOS_ACCESS_KEY_VAR = "NACOS_ACCESS_KEY";
    
    /**
     * Nacos访问密钥Secret环境变量名
     */
    public static final String NACOS_SECRET_KEY_VAR = "NACOS_SECRET_KEY";
    
    // ======================== GitLab相关常量 ========================
    
    /**
     * GitLab令牌环境变量名
     */
    public static final String GITLAB_TOKEN_VAR = "GITLAB_TOKEN";
    
    /**
     * GitLab URL环境变量名
     */
    public static final String GITLAB_URL_VAR = "GITLAB_URL";
    
    /**
     * GitLab操作超时时间（毫秒）
     */
    public static final int GITLAB_TIMEOUT = 30000;
    
    // ======================== 文件操作常量 ========================
    
    /**
     * 默认字符编码
     */
    public static final String DEFAULT_CHARSET = "UTF-8";
    
    /**
     * 临时文件前缀
     */
    public static final String TEMP_FILE_PREFIX = "dm-auto-utils-";
    
    /**
     * 备份文件后缀
     */
    public static final String BACKUP_FILE_SUFFIX = ".backup";
    
    /**
     * 日期时间格式
     */
    public static final String DATETIME_FORMAT = "yyyyMMdd_HHmmss";
    
    // ======================== Spring相关常量 ========================
    
    /**
     * Spring应用名称属性
     */
    public static final String SPRING_APPLICATION_NAME = "spring.application.name";
    
    /**
     * Spring激活的配置文件属性
     */
    public static final String SPRING_PROFILES_ACTIVE = "spring.profiles.active";
    
    /**
     * Nacos配置服务器地址属性
     */
    public static final String NACOS_CONFIG_SERVER_ADDR = "spring.cloud.nacos.config.server-addr";
    
    /**
     * Nacos配置命名空间属性
     */
    public static final String NACOS_CONFIG_NAMESPACE = "spring.cloud.nacos.config.namespace";
    
    /**
     * Nacos配置访问密钥属性
     */
    public static final String NACOS_CONFIG_ACCESS_KEY = "spring.cloud.nacos.config.access-key";
    
    /**
     * Nacos配置访问密钥Secret属性
     */
    public static final String NACOS_CONFIG_SECRET_KEY = "spring.cloud.nacos.config.secret-key";
    
    // ======================== FeignClient相关常量 ========================
    
    /**
     * FeignClient注解名
     */
    public static final String FEIGN_CLIENT_ANNOTATION = "@FeignClient";
    
    /**
     * FeignClient URL属性
     */
    public static final String FEIGN_CLIENT_URL_ATTR = "url";
    
    /**
     * FeignClient名称属性
     */
    public static final String FEIGN_CLIENT_NAME_ATTR = "name";
    
    // ======================== 正则表达式常量 ========================
    
    /**
     * 环境变量占位符正则表达式
     */
    public static final String ENV_VAR_PLACEHOLDER_REGEX = "\\$\\{([^}]+)\\}";
    
    /**
     * FeignClient URL正则表达式
     */
    public static final String FEIGN_CLIENT_URL_REGEX = "url\\s*=\\s*\"?(\\$\\{[^}]+\\})\"?";
    
    /**
     * FeignClient名称正则表达式
     */
    public static final String FEIGN_CLIENT_NAME_REGEX = "name\\s*=\\s*\"([^\"]+)\"";
    
    // ======================== 默认值常量 ========================
    
    /**
     * 默认刷新配置
     */
    public static final String DEFAULT_REFRESH = "true";
    
    /**
     * 默认注册配置
     */
    public static final String DEFAULT_REGISTER = "true";
    
    /**
     * 默认发布配置
     */
    public static final String DEFAULT_PUBLISH = "false";
    
    /**
     * 默认超时时间
     */
    public static final int DEFAULT_TIMEOUT = 5000;
    
    // ======================== HTTP相关常量 ========================
    
    /**
     * HTTP成功状态码
     */
    public static final int HTTP_SUCCESS = 200;
    
    /**
     * HTTP客户端错误状态码
     */
    public static final int HTTP_CLIENT_ERROR = 400;
    
    /**
     * HTTP服务器错误状态码
     */
    public static final int HTTP_SERVER_ERROR = 500;
}