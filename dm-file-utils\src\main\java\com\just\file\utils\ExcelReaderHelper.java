package com.just.file.utils;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.List;

/**
 * Excel读取辅助类
 * 提供通用的Excel读取功能
 * 
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
@Slf4j
@Component
public class ExcelReaderHelper {
    
    /**
     * 读取Excel工作表数据
     * 
     * @param fileName 文件名
     * @param sheetName 工作表名
     * @return 行数据列表
     */
    public List<List<Object>> readSheet(String fileName, String sheetName) {
        try (ExcelReader reader = getExcelReader(fileName, sheetName)) {
            return reader.read();
        } catch (Exception e) {
            log.error("读取Excel工作表失败: fileName={}, sheetName={}", fileName, sheetName, e);
            throw new RuntimeException("读取Excel工作表失败: " + fileName + ", 工作表: " + sheetName, e);
        }
    }
    
    /**
     * 获取Excel读取器
     * 
     * @param fileName 文件名
     * @param sheetName 工作表名
     * @return ExcelReader
     */
    private ExcelReader getExcelReader(String fileName, String sheetName) {
        try {
            InputStream inputStream = getInputStream(fileName);
            return ExcelUtil.getReader(inputStream, sheetName);
        } catch (Exception e) {
            throw new RuntimeException("创建Excel读取器失败: " + fileName + ", 工作表: " + sheetName, e);
        }
    }
    
    /**
     * 获取输入流
     * 
     * @param fileName 文件名
     * @return InputStream
     */
    private InputStream getInputStream(String fileName) {
        try {
            // 首先尝试从classpath读取
            try {
                ClassPathResource resource = new ClassPathResource(fileName);
                return resource.getInputStream();
            } catch (Exception e) {
                // 如果classpath读取失败，尝试直接从文件系统读取
                InputStream inputStream = getClass().getClassLoader().getResourceAsStream(fileName);
                if (inputStream == null) {
                    throw new RuntimeException("无法找到配置文件: " + fileName);
                }
                return inputStream;
            }
        } catch (Exception e) {
            throw new RuntimeException("获取文件输入流失败: " + fileName, e);
        }
    }
    
    /**
     * 安全获取字符串值
     * 
     * @param row 行数据
     * @param index 列索引
     * @return 字符串值
     */
    public String getStringValue(List<Object> row, int index) {
        if (row.size() <= index) {
            return null;
        }
        Object value = row.get(index);
        return value != null ? value.toString().trim() : null;
    }
    
    /**
     * 检查行是否为空
     * 
     * @param row 行数据
     * @return 是否为空
     */
    public boolean isEmptyRow(List<Object> row) {
        return row == null || row.isEmpty() || 
               row.stream().allMatch(cell -> cell == null || cell.toString().trim().isEmpty());
    }
} 