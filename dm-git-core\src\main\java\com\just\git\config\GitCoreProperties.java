package com.just.git.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Git核心模块配置属性
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
@ConfigurationProperties(prefix = "dm.git")
public class GitCoreProperties {
    
    /**
     * Git服务器基础URL
     */
    private String baseUrl = "http://gitlab.company.com";
    
    /**
     * 本地工作目录
     */
    private Path workspaceDirectory = Paths.get(System.getProperty("user.home"), "git-workspace");
    
    /**
     * 默认分支名称
     */
    private String defaultBranch = "master";
    
    /**
     * 默认超时时间（秒）
     */
    private int defaultTimeoutSeconds = 30;
    
    /**
     * 是否自动创建工作目录
     */
    private boolean autoCreateWorkspace = true;
    
    /**
     * 是否启用详细日志
     */
    private boolean verboseLogging = false;
    
    /**
     * 默认用户名（用于提交）
     */
    private String defaultUserName = "dm-auto-utils";
    
    /**
     * 默认用户邮箱（用于提交）
     */
    private String defaultUserEmail = "<EMAIL>";
    
    /**
     * 是否启用浅克隆
     */
    private boolean enableShallowClone = false;
    
    /**
     * 浅克隆深度
     */
    private int shallowCloneDepth = 1;

    /**
     * 用户名
     */
    private String loginUserName;

    /**
     * 密码
     */
    private String loginPassword;
    /**
     * 密钥
     */
    private String token;
    /**
     * ssh密钥
     */
    private String sshKeyPath;
} 