package com.just.file.config;

import com.just.file.service.*;
import com.just.file.utils.ExcelReaderHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * File Utils自动配置
 * 重构后的完整配置，包含所有必要的组件
 * 
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
@Slf4j
@Configuration
@EnableCaching
@EnableConfigurationProperties(ExcelConfigProperties.class)
@ComponentScan(basePackages = "com.just.file")
public class FileUtilsAutoConfiguration {

    public FileUtilsAutoConfiguration() {
        log.info("=== DM File Utils 自动配置启动 ===");
    }

    // ==================== 缓存配置 ====================

    /**
     * 缓存管理器
     */
    @Bean
    @ConditionalOnMissingBean
    public CacheManager cacheManager() {
        log.debug("配置 CacheManager Bean");
        return new ConcurrentMapCacheManager("excelConfigCache", "configurationDataCache");
    }

    // ==================== 核心工具类 Bean 配置 ====================

    /**
     * Excel读取帮助类
     */
    @Bean
    @ConditionalOnMissingBean
    public ExcelReaderHelper excelReaderHelper() {
        log.debug("配置 ExcelReaderHelper Bean");
        return new ExcelReaderHelper();
    }

    // ==================== 配置处理器 Bean 配置 ====================

    /**
     * Nacos配置处理器
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "dm.file.nacos", name = "enabled", havingValue = "true", matchIfMissing = true)
    public NacosConfigurationProcessor nacosConfigurationProcessor(ExcelReaderHelper excelReaderHelper) {
        log.debug("配置 NacosConfigurationProcessor Bean");
        return new NacosConfigurationProcessor(excelReaderHelper);
    }

    /**
     * URL映射配置处理器
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "dm.file.url", name = "enabled", havingValue = "true", matchIfMissing = true)
    public UrlMappingConfigurationProcessor urlMappingConfigurationProcessor(ExcelReaderHelper excelReaderHelper) {
        log.debug("配置 UrlMappingConfigurationProcessor Bean");
        return new UrlMappingConfigurationProcessor(excelReaderHelper);
    }

    // ==================== 工厂类 Bean 配置 ====================

    /**
     * 配置数据工厂
     */
    @Bean
    @ConditionalOnMissingBean
    public ConfigurationDataFactory configurationDataFactory(
            ExcelConfigProperties configProperties,
            List<ConfigurationProcessor> processors,
            List<LoadingEnvAware> loadingEnvAwares) {
        log.debug("配置 ConfigurationDataFactory Bean，处理器数量: {}", processors.size());
        return new ConfigurationDataFactory(configProperties, processors,loadingEnvAwares);
    }

    // ==================== 服务层 Bean 配置 ====================

    /**
     * Excel配置服务
     */
    @Bean
    @ConditionalOnMissingBean
    public ExcelConfigService excelConfigService(ConfigurationDataFactory configurationDataFactory) {
        log.debug("配置 ExcelConfigService Bean");
        return new ExcelConfigService(configurationDataFactory);
    }

    /**
     * 文件操作服务
     */
    @Bean
    @ConditionalOnMissingBean
    public FileOperationService fileOperationService() {
        log.debug("配置 FileOperationService Bean");
        return new FileOperationService();
    }

    // ==================== 配置验证和启动信息 ====================

    /**
     * 配置验证Bean - 在所有Bean创建完成后执行验证
     */
    @Bean
    @ConditionalOnMissingBean
    public FileUtilsConfigValidator configValidator(
            ExcelConfigProperties configProperties,
            List<ConfigurationProcessor> processors,
            ConfigurationDataFactory configurationDataFactory) {
        
        log.info("=== DM File Utils 配置验证 ===");
        log.info("配置处理器数量: {}", processors.size());
        
        processors.forEach(processor -> 
            log.info("- {}: {}", processor.getClass().getSimpleName(), processor.getSupportedType()));
        
        log.info("Excel配置文件数量: {}", 
            configProperties.getFiles() != null ? configProperties.getFiles().size() : 0);
        
        if (configProperties.getFiles() != null) {
            configProperties.getFiles().forEach((key, fileConfig) -> 
                log.info("- {}: {} (enabled: {})", key, fileConfig.getPath(), fileConfig.isEnabled()));
        }
        
        log.info("=== DM File Utils 配置完成 ===");
        
        return new FileUtilsConfigValidator();
    }

    // ==================== 内部配置验证类 ====================

    /**
     * 配置验证器
     */
    public static class FileUtilsConfigValidator {
        
        public FileUtilsConfigValidator() {
            // 执行配置验证逻辑
            validateConfiguration();
        }
        
        private void validateConfiguration() {
            log.debug("执行 File Utils 配置验证");
            // 这里可以添加额外的配置验证逻辑
        }
        
        public boolean isConfigurationValid() {
            return true;
        }
    }
}