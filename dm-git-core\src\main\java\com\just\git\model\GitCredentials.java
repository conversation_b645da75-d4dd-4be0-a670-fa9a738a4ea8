package com.just.git.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Git认证信息
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GitCredentials {
    
    /**
     * 认证类型
     */
    public enum AuthType {
        TOKEN,      // 令牌认证（推荐）
        USERNAME_PASSWORD,  // 用户名密码
        SSH_KEY     // SSH密钥
    }
    
    /**
     * 认证类型
     */
    private AuthType authType = AuthType.TOKEN;
    
    /**
     * 访问令牌（用于TOKEN认证）
     */
    private String token;
    
    /**
     * 用户名（用于USERNAME_PASSWORD认证）
     */
    private String username;
    
    /**
     * 密码（用于USERNAME_PASSWORD认证）
     */
    private String password;
    
    /**
     * SSH私钥路径（用于SSH_KEY认证）
     */
    private String sshKeyPath;
    
    /**
     * SSH密钥密码（可选）
     */
    private String sshKeyPassword;
    
    /**
     * 用户邮箱（用于提交者信息）
     */
    private String email;
    
    /**
     * 用户显示名称（用于提交者信息）
     */
    private String displayName;
    
    /**
     * 创建Token认证
     */
    public static GitCredentials ofToken(String token) {
        return GitCredentials.builder()
                .authType(AuthType.TOKEN)
                .token(token)
                .build();
    }
    
    /**
     * 创建用户名密码认证
     */
    public static GitCredentials ofUsernamePassword(String username, String password) {
        return GitCredentials.builder()
                .authType(AuthType.USERNAME_PASSWORD)
                .username(username)
                .password(password)
                .build();
    }
    
    /**
     * 创建SSH密钥认证
     */
    public static GitCredentials ofSshKey(String sshKeyPath, String sshKeyPassword) {
        return GitCredentials.builder()
                .authType(AuthType.SSH_KEY)
                .sshKeyPath(sshKeyPath)
                .sshKeyPassword(sshKeyPassword)
                .build();
    }
    
    /**
     * 验证认证信息是否有效
     */
    public boolean isValid() {
        switch (authType) {
            case TOKEN:
                return token != null && !token.trim().isEmpty();
            case USERNAME_PASSWORD:
                return username != null && !username.trim().isEmpty() && 
                       password != null && !password.trim().isEmpty();
            case SSH_KEY:
                return sshKeyPath != null && !sshKeyPath.trim().isEmpty();
            default:
                return false;
        }
    }
} 