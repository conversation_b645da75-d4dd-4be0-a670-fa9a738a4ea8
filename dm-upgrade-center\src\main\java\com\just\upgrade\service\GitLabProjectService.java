package com.just.upgrade.service;

import com.just.git.model.GitLabProject;
import com.just.upgrade.model.GitLabSearchRequest;

import java.util.List;

/**
 * GitLab项目服务接口
 *
 * <AUTHOR>
 * @since 2.0.0
 */
public interface GitLabProjectService {

    /**
     * 获取GitLab所有项目基本信息
     *
     * @param request 搜索请求
     * @return 项目列表
     */
    List<GitLabProject> getAllProjects(GitLabSearchRequest request);

    /**
     * 根据关键词搜索GitLab项目
     *
     * @param request 搜索请求
     * @return 项目列表
     */
    List<GitLabProject> searchProjects(GitLabSearchRequest request);

    /**
     * 获取指定项目详细信息
     *
     * @param projectId 项目ID
     * @return 项目详情
     */
    GitLabProject getProjectDetail(String projectId);

    /**
     * 获取指定项目的分支列表
     *
     * @param projectId 项目ID
     * @return 分支列表
     */
    List<org.gitlab4j.api.models.Branch> getProjectBranches(String projectId);
} 