package com.just.upgrade;

import com.just.upgrade.controller.UpgradeCenterController;
import com.just.upgrade.enums.DmNacosGroupEnvironmentEnum;
import com.just.upgrade.model.UpgradeRequest;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = DmUpgradeCenterApplication.class)
public class Test {

    @Resource
    UpgradeCenterController upgradeCenterController;


    @org.junit.Test
    public void test() {
        UpgradeRequest request = new UpgradeRequest();
        request.setEnvironment("dev");
        request.setServiceName("onshore-broker-quotation");
        request.setUpgradeRegistry(true);
        request.setAutoRefresh(false);
        request.setPublishMseNacos(false);
        request.setBranchName("nacos-upgrade-guoqing");
        request.setDmGroup(DmNacosGroupEnvironmentEnum.PRICE.getGroupName());
        upgradeCenterController.upgradeNacosService(request);
    }
}
