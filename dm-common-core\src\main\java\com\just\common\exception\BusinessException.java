package com.just.common.exception;

import com.just.common.constants.ErrorCodes;
import lombok.Getter;

/**
 * 业务异常
 * 用于业务逻辑层抛出的可预期异常
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
@Getter
public class BusinessException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private final String errorCode;
    
    /**
     * 错误消息
     */
    private final String errorMessage;
    
    /**
     * 错误详情（可选）
     */
    private final Object errorDetails;
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     */
    public BusinessException(String message) {
        super(message);
        this.errorCode = ErrorCodes.SYSTEM_ERROR;
        this.errorMessage = message;
        this.errorDetails = null;
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param message 错误消息
     */
    public BusinessException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.errorMessage = message;
        this.errorDetails = null;
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param message 错误消息
     * @param errorDetails 错误详情
     */
    public BusinessException(String errorCode, String message, Object errorDetails) {
        super(message);
        this.errorCode = errorCode;
        this.errorMessage = message;
        this.errorDetails = errorDetails;
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原因异常
     */
    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = ErrorCodes.SYSTEM_ERROR;
        this.errorMessage = message;
        this.errorDetails = null;
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param message 错误消息
     * @param cause 原因异常
     */
    public BusinessException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorMessage = message;
        this.errorDetails = null;
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param message 错误消息
     * @param errorDetails 错误详情
     * @param cause 原因异常
     */
    public BusinessException(String errorCode, String message, Object errorDetails, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorMessage = message;
        this.errorDetails = errorDetails;
    }
    
    // ======================== 便捷方法 ========================
    
    /**
     * 创建参数错误异常
     * 
     * @param message 错误消息
     * @return 业务异常
     */
    public static BusinessException paramError(String message) {
        return new BusinessException(ErrorCodes.PARAM_ERROR, message);
    }
    
    /**
     * 创建参数为空异常
     * 
     * @param paramName 参数名
     * @return 业务异常
     */
    public static BusinessException paramNull(String paramName) {
        return new BusinessException(ErrorCodes.PARAM_NULL, "参数不能为空: " + paramName);
    }
    
    /**
     * 创建参数格式错误异常
     * 
     * @param paramName 参数名
     * @param expectedFormat 期望格式
     * @return 业务异常
     */
    public static BusinessException paramFormatError(String paramName, String expectedFormat) {
        return new BusinessException(ErrorCodes.PARAM_FORMAT_ERROR, 
                String.format("参数格式错误: %s，期望格式: %s", paramName, expectedFormat));
    }
    
    /**
     * 创建资源不存在异常
     * 
     * @param resourceName 资源名称
     * @return 业务异常
     */
    public static BusinessException resourceNotFound(String resourceName) {
        return new BusinessException(ErrorCodes.RESOURCE_NOT_FOUND, "资源不存在: " + resourceName);
    }
    
    /**
     * 创建资源已存在异常
     * 
     * @param resourceName 资源名称
     * @return 业务异常
     */
    public static BusinessException resourceAlreadyExists(String resourceName) {
        return new BusinessException(ErrorCodes.RESOURCE_ALREADY_EXISTS, "资源已存在: " + resourceName);
    }
    
    /**
     * 创建权限不足异常
     * 
     * @param operation 操作名称
     * @return 业务异常
     */
    public static BusinessException permissionDenied(String operation) {
        return new BusinessException(ErrorCodes.PERMISSION_DENIED, "权限不足，无法执行操作: " + operation);
    }
    
    /**
     * 创建操作超时异常
     * 
     * @param operation 操作名称
     * @return 业务异常
     */
    public static BusinessException timeoutError(String operation) {
        return new BusinessException(ErrorCodes.TIMEOUT_ERROR, "操作超时: " + operation);
    }
    
    /**
     * 创建配置错误异常
     * 
     * @param configName 配置名称
     * @param reason 错误原因
     * @return 业务异常
     */
    public static BusinessException configError(String configName, String reason) {
        return new BusinessException(ErrorCodes.CONFIG_ERROR, 
                String.format("配置错误: %s，原因: %s", configName, reason));
    }
    
    @Override
    public String toString() {
        return String.format("BusinessException{errorCode='%s', errorMessage='%s', errorDetails=%s}", 
                errorCode, errorMessage, errorDetails);
    }
}