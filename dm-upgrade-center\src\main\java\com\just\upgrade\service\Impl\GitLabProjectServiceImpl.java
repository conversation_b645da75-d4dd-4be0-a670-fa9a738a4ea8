package com.just.upgrade.service.Impl;

import com.just.git.model.GitLabProject;
import com.just.git.service.GitProjectService;
import com.just.upgrade.model.GitLabSearchRequest;
import com.just.upgrade.service.********************;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * GitLab项目服务实现
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@Service
public class ********************Impl implements ******************** {

    @Autowired
    private GitProjectService gitProjectService;

    @Override
    public List<GitLabProject> getAllProjects(GitLabSearchRequest request) {
        try {
            log.info("获取GitLab所有项目，关键词: {}", request.getKeyword());
            
            // 获取缓存的所有项目
            List<GitLabProject> allProjects = gitProjectService.getCachedProjects();
            
            if (!StringUtils.hasText(request.getKeyword())) {
                return allProjects;
            }
            
            // 根据关键词过滤
            return allProjects.stream()
                    .filter(project -> projectMatchesKeyword(project, request.getKeyword()))
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("获取GitLab项目失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<GitLabProject> searchProjects(GitLabSearchRequest request) {
        try {
            log.info("搜索GitLab项目，关键词: {}", request.getKeyword());
            
            if (!StringUtils.hasText(request.getKeyword())) {
                return this.getAllProjects(request);
            }
            // 使用GitProjectService的搜索功能
            List<GitLabProject> searchResults = gitProjectService.searchProjectsByName(request.getKeyword());
            return searchResults;
        } catch (Exception e) {
            log.error("搜索GitLab项目失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public GitLabProject getProjectDetail(String projectId) {
        try {
            log.info("获取GitLab项目详情，项目ID: {}", projectId);
            
            Optional<GitLabProject> projectOpt = gitProjectService.getProjectById(projectId);
            return projectOpt.orElse(null);
            
        } catch (Exception e) {
            log.error("获取GitLab项目详情失败，项目ID: {}", projectId, e);
            return null;
        }
    }

    @Override
    public List<org.gitlab4j.api.models.Branch> getProjectBranches(String projectId) {
        try {
            log.info("获取GitLab项目分支列表，项目ID: {}", projectId);
            
            List<org.gitlab4j.api.models.Branch> branches = gitProjectService.listBranches(projectId);
            
            log.info("成功获取项目 {} 的分支列表，共 {} 个分支", projectId, branches.size());
            return branches;
            
        } catch (Exception e) {
            log.error("获取GitLab项目分支列表失败，项目ID: {}", projectId, e);
            // 返回空列表而不是默认分支，让前端处理
            return Collections.emptyList();
        }
    }
    /**
     * 检查项目是否匹配关键词
     */
    private boolean projectMatchesKeyword(GitLabProject project, String keyword) {
        if (project == null || !StringUtils.hasText(keyword)) {
            return false;
        }
        
        String lowerKeyword = keyword.toLowerCase();
        
        return (project.getName() != null && project.getName().toLowerCase().contains(lowerKeyword)) ||
               (project.getPath() != null && project.getPath().toLowerCase().contains(lowerKeyword)) ||
               (project.getDescription() != null && project.getDescription().toLowerCase().contains(lowerKeyword)) ||
               (project.getPathWithNamespace() != null && project.getPathWithNamespace().toLowerCase().contains(lowerKeyword));
    }
} 