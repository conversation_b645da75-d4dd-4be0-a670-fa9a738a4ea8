import api from './request'

/**
 * Nacos升级相关API
 */
export const upgradeApi = {
  // 升级Nacos服务
  upgradeService: (data) => {
    return api.post('/upgrade/nacos/upgrade', data)
  },

  // 健康检查
  healthCheck: () => {
    return api.get('/upgrade/health')
  }
}

/**
 * Nacos配置相关API
 */
export const configApi = {
  // 获取单个配置详情
  getConfigDetail: (dataId, params) => {
    return api.get(`/upgrade/nacos/configs/${dataId}`, { params })
  },

  // 更新配置
  updateConfig: (dataId, data) => {
    return api.put(`/upgrade/nacos/configs/${dataId}`, data)
  },

  // 创建配置（复用updateConfig接口）
  createConfig: (data) => {
    return api.put(`/upgrade/nacos/configs/${data.dataId}`, data)
  },

  // 搜索配置
  searchConfigs: (data) => {
    return api.post('/upgrade/nacos/config/search', data)
  },

  // 获取所有配置
  getAllConfigs: (data) => {
    return api.post('/upgrade/nacos/config/list', data)
  }
}

/**
 * GitLab项目相关API
 */
export const gitlabApi = {
  // 搜索GitLab项目
  searchProjects: (data) => {
    return api.post('/v1/git/gitlab/projects/search', data)
  },

  // 获取项目详情
  getProjectDetail: (projectId) => {
    return api.get(`/v1/git/gitlab/projects/${projectId}`)
  },

  // 获取项目分支列表
  getBranches: (projectId) => {
    return api.get(`/v1/git/gitlab/projects/${projectId}/branches`)
  }
}