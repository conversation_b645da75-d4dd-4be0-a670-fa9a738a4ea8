package com.just.nacos.exception;

/**
 * Nacos环境操作异常
 *
 * <AUTHOR>
 * @since 2.0.0
 */
public class NacosEnvironmentException extends NacosException {

    public static final String ENVIRONMENT_NOT_FOUND = "NACOS_ENVIRONMENT_NOT_FOUND";
    public static final String ENVIRONMENT_ALREADY_EXISTS = "NACOS_ENVIRONMENT_ALREADY_EXISTS";
    public static final String ENVIRONMENT_NOT_AVAILABLE = "NACOS_ENVIRONMENT_NOT_AVAILABLE";
    public static final String ENVIRONMENT_CONFIG_INVALID = "NACOS_ENVIRONMENT_CONFIG_INVALID";
    public static final String ENVIRONMENT_CONNECTION_FAILED = "NACOS_ENVIRONMENT_CONNECTION_FAILED";

    public NacosEnvironmentException(String message) {
        super(message);
    }

    public NacosEnvironmentException(String message, Throwable cause) {
        super(message, cause);
    }

    public NacosEnvironmentException(String code, String message) {
        super(code, message);
    }

    public NacosEnvironmentException(String code, String message, Throwable cause) {
        super(code, message, cause);
    }

    /**
     * 环境不存在异常
     */
    public static NacosEnvironmentException environmentNotFound(String environmentName) {
        return new NacosEnvironmentException(ENVIRONMENT_NOT_FOUND, 
                String.format("环境不存在: %s", environmentName));
    }

    /**
     * 环境已存在异常
     */
    public static NacosEnvironmentException environmentAlreadyExists(String environmentName) {
        return new NacosEnvironmentException(ENVIRONMENT_ALREADY_EXISTS, 
                String.format("环境已存在: %s", environmentName));
    }

    /**
     * 环境不可用异常
     */
    public static NacosEnvironmentException environmentNotAvailable(String environmentName, String reason) {
        return new NacosEnvironmentException(ENVIRONMENT_NOT_AVAILABLE, 
                String.format("环境不可用[%s]: %s", environmentName, reason));
    }

    /**
     * 环境配置无效异常
     */
    public static NacosEnvironmentException configInvalid(String environmentName, String reason) {
        return new NacosEnvironmentException(ENVIRONMENT_CONFIG_INVALID, 
                String.format("环境配置无效[%s]: %s", environmentName, reason));
    }

    /**
     * 环境连接失败异常
     */
    public static NacosEnvironmentException connectionFailed(String environmentName, Throwable cause) {
        return new NacosEnvironmentException(ENVIRONMENT_CONNECTION_FAILED, 
                String.format("环境连接失败: %s", environmentName), cause);
    }
}