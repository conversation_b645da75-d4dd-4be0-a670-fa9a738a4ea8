package com.just.upgrade.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 升级状态
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
@Accessors(chain = true)
public class UpgradeStatus {

    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 升级ID（用于模版方法）
     */
    private String upgradeId;
    /**
     * 环境名称
     */
    private String environment;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 升级状态
     */
    private Status status;

    /**
     * 当前步骤
     */
    private String currentStep;

    /**
     * 总步骤数
     */
    private Integer totalSteps;

    /**
     * 当前步骤索引
     */
    private Integer currentStepIndex;

    /**
     * 进度百分比
     */
    private Integer progress;

    /**
     * 升级步骤列表
     */
    private List<UpgradeStep> steps = new ArrayList<>();

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 未完成替换的环境变量
     */
    private List<String> unreplacedVariables = new ArrayList<>();

    /**
     * 处理消息列表
     */
    private List<String> messages = new ArrayList<>();

    /**
     * 未处理文件列表
     */
    private List<String> unprocessedFiles = new ArrayList<>();

    /**
     * Nacos配置列表
     */
    private List<String> nacosConfigs = new ArrayList<>();
    
    /**
     * 上下文数据，用于在升级步骤间传递数据
     */
    private Map<String, Object> context = new HashMap<>();
    
    /**
     * 未解析的环境变量列表
     */
    private List<String> unresolvedEnvVars = new ArrayList<>();

    /**
     * 添加处理消息
     */
    public void addMessage(String message) {
        if (this.messages == null) {
            this.messages = new ArrayList<>();
        }
        this.messages.add(LocalDateTime.now() + ": " + message);
    }

    /**
     * 升级状态枚举
     */
    public enum Status {
        PENDING("待开始"),
        RUNNING("进行中"),
        SUCCESS("成功"),
        COMPLETED("完成"),
        FAILED("失败"),
        CANCELLED("已取消");

        private final String description;

        Status(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 升级步骤
     */
    @Data
    @Accessors(chain = true)
    public static class UpgradeStep {
        /**
         * 步骤索引
         */
        private Integer stepIndex;

        /**
         * 步骤名称
         */
        private String stepName;

        /**
         * 步骤描述
         */
        private String description;

        /**
         * 步骤状态
         */
        private Status status;

        /**
         * 步骤消息
         */
        private String message;

        /**
         * 开始时间
         */
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        private LocalDateTime endTime;
    }
} 