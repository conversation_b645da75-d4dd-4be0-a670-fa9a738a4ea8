package com.just.common.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.just.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.annotation.PostConstruct;

/**
 * 通用自动配置类
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(CommonProperties.class)
public class CommonAutoConfiguration {
    
    private final CommonProperties commonProperties;
    
    public CommonAutoConfiguration(CommonProperties commonProperties) {
        this.commonProperties = commonProperties;
    }
    
    @PostConstruct
    public void init() {
        log.info("DM Common Core 模块初始化完成");
        log.info("应用名称: {}", commonProperties.getAppName());
        log.info("应用版本: {}", commonProperties.getAppVersion());
        log.info("应用描述: {}", commonProperties.getAppDescription());
        log.info("默认字符编码: {}", commonProperties.getCharset());
        log.info("默认时区: {}", commonProperties.getTimezone());
    }
    
    /**
     * 配置ObjectMapper Bean
     * 
     * @return ObjectMapper实例
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = JsonUtils.getObjectMapper();
        log.info("注册ObjectMapper Bean");
        return mapper;
    }
}