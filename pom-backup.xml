<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.7.5</version>
    <relativePath/> <!-- lookup parent from repository -->
  </parent>
  
  <groupId>com.just</groupId>
  <artifactId>nacos-property</artifactId>
  <version>1.1</version>
  <packaging>jar</packaging>
  <name>nacos-property</name>
  <description>Nacos配置管理工具</description>

  <properties>
    <java.version>1.8</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <spring-boot.version>2.7.5</spring-boot.version>
    <spring-shell.version>2.1.9</spring-shell.version>
    <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
    <hutool.version>5.8.25</hutool.version>
    <poi.version>4.1.2</poi.version>
    <aliyun.mse.version>7.18.1</aliyun.mse.version>
  </properties>

  <dependencies>
    <!-- Spring Boot Starter (最小依赖) -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.yaml</groupId>
          <artifactId>snakeyaml</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- Spring Shell (最小依赖) -->
    <dependency>
      <groupId>org.springframework.shell</groupId>
      <artifactId>spring-shell-starter</artifactId>
      <version>${spring-shell.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- pom.xml中添加依赖 -->
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-model</artifactId>
      <version>3.8.6</version>
    </dependency>

    <!-- Validation -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>

    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>3.0.2</version>
      <optional>true</optional>
    </dependency>
    <!-- Nacos Client (最小依赖) -->
    <dependency>
      <groupId>com.alibaba.nacos</groupId>
      <artifactId>nacos-client</artifactId>
      <version>2.2.0</version>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.prometheus</groupId>
          <artifactId>simpleclient</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- Jackson 依赖 -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>

    <!-- HttpAsyncClient -->
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpasyncclient</artifactId>
      <version>4.1.5</version>
    </dependency>

    <!-- Excel处理 (最小依赖) -->
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi</artifactId>
      <version>${poi.version}</version>
      <exclusions>
        <exclusion>
          <groupId>commons-codec</groupId>
          <artifactId>commons-codec</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
      <version>${poi.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.github.virtuald</groupId>
          <artifactId>curvesapi</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- Hutool POI (最小依赖) -->
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-poi</artifactId>
      <version>${hutool.version}</version>
    </dependency>

    <!-- Apache Commons -->
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
    </dependency>

    <!-- 日志框架 (最小依赖) -->
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <exclusions>
        <exclusion>
          <groupId>com.sun.mail</groupId>
          <artifactId>javax.mail</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- 工具类 -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>
    
    <!-- 阿里云MSE SDK (最小依赖) -->
    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>mse20190531</artifactId>
      <version>${aliyun.mse.version}</version>
      <exclusions>
        <exclusion>
          <groupId>io.opentracing</groupId>
          <artifactId>opentracing-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opentracing</groupId>
          <artifactId>opentracing-util</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- Test -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <!-- GitLab API -->
    <dependency>
      <groupId>org.gitlab4j</groupId>
      <artifactId>gitlab4j-api</artifactId>
      <version>4.19.0</version>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.konghq</groupId>
      <artifactId>unirest-java</artifactId>
      <version>3.13.10</version>
    </dependency>


    <dependency>
      <groupId>org.eclipse.jgit</groupId>
      <artifactId>org.eclipse.jgit</artifactId>
      <version>5.13.0.202109080827-r</version>
    </dependency>

  </dependencies>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring-boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <repositories>
    <repository>
      <id>nexus</id>
      <name>Team Nexus Repository</name>
      <url>http://nexus.innodealing.com/nexus/content/repositories/thirdparty/</url>
    </repository>
  </repositories>
  
  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>${spring-boot.version}</version>
        <configuration>
          <excludes>
            <exclude>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
            </exclude>
          </excludes>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <!-- 依赖分析插件 -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <executions>
          <execution>
            <id>analyze-duplicate</id>
            <goals>
              <goal>analyze-duplicate</goal>
            </goals>
            <phase>verify</phase>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
