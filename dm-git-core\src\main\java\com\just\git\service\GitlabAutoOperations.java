package com.just.git.service;

import com.just.git.enums.EnvPathEnum;
import org.apache.commons.lang3.tuple.Pair;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.gitlab4j.api.GitLabApiException;
import org.gitlab4j.api.models.MergeRequest;

import javax.annotation.Nullable;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.Map;

public interface GitlabAutoOperations {

    Git gitClone() throws GitLabApiException, GitAPIException, IOException;

    void set(Git git);

    Pair<Boolean, String> checkRunning(Path path, @Nullable EnvPathEnum env) throws Exception;

    Git switchBranch(String branchName) throws Exception;

    MergeRequest commitAndPush(String title, String commitMessage) throws Exception;

    int getProjectId(String serviceName) throws GitLabApiException;

    int getProjectId() throws GitLabApiException;

    // 检查目录是否是 Git 仓库
    boolean isGitRepository(File dir);

    boolean isGitRepository();

    Map<String, String> getRemoteEvnMap(EnvPathEnum envPathEnum) throws Exception;

    Map<String, String> getDefaultEvnMap() throws IOException;

    void createMergeRequestNote(Integer mergeRequestIid, String text) throws GitLabApiException;

}
