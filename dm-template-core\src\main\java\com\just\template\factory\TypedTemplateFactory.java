package com.just.template.factory;

import com.just.template.core.*;
import com.just.template.engine.FreeMarkerTemplateEngine;
import com.just.template.exception.TemplateException;
import com.just.template.model.ApplicationNacosConfigModel;
import com.just.template.model.BootstrapConfigModel;
import com.just.template.model.PomConfigModel;

/**
 * 类型安全的模板工厂
 * 支持强类型的配置模型，提供更好的开发体验
 *
 * <AUTHOR>
 * @since 2.0.0
 */
public class TypedTemplateFactory {

    /**
     * 模板引擎
     */
    private final TemplateEngine templateEngine;

    public TypedTemplateFactory() {
        this(new FreeMarkerTemplateEngine());
    }

    public TypedTemplateFactory(TemplateEngine templateEngine) {
        this.templateEngine = templateEngine;
    }

    /**
     * 生成Bootstrap配置
     *
     * @param config Bootstrap配置模型
     * @return 生成的配置内容
     * @throws TemplateException 模板处理异常
     */
    public String generateBootstrap(BootstrapConfigModel config) throws TemplateException {
        if (config == null) {
            throw new IllegalArgumentException("BootstrapConfigModel cannot be null");
        }

        TypedTemplateContext context = TypedTemplateContext.from(config);
        return templateEngine.process("bootstrap/bootstrap-simple.ftl", context);
    }

    /**
     * 生成Application Nacos配置
     *
     * @param config Application Nacos配置模型
     * @return 生成的配置内容
     * @throws TemplateException 模板处理异常
     */
    public String generateApplicationNacos(ApplicationNacosConfigModel config) throws TemplateException {
        if (config == null) {
            throw new IllegalArgumentException("ApplicationNacosConfigModel cannot be null");
        }

        TypedTemplateContext context = TypedTemplateContext.from(config);
        return templateEngine.process("application/application.ftl", context);
    }

    /**
     * 生成pom配置
     *
     * @param config Application Nacos配置模型
     * @return 生成的配置内容
     * @throws TemplateException 模板处理异常
     */
    public String generatePomParentDependency(PomConfigModel config) throws TemplateException {
        if (config == null) {
            throw new IllegalArgumentException("PomConfigModel cannot be null");
        }

        TypedTemplateContext context = TypedTemplateContext.from(config);
        return templateEngine.process("pom/pom-parent-replacement.ftl", context);
    }

    /**
     * 生成pom Nacos配置
     *
     * @param config Application Nacos配置模型
     * @return 生成的配置内容
     * @throws TemplateException 模板处理异常
     */
    public String generateNacosPomDependency(PomConfigModel config) throws TemplateException {
        if (config == null) {
            throw new IllegalArgumentException("PomConfigModel cannot be null");
        }

        TypedTemplateContext context = TypedTemplateContext.from(config);
        return templateEngine.process("pom/pom-nacos-dependencies.ftl", context);
    }


    /**
     * 根据模板类型和配置模型生成内容
     *
     * @param templateType 模板类型
     * @param configModel 配置模型
     * @return 生成的内容
     * @throws TemplateException 模板处理异常
     */
    public String generate(TemplateType templateType, Object configModel) throws TemplateException {
        if (templateType == null) {
            throw new IllegalArgumentException("TemplateType cannot be null");
        }
        if (configModel == null) {
            throw new IllegalArgumentException("Config model cannot be null");
        }

        switch (templateType) {
            case BOOTSTRAP:
                if (!(configModel instanceof BootstrapConfigModel)) {
                    throw new IllegalArgumentException("BOOTSTRAP template requires BootstrapConfigModel, got: "
                            + configModel.getClass().getSimpleName());
                }
                return generateBootstrap((BootstrapConfigModel) configModel);

            case APPLICATION_NACOS:
                if (!(configModel instanceof ApplicationNacosConfigModel)) {
                    throw new IllegalArgumentException("APPLICATION_NACOS template requires ApplicationNacosConfigModel, got: "
                            + configModel.getClass().getSimpleName());
                }
                return generateApplicationNacos((ApplicationNacosConfigModel) configModel);
            default:
                throw new IllegalArgumentException("Unsupported template type: " + templateType);
        }
    }

    /**
     * 验证配置模型
     *
     * @param templateType 模板类型
     * @param configModel 配置模型
     * @throws IllegalArgumentException 如果配置无效
     */
    public void validateConfig(TemplateType templateType, Object configModel) {
        if (templateType == null) {
            throw new IllegalArgumentException("TemplateType cannot be null");
        }
        if (configModel == null) {
            throw new IllegalArgumentException("Config model cannot be null");
        }

        switch (templateType) {
            case BOOTSTRAP:
                if (!(configModel instanceof BootstrapConfigModel)) {
                    throw new IllegalArgumentException("BOOTSTRAP template requires BootstrapConfigModel");
                }
                ((BootstrapConfigModel) configModel).validate();
                break;

            case APPLICATION_NACOS:
                if (!(configModel instanceof ApplicationNacosConfigModel)) {
                    throw new IllegalArgumentException("APPLICATION_NACOS template requires ApplicationNacosConfigModel");
                }
                ((ApplicationNacosConfigModel) configModel).validate();
                break;
            case POM:
                if (!(configModel instanceof PomConfigModel)) {
                    throw new IllegalArgumentException("APPLICATION_NACOS template requires ApplicationNacosConfigModel");
                }
                ((PomConfigModel) configModel).validate();
                break;
            default:
                throw new IllegalArgumentException("Unsupported template type: " + templateType);
        }
    }

    /**
     * 检查模板是否存在
     *
     * @param templateType 模板类型
     * @return 是否存在
     */
    public boolean templateExists(TemplateType templateType) {
        switch (templateType) {
            case BOOTSTRAP:
                return templateEngine.templateExists("bootstrap/bootstrap-simple.ftl");
            case APPLICATION_NACOS:
                return templateEngine.templateExists("application/application.ftl");
            case POM:
                return templateEngine.templateExists("pom-nacos-dependencies.ftl") &&
                        templateEngine.templateExists("pom-parent-replacement.ftl");
            default:
                return false;
        }
    }

    /**
     * 获取模板引擎
     */
    public TemplateEngine getTemplateEngine() {
        return templateEngine;
    }
} 