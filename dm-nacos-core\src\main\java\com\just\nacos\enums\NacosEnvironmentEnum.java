package com.just.nacos.enums;

/**
 * nacos 环境枚举
 * <AUTHOR>
 */

public enum NacosEnvironmentEnum {

    DEV("开发环境", "dev"),
    QA("测试环境", "qa"),
    UAT("预生产环境", "uat"),
    PRODUCTION("生产环境", "prod");

    private final String description;
    private final String env;

    NacosEnvironmentEnum(String description, String env) {
        this.description = description;
        this.env = env;
    }


    public String getDescription() {
        return description;
    }

    public String getEnv() {
        return env;
    }

    public static NacosEnvironmentEnum getByEnv(String env) {
        for (NacosEnvironmentEnum value : NacosEnvironmentEnum.values()) {
            if (value.getEnv().equals(env)) {
                return value;
            }
        }
        return null;
    }
}
