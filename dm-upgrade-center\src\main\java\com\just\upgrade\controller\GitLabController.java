package com.just.upgrade.controller;

import com.just.git.model.GitLabProject;
import com.just.upgrade.model.ApiResponse;
import com.just.upgrade.model.GitLabSearchRequest;
import com.just.upgrade.service.GitLabProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 升级中心控制器
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/git")
@Validated
public class GitLabController {

    @Autowired
    private GitLabProjectService ********************;

    /**
     * 搜索GitLab项目
     *
     * @param request 搜索请求
     * @return 项目列表
     */
    @PostMapping("/gitlab/projects/search")
    public ApiResponse<List<GitLabProject>> searchGitLabProjects(@RequestBody GitLabSearchRequest request) {
        try {
            log.info("搜索GitLab项目，关键词: {}", request.getKeyword());

            List<GitLabProject> projects = ********************.searchProjects(request);

            return ApiResponse.success(projects, 
                String.format("搜索成功，共%d个项目", projects.size()));

        } catch (Exception e) {
            log.error("搜索GitLab项目失败", e);
            return ApiResponse.error("搜索GitLab项目失败: " + e.getMessage());
        }
    }

    /**
     * 获取GitLab项目详情
     *
     * @param projectId 项目ID
     * @return 项目详情
     */
    @GetMapping("/gitlab/projects/{projectId}")
    public ApiResponse<GitLabProject> getGitLabProjectDetail(@PathVariable String projectId) {
        try {
            log.info("获取GitLab项目详情，项目ID: {}", projectId);

            GitLabProject project = ********************.getProjectDetail(projectId);

            if (project != null) {
                return ApiResponse.success(project, "项目详情获取成功");
            } else {
                return ApiResponse.notFound("项目不存在");
            }

        } catch (Exception e) {
            log.error("获取GitLab项目详情失败，项目ID: " + projectId, e);
            return ApiResponse.error("获取GitLab项目详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取GitLab项目分支列表
     *
     * @param projectId 项目ID
     * @return 分支列表
     */
    @GetMapping("/gitlab/projects/{projectId}/branches")
    public ApiResponse<List<org.gitlab4j.api.models.Branch>> getGitLabProjectBranches(@PathVariable String projectId) {
        try {
            log.info("获取GitLab项目分支列表，项目ID: {}", projectId);

            List<org.gitlab4j.api.models.Branch> branches = ********************.getProjectBranches(projectId);

            return ApiResponse.success(branches, 
                String.format("分支列表获取成功，共%d个分支", branches.size()));

        } catch (Exception e) {
            log.error("获取GitLab项目分支列表失败，项目ID: " + projectId, e);
            return ApiResponse.error("获取GitLab项目分支列表失败: " + e.getMessage());
        }
    }


}