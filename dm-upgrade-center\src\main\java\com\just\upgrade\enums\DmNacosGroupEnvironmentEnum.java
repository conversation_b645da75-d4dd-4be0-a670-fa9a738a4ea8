package com.just.upgrade.enums;

import java.util.Optional;

/**
 * Nacos组环境变量枚举
 * 管理不同业务组的Nacos访问密钥和秘钥环境变量
 * 
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
public enum DmNacosGroupEnvironmentEnum {
    
    /**
     * 价格组环境变量
     */
    PRICE("价格组", "NACOS_PRICE_ACCESS_KEY", "NACOS_PRICE_SECRET_KEY"),
    
    /**
     * 综合组环境变量
     */
    GENERAL("综合组", "NACOS_GENERAL_ACCESS_KEY", "NACOS_GENERAL_SECRET_KEY"),
    
    /**
     * 数据组环境变量
     */
    DATA("数据组", "NACOS_DATA_ACCESS_KEY", "NACOS_DATA_SECRET_KEY"),
    
    /**
     * 国际落地组环境变量
     */
    INTL("国际落地组", "NACOS_INTL_ACCESS_KEY", "NACOS_INTL_SECRET_KEY"),
    
    /**
     * 架构组环境变量
     */
    TA("架构组", "NACOS_TA_ACCESS_KEY", "NACOS_TA_SECRET_KEY");
    
    /**
     * 组名称
     */
    private final String groupName;
    
    /**
     * 访问密钥环境变量名
     */
    private final String accessKeyEnvVar;
    
    /**
     * 秘钥环境变量名
     */
    private final String secretKeyEnvVar;
    
    /**
     * 构造函数
     * 
     * @param groupName 组名称
     * @param accessKeyEnvVar 访问密钥环境变量名
     * @param secretKeyEnvVar 秘钥环境变量名
     */
    DmNacosGroupEnvironmentEnum(String groupName, String accessKeyEnvVar, String secretKeyEnvVar) {
        this.groupName = groupName;
        this.accessKeyEnvVar = accessKeyEnvVar;
        this.secretKeyEnvVar = secretKeyEnvVar;
    }
    
    /**
     * 获取组名称
     * 
     * @return 组名称
     */
    public String getGroupName() {
        return groupName;
    }
    
    /**
     * 获取访问密钥环境变量名
     * 
     * @return 访问密钥环境变量名
     */
    public String getAccessKeyEnvVar() {
        return accessKeyEnvVar;
    }
    
    /**
     * 获取秘钥环境变量名
     * 
     * @return 秘钥环境变量名
     */
    public String getSecretKeyEnvVar() {
        return secretKeyEnvVar;
    }
    
    /**
     * 根据组名称查找枚举
     * 
     * @param groupName 组名称
     * @return 对应的枚举值，如果未找到返回null
     */
    public static Optional<DmNacosGroupEnvironmentEnum> getByGroupName(String groupName) {
        if (groupName == null) {
            return Optional.empty();
        }
        
        for (DmNacosGroupEnvironmentEnum group : values()) {
            if (group.getGroupName().equals(groupName)) {
                return Optional.of(group);
            }
        }
        return Optional.empty();
    }
    
    /**
     * 根据访问密钥环境变量名查找枚举
     * 
     * @param accessKeyEnvVar 访问密钥环境变量名
     * @return 对应的枚举值，如果未找到返回null
     */
    public static DmNacosGroupEnvironmentEnum getByAccessKeyEnvVar(String accessKeyEnvVar) {
        if (accessKeyEnvVar == null) {
            return null;
        }
        
        for (DmNacosGroupEnvironmentEnum group : values()) {
            if (group.getAccessKeyEnvVar().equals(accessKeyEnvVar)) {
                return group;
            }
        }
        return null;
    }
    
    /**
     * 根据秘钥环境变量名查找枚举
     * 
     * @param secretKeyEnvVar 秘钥环境变量名
     * @return 对应的枚举值，如果未找到返回null
     */
    public static DmNacosGroupEnvironmentEnum getBySecretKeyEnvVar(String secretKeyEnvVar) {
        if (secretKeyEnvVar == null) {
            return null;
        }
        
        for (DmNacosGroupEnvironmentEnum group : values()) {
            if (group.getSecretKeyEnvVar().equals(secretKeyEnvVar)) {
                return group;
            }
        }
        return null;
    }
    
    /**
     * 检查给定的环境变量名是否属于某个组
     * 
     * @param envVarName 环境变量名
     * @return 如果是Nacos组环境变量返回对应的枚举值，否则返回null
     */
    public static DmNacosGroupEnvironmentEnum getByAnyEnvVar(String envVarName) {
        if (envVarName == null) {
            return null;
        }
        
        for (DmNacosGroupEnvironmentEnum group : values()) {
            if (group.getAccessKeyEnvVar().equals(envVarName) || 
                group.getSecretKeyEnvVar().equals(envVarName)) {
                return group;
            }
        }
        return null;
    }
    
    /**
     * 获取所有组的名称列表
     * 
     * @return 组名称列表
     */
    public static String[] getAllGroupNames() {
        DmNacosGroupEnvironmentEnum[] values = values();
        String[] groupNames = new String[values.length];
        
        for (int i = 0; i < values.length; i++) {
            groupNames[i] = values[i].getGroupName();
        }
        
        return groupNames;
    }
    
    /**
     * 获取格式化的环境变量信息
     * 
     * @return 格式化的字符串
     */
    public String getFormattedInfo() {
        return String.format("%s - AccessKey: %s, SecretKey: %s", 
                groupName, accessKeyEnvVar, secretKeyEnvVar);
    }
    
    @Override
    public String toString() {
        return getFormattedInfo();
    }
} 