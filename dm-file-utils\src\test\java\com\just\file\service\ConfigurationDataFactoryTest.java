package com.just.file.service;

import com.just.file.model.ConfigurationDataCenter;
import com.just.file.model.ConfigurationMetadata;
import com.just.file.model.ConfigurationType;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 配置数据工厂测试
 * 验证重构后的架构功能
 * 
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
@Slf4j
@SpringBootTest
public class ConfigurationDataFactoryTest {
    
    @Autowired
    private ConfigurationDataFactory configurationDataFactory;
    
    @Autowired
    private ExcelConfigService excelConfigService;
    
    @Test
    public void testFactoryInitialization() {
        // 验证工厂能够正常初始化
        assertNotNull(configurationDataFactory, "配置数据工厂应该被正确注入");
        
        // 获取统计信息
        Map<String, Object> statistics = configurationDataFactory.getStatistics();
        assertNotNull(statistics, "统计信息不应为空");
        
        log.info("工厂初始化成功，统计信息: {}", statistics);
    }
    
    @Test
    public void testGetConfigurationDataCenter() {
        // 测试获取完整的配置数据中心对象
        ConfigurationDataCenter nacosDataCenter = 
            configurationDataFactory.getConfigurationDataCenter(ConfigurationType.NACOS);
        
        if (nacosDataCenter != null) {
            log.info("成功获取Nacos配置数据中心");
            
            // 验证元数据
            Map<ConfigurationType, ConfigurationMetadata> metadataMap = nacosDataCenter.getMetadataMap();
            if (metadataMap != null && metadataMap.containsKey(ConfigurationType.NACOS)) {
                ConfigurationMetadata metadata = metadataMap.get(ConfigurationType.NACOS);
                assertNotNull(metadata, "元数据不应为空");
                assertTrue(metadata.getTotalRows() >= 0, "总行数应该大于等于0");
                assertTrue(metadata.getValidRows() >= 0, "有效行数应该大于等于0");
                assertNotNull(metadata.getProcessorInfo(), "处理器信息不应为空");
                assertNotNull(metadata.getProcessingDuration(), "处理耗时不应为空");
                assertFalse(metadata.isCacheHit(), "初始加载应该不是缓存命中");
                
                log.info("Nacos元数据验证通过: 总行数={}, 有效行数={}, 处理器={}, 处理耗时={}ms, 验证错误数={}", 
                    metadata.getTotalRows(), metadata.getValidRows(), metadata.getProcessorInfo(),
                    metadata.getProcessingDuration().toMillis(),
                    metadata.getValidationErrors() != null ? metadata.getValidationErrors().size() : 0);
                    
                // 验证验证错误信息
                if (metadata.getValidationErrors() != null && !metadata.getValidationErrors().isEmpty()) {
                    log.info("发现 {} 个验证错误:", metadata.getValidationErrors().size());
                    metadata.getValidationErrors().stream()
                        .limit(3) // 只显示前3个错误
                        .forEach(error -> log.info("  错误: {} - {} (行:{}, 列:{})", 
                            error.getErrorCode(), error.getMessage(), 
                            error.getRowIndex(), error.getColumnIndex()));
                }
            }
        } else {
            log.warn("未找到Nacos配置数据中心，可能是配置文件不存在");
        }
    }
    
    @Test
    public void testPerformanceImprovement() {
        // 测试性能改进：验证文件只读取一次
        long startTime = System.currentTimeMillis();
        
        // 获取配置数据中心（一次性读取文件并构建配置和元数据）
        ConfigurationDataCenter nacosDataCenter = 
            configurationDataFactory.getConfigurationDataCenter(ConfigurationType.NACOS);
            
        long endTime = System.currentTimeMillis();
        
        if (nacosDataCenter != null) {
            Map<ConfigurationType, ConfigurationMetadata> metadataMap = nacosDataCenter.getMetadataMap();
            if (metadataMap != null && metadataMap.containsKey(ConfigurationType.NACOS)) {
                ConfigurationMetadata metadata = metadataMap.get(ConfigurationType.NACOS);
                
                // 工厂总耗时应该与处理器耗时接近（说明只读取了一次文件）
                long factoryTime = endTime - startTime;
                long processorTime = metadata.getProcessingDuration().toMillis();
                
                log.info("性能测试结果: 工厂总耗时={}ms, 处理器耗时={}ms", factoryTime, processorTime);
                
                // 由于只读取一次文件，工厂耗时应该不会明显超过处理器耗时的2倍
                assertTrue(factoryTime < processorTime * 2, 
                    "工厂总耗时应该接近处理器耗时，说明文件只读取了一次");
            }
        }
    }
    
    @Test
    public void testGetConfigurations() {
        // 测试获取配置列表
        List<ConfigurationDataCenter.NacosProperty> nacosConfigs = 
            configurationDataFactory.getAllNacosConfigurations();
        assertNotNull(nacosConfigs, "Nacos配置列表不应为空");
        
        log.info("获取到 {} 个Nacos配置项", nacosConfigs.size());
        
        if (!nacosConfigs.isEmpty()) {
            ConfigurationDataCenter.NacosProperty firstConfig = nacosConfigs.get(0);
            assertNotNull(firstConfig.getType(), "配置类型不应为空");
            assertEquals(ConfigurationType.NACOS, firstConfig.getType(), "配置类型应该是NACOS");
            
            log.info("第一个配置项: K8s环境变量={}, Nacos环境变量={}", 
                firstConfig.getK8sEnv(), firstConfig.getNacosEnv());
        }
    }
    
    @Test
    public void testGetConfigurationsByEnvironment() {
        // 测试按环境获取配置
        List<ConfigurationDataCenter.NacosProperty> devUrlMappings = 
            configurationDataFactory.getUrlMappings("dev");
        assertNotNull(devUrlMappings, "DEV环境URL映射不应为空");
        
        log.info("DEV环境URL映射数量: {}", devUrlMappings.size());
        
        // 验证所有配置都是dev环境的
        for (ConfigurationDataCenter.NacosProperty config : devUrlMappings) {
            assertEquals("dev", config.getEnvironment(), "所有配置都应该是dev环境的");
            assertEquals(ConfigurationType.URL_MAPPING, config.getType(), "配置类型应该是URL_MAPPING");
        }
    }
    
    @Test
    public void testGetSupportedEnvironments() {
    }
    
    @Test
    public void testBackwardCompatibility() {
        // 测试向后兼容性 - 原有方法应该继续工作
        List<ConfigurationDataCenter.NacosProperty> nacosConfigs = 
            excelConfigService.readAllNacosConfigs();
        assertNotNull(nacosConfigs, "向后兼容的Nacos配置读取应该正常工作");
        
        log.info("通过向后兼容方法获取到 {} 个Nacos配置项", nacosConfigs.size());
        
        // 测试URL映射读取
        List<ConfigurationDataCenter.NacosProperty> devUrlMappings = 
            excelConfigService.readUrlMappings("dev");
        assertNotNull(devUrlMappings, "向后兼容的URL映射读取应该正常工作");
        
        log.info("通过向后兼容方法获取到 {} 个DEV环境URL映射", devUrlMappings.size());
    }
    
    @Test
    public void testNewMethods() {
        // 测试新增的方法
        List<ConfigurationDataCenter.NacosProperty> allUrlMappings = 
            excelConfigService.readAllUrlMappings();
        assertNotNull(allUrlMappings, "新增的读取所有URL映射方法应该正常工作");
        
        log.info("通过新方法获取到 {} 个URL映射", allUrlMappings.size());
        
        // 测试通用配置读取方法
        List<ConfigurationDataCenter.NacosProperty> nacosConfigsByType = 
            excelConfigService.readConfigurations(ConfigurationType.NACOS);
        assertNotNull(nacosConfigsByType, "通用配置读取方法应该正常工作");
        
        log.info("通过通用方法获取到 {} 个Nacos配置", nacosConfigsByType.size());
        
        // 测试带环境参数的通用方法
        List<ConfigurationDataCenter.NacosProperty> devConfigsByType = 
            excelConfigService.readConfigurations(ConfigurationType.URL_MAPPING, "dev");
        assertNotNull(devConfigsByType, "带环境参数的通用配置读取方法应该正常工作");
        
        log.info("通过通用方法获取到 {} 个DEV环境配置", devConfigsByType.size());
    }
} 