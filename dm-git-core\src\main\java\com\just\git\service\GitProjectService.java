package com.just.git.service;

import com.just.git.model.GitCredentials;
import com.just.git.model.GitLabProject;
import org.gitlab4j.api.GitLabApiException;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * Git项目服务接口
 * 支持通过GitLab API和JGit获取所有项目信息
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
public interface GitProjectService {
    
    /**
     * 加载所有可访问的Git项目信息
     * 结合GitLab API和JGit方式获取完整信息
     * 
     * @return 异步加载结果
     */
    CompletableFuture<List<GitLabProject>> loadAllProjects();
    
    /**
     * 通过GitLab API获取所有项目基础信息
     * 
     * @return 项目列表
     */
    List<GitLabProject> ********************();
    
    /**
     * 根据项目名称搜索项目
     * 
     * @param projectName 项目名称（支持模糊匹配）
     * @return 匹配的项目列表
     */
    List<GitLabProject> searchProjectsByName(String projectName);
    
    /**
     * 根据项目名称搜索项目（带认证）
     * 
     * @param projectName 项目名称
     * @param credentials 认证信息
     * @return 匹配的项目列表
     */
    List<GitLabProject> searchProjectsByName(String projectName, GitCredentials credentials);
    
    /**
     * 获取特定项目的详细信息
     * 
     * @param projectId 项目ID或路径
     * @return 项目详细信息
     */
    Optional<GitLabProject> getProjectById(String projectId);
    
    /**
     * 获取特定项目的详细信息（带认证）
     * 
     * @param projectId 项目ID或路径
     * @param credentials 认证信息
     * @return 项目详细信息
     */
    Optional<GitLabProject> getProjectById(String projectId, GitCredentials credentials);
    
    /**
     * 验证Git仓库连接性
     * 
     * @param gitUrl Git仓库地址
     * @param credentials 认证信息
     * @return 验证结果
     */
    boolean validateGitRepository(String gitUrl, GitCredentials credentials);
    

    /**
     * 刷新项目缓存
     * 
     * @return 刷新是否成功
     */
    boolean refreshCache();
    
    /**
     * 获取缓存的项目数量
     * 
     * @return 缓存项目数量
     */
    int getCachedProjectCount();
    
    /**
     * 获取所有已缓存的项目
     * 
     * @return 缓存的项目列表
     */
    List<GitLabProject> getCachedProjects();
    
    /**
     * 根据命名空间获取项目
     * 
     * @param namespace 命名空间
     * @param credentials 认证信息
     * @return 项目列表
     */
    List<GitLabProject> getProjectsByNamespace(String namespace, GitCredentials credentials);
    

    /**
     * 获取项目最近的提交信息
     * 
     * @param projectId 项目ID
     * @param branch 分支名称
     * @param credentials 认证信息
     * @return 最近提交信息
     */
    Optional<String> getLatestCommit(String projectId, String branch, GitCredentials credentials);

    List<org.gitlab4j.api.models.Branch> listBranches(String projectId) throws GitLabApiException;
}