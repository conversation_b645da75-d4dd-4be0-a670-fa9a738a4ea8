package com.just.file.service;

import com.just.file.dto.FileOperationResult;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * FileOperationService测试类
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
public class FileOperationServiceTest {
    
    private FileOperationService fileOperationService;
    private String tempDir;
    private String testFile;
    private String testContent;
    
    @Before
    public void setUp() throws Exception {
        fileOperationService = new FileOperationService();
        
        // 创建临时目录
        tempDir = fileOperationService.createTempDirectory("file-test");
        testFile = tempDir + "/test.txt";
        testContent = "Hello, World!\\nThis is a test file.\\n测试中文内容";
        
        // 确保测试目录存在
        Files.createDirectories(Paths.get(tempDir));
    }
    
    @After
    public void tearDown() throws Exception {
        // 清理测试文件和目录
        try {
            if (fileOperationService.isDirectory(tempDir)) {
                fileOperationService.deleteDirectory(tempDir);
            }
        } catch (Exception e) {
            // 忽略清理错误
        }
    }
    
    @Test
    public void testWriteAndReadFileContent() {
        // 测试写入文件
        FileOperationResult writeResult = fileOperationService.writeFileContent(testFile, testContent);
        assertTrue("写入文件应该成功", writeResult.isSuccess());
        assertEquals("操作类型应该是写入文件", "写入文件", writeResult.getOperationType());
        assertTrue("处理文件数应该大于0", writeResult.getProcessedCount() > 0);
        
        // 测试读取文件
        String readContent = fileOperationService.readFileContent(testFile);
        assertEquals("读取的内容应该与写入的内容一致", testContent, readContent);
        
        // 测试文件存在性
        assertTrue("文件应该存在", fileOperationService.fileExists(testFile));
        assertTrue("应该是文件", fileOperationService.isFile(testFile));
    }
    
    @Test
    public void testWriteAndReadFileLines() {
        List<String> lines = Arrays.asList(
                "第一行",
                "第二行",
                "Third line",
                "最后一行"
        );
        
        // 测试写入行
        FileOperationResult writeResult = fileOperationService.writeFileLines(testFile, lines);
        assertTrue("写入行应该成功", writeResult.isSuccess());
        
        // 测试读取行
        List<String> readLines = fileOperationService.readFileLines(testFile);
        assertEquals("行数应该一致", lines.size(), readLines.size());
        for (int i = 0; i < lines.size(); i++) {
            assertEquals("第" + (i + 1) + "行内容应该一致", lines.get(i), readLines.get(i));
        }
    }
    
    @Test
    public void testAppendFile() {
        String initialContent = "初始内容";
        String appendContent = "\\n追加内容";
        
        // 写入初始内容
        FileOperationResult writeResult = fileOperationService.writeFileContent(testFile, initialContent);
        assertTrue("写入初始内容应该成功", writeResult.isSuccess());
        
        // 追加内容
        FileOperationResult appendResult = fileOperationService.writeFileContent(testFile, appendContent, true);
        assertTrue("追加内容应该成功", appendResult.isSuccess());
        
        // 验证结果
        String finalContent = fileOperationService.readFileContent(testFile);
        assertEquals("最终内容应该是初始内容加追加内容", initialContent + appendContent, finalContent);
    }
    
    @Test
    public void testCopyFile() {
        String sourceFile = testFile;
        String targetFile = tempDir + "/copy.txt";
        
        // 先创建源文件
        fileOperationService.writeFileContent(sourceFile, testContent);
        
        // 测试复制文件
        FileOperationResult copyResult = fileOperationService.copyFile(sourceFile, targetFile);
        assertTrue("复制文件应该成功", copyResult.isSuccess());
        assertEquals("操作类型应该是复制文件", "复制文件", copyResult.getOperationType());
        
        // 验证文件存在且内容一致
        assertTrue("目标文件应该存在", fileOperationService.fileExists(targetFile));
        String targetContent = fileOperationService.readFileContent(targetFile);
        assertEquals("复制后的内容应该一致", testContent, targetContent);
        
        // 验证源文件仍然存在
        assertTrue("源文件应该仍然存在", fileOperationService.fileExists(sourceFile));
    }
    
    @Test
    public void testMoveFile() {
        String sourceFile = testFile;
        String targetFile = tempDir + "/moved.txt";
        
        // 先创建源文件
        fileOperationService.writeFileContent(sourceFile, testContent);
        
        // 测试移动文件
        FileOperationResult moveResult = fileOperationService.moveFile(sourceFile, targetFile);
        assertTrue("移动文件应该成功", moveResult.isSuccess());
        
        // 验证文件被移动
        assertFalse("源文件应该不存在", fileOperationService.fileExists(sourceFile));
        assertTrue("目标文件应该存在", fileOperationService.fileExists(targetFile));
        String targetContent = fileOperationService.readFileContent(targetFile);
        assertEquals("移动后的内容应该一致", testContent, targetContent);
    }
    
    @Test
    public void testDeleteFile() {
        // 先创建文件
        fileOperationService.writeFileContent(testFile, testContent);
        assertTrue("文件应该存在", fileOperationService.fileExists(testFile));
        
        // 测试删除文件
        FileOperationResult deleteResult = fileOperationService.deleteFile(testFile);
        assertTrue("删除文件应该成功", deleteResult.isSuccess());
        
        // 验证文件被删除
        assertFalse("文件应该不存在", fileOperationService.fileExists(testFile));
    }
    
    @Test
    public void testBackupFile() {
        // 先创建文件
        fileOperationService.writeFileContent(testFile, testContent);
        
        // 测试备份文件
        FileOperationResult backupResult = fileOperationService.backupFile(testFile);
        assertTrue("备份文件应该成功", backupResult.isSuccess());
        
        // 验证备份文件存在
        String backupPath = (String) backupResult.getData();
        assertNotNull("备份路径不应该为空", backupPath);
        assertTrue("备份文件应该存在", fileOperationService.fileExists(backupPath));
        
        // 验证备份内容一致
        String backupContent = fileOperationService.readFileContent(backupPath);
        assertEquals("备份内容应该一致", testContent, backupContent);
        
        // 验证原文件仍然存在
        assertTrue("原文件应该仍然存在", fileOperationService.fileExists(testFile));
    }
    
    @Test
    public void testCreateAndDeleteDirectory() {
        String subDir = tempDir + "/subdir";
        
        // 测试创建目录
        FileOperationResult createResult = fileOperationService.createDirectory(subDir);
        assertTrue("创建目录应该成功", createResult.isSuccess());
        assertTrue("目录应该存在", fileOperationService.isDirectory(subDir));
        
        // 测试删除目录
        FileOperationResult deleteResult = fileOperationService.deleteDirectory(subDir);
        assertTrue("删除目录应该成功", deleteResult.isSuccess());
        assertFalse("目录应该不存在", fileOperationService.fileExists(subDir));
    }
    
    @Test
    public void testListFiles() {
        // 创建一些测试文件
        String file1 = tempDir + "/file1.txt";
        String file2 = tempDir + "/file2.log";
        String file3 = tempDir + "/file3.txt";
        
        fileOperationService.writeFileContent(file1, "content1");
        fileOperationService.writeFileContent(file2, "content2");
        fileOperationService.writeFileContent(file3, "content3");
        
        // 测试列出所有文件
        List<String> allFiles = fileOperationService.listFiles(tempDir);
        assertEquals("应该有3个文件", 3, allFiles.size());
        
        // 测试按扩展名过滤
        List<String> txtFiles = fileOperationService.listFiles(tempDir, "txt");
        assertEquals("应该有2个txt文件", 2, txtFiles.size());
        
        List<String> logFiles = fileOperationService.listFiles(tempDir, "log");
        assertEquals("应该有1个log文件", 1, logFiles.size());
    }
    
    @Test
    public void testBatchCopyFiles() {
        // 创建源文件
        String source1 = tempDir + "/source1.txt";
        String source2 = tempDir + "/source2.txt";
        String target1 = tempDir + "/target1.txt";
        String target2 = tempDir + "/target2.txt";
        
        fileOperationService.writeFileContent(source1, "content1");
        fileOperationService.writeFileContent(source2, "content2");
        
        // 准备批量复制映射
        Map<String, String> fileMap = new HashMap<>();
        fileMap.put(source1, target1);
        fileMap.put(source2, target2);
        
        // 测试批量复制
        FileOperationResult batchResult = fileOperationService.batchCopyFiles(fileMap, false);
        assertTrue("批量复制应该成功", batchResult.isSuccess());
        assertEquals("处理文件数应该是2", 2, batchResult.getProcessedCount());
        assertEquals("失败文件数应该是0", 0, batchResult.getFailedCount());
        
        // 验证文件存在
        assertTrue("目标文件1应该存在", fileOperationService.fileExists(target1));
        assertTrue("目标文件2应该存在", fileOperationService.fileExists(target2));
        
        // 验证内容
        assertEquals("目标文件1内容应该正确", "content1", fileOperationService.readFileContent(target1));
        assertEquals("目标文件2内容应该正确", "content2", fileOperationService.readFileContent(target2));
    }
    
    @Test
    public void testBatchDeleteFiles() {
        // 创建测试文件
        String file1 = tempDir + "/delete1.txt";
        String file2 = tempDir + "/delete2.txt";
        String file3 = tempDir + "/delete3.txt";
        
        fileOperationService.writeFileContent(file1, "content1");
        fileOperationService.writeFileContent(file2, "content2");
        fileOperationService.writeFileContent(file3, "content3");
        
        // 准备删除列表
        List<String> filesToDelete = Arrays.asList(file1, file2, file3);
        
        // 测试批量删除
        FileOperationResult batchResult = fileOperationService.batchDeleteFiles(filesToDelete);
        assertTrue("批量删除应该成功", batchResult.isSuccess());
        assertEquals("处理文件数应该是3", 3, batchResult.getProcessedCount());
        assertEquals("失败文件数应该是0", 0, batchResult.getFailedCount());
        
        // 验证文件被删除
        assertFalse("文件1应该不存在", fileOperationService.fileExists(file1));
        assertFalse("文件2应该不存在", fileOperationService.fileExists(file2));
        assertFalse("文件3应该不存在", fileOperationService.fileExists(file3));
    }
    
    @Test
    public void testFileInfo() {
        // 创建测试文件
        fileOperationService.writeFileContent(testFile, testContent);
        
        // 测试文件大小
        long fileSize = fileOperationService.getFileSize(testFile);
        assertTrue("文件大小应该大于0", fileSize > 0);
        
        // 测试最后修改时间
        java.time.LocalDateTime lastModified = fileOperationService.getLastModifiedTime(testFile);
        assertNotNull("最后修改时间不应该为空", lastModified);
        
        // 验证修改时间在合理范围内（最近1分钟内）
        java.time.LocalDateTime now = java.time.LocalDateTime.now();
        assertTrue("修改时间应该在当前时间之前", lastModified.isBefore(now) || lastModified.isEqual(now));
        assertTrue("修改时间应该在1分钟内", 
                java.time.Duration.between(lastModified, now).toMinutes() < 1);
    }
    
    @Test
    public void testCharsetSupport() {
        String utf8Content = "UTF-8测试内容：你好世界！";
        String utf8File = tempDir + "/utf8.txt";
        
        // 测试UTF-8编码
        FileOperationResult writeResult = fileOperationService.writeFileContent(
                utf8File, utf8Content, StandardCharsets.UTF_8, false);
        assertTrue("UTF-8写入应该成功", writeResult.isSuccess());
        
        String readContent = fileOperationService.readFileContent(utf8File, StandardCharsets.UTF_8);
        assertEquals("UTF-8读取内容应该一致", utf8Content, readContent);
    }
    
    @Test
    public void testErrorHandling() {
        // 测试读取不存在的文件
        try {
            fileOperationService.readFileContent("/nonexistent/file.txt");
            fail("应该抛出异常");
        } catch (Exception e) {
            // 预期的异常
            assertNotNull("异常消息不应该为空", e.getMessage());
        }
        
        // 测试空路径
        FileOperationResult result = fileOperationService.writeFileContent("", "content");
        assertFalse("空路径写入应该失败", result.isSuccess());
        assertNotNull("错误消息不应该为空", result.getMessage());
        
        // 测试null路径
        result = fileOperationService.writeFileContent(null, "content");
        assertFalse("null路径写入应该失败", result.isSuccess());
    }
}