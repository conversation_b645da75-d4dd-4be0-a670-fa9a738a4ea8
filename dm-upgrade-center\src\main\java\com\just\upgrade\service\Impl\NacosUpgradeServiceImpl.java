package com.just.upgrade.service.Impl;

import com.just.common.utils.StringUtils;
import com.just.file.model.ConfigurationDataCenter;
import com.just.file.model.ConfigurationType;
import com.just.file.service.ExcelConfigService;
import com.just.file.service.FileOperationService;
import com.just.git.config.GitConfig;
import com.just.git.config.GitCoreProperties;
import com.just.git.model.GitBranchRequest;
import com.just.git.model.GitCloneRequest;
import com.just.git.model.GitLabProject;
import com.just.git.model.GitRepository;
import com.just.git.service.GitOperationService;
import com.just.git.service.GitProjectService;
import com.just.git.service.GitlabOperationsAccessor;
import com.just.git.service.LogStore;
import com.just.nacos.model.NacosConfig;
import com.just.nacos.model.NacosEnvironment;
import com.just.nacos.service.NacosConfigService;
import com.just.nacos.service.NacosEnvironmentService;
import com.just.template.core.TemplateEngine;
import com.just.template.core.TypedTemplateContext;
import com.just.template.factory.TypedTemplateFactory;
import com.just.template.model.ApplicationNacosConfigModel;
import com.just.template.model.BootstrapConfigModel;
import com.just.template.model.PomConfigModel;
import com.just.upgrade.enums.DmNacosGroupEnvironmentEnum;
import com.just.upgrade.model.NacosSearchRequest;
import com.just.upgrade.model.UpgradeRequest;
import com.just.upgrade.model.UpgradeStatus;
import com.just.upgrade.service.NacosUpgradeService;
import com.just.upgrade.utils.EnvironmentReplaceUtils;
import kotlin.text.Charsets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.gitlab4j.api.models.MergeRequest;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * Nacos升级服务实现类
 * 负责执行Nacos服务升级的核心逻辑
 * 使用模版方法模式
 *
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NacosUpgradeServiceImpl extends UpgradeTemplate implements NacosUpgradeService {

    private final GitConfig gitConfig;
    private final GitCoreProperties gitCoreProperties;
    private final NacosEnvironmentService nacosEnvironmentService;
    private final FileOperationService fileOperationService;
    private final ExcelConfigService excelConfigService;
    private final GitProjectService gitProjectService;
    private final GitOperationService gitOperationService;
    private final TypedTemplateFactory templateFactory;
    private final NacosConfigService nacosConfigService;

    // 存储升级状态的Map
    private final ConcurrentMap<String, UpgradeStatus> upgradeStatusMap = new ConcurrentHashMap<>();

    // 存储未解析的环境变量
    private final ConcurrentMap<String, List<String>> unresolvedEnvVarsMap = new ConcurrentHashMap<>();

    /**
     * 未解析环境变量信息
     */
    public static class UnresolvedEnvInfo {
        private String k8sEnvVarName;     // K8s环境变量名称
        private int lineNumber;           // 代码位置信息（行号）
        private String originalLine;      // 所在环境变量那一行的内容
        private String context;           // 额外的上下文信息

        public UnresolvedEnvInfo() {
        }

        public UnresolvedEnvInfo(String k8sEnvVarName, int lineNumber, String originalLine, String context) {
            this.k8sEnvVarName = k8sEnvVarName;
            this.lineNumber = lineNumber;
            this.originalLine = originalLine;
            this.context = context;
        }

        public String getK8sEnvVarName() {
            return k8sEnvVarName;
        }

        public void setK8sEnvVarName(String k8sEnvVarName) {
            this.k8sEnvVarName = k8sEnvVarName;
        }

        public int getLineNumber() {
            return lineNumber;
        }

        public void setLineNumber(int lineNumber) {
            this.lineNumber = lineNumber;
        }

        public String getOriginalLine() {
            return originalLine;
        }

        public void setOriginalLine(String originalLine) {
            this.originalLine = originalLine;
        }

        public String getContext() {
            return context;
        }

        public void setContext(String context) {
            this.context = context;
        }

        @Override
        public String toString() {
            return String.format("UnresolvedEnvInfo{k8sEnvVarName='%s', lineNumber=%d, originalLine='%s'}",
                    k8sEnvVarName, lineNumber, originalLine);
        }
    }

    public void init() {
    }

    // ==================== NacosUpgradeService 接口实现 ====================

    @Override
    public UpgradeStatus upgradeService(UpgradeRequest request) {
        log.info("开始执行升级任务 - 服务: {}, 环境: {}", request.getServiceName(), request.getEnvironment());
        UpgradeStatus status = null;
        try {
            // 使用模板方法执行升级
            status = super.executeUpgradeNacos(request);
            return status;
        } finally {
            // 确保升级完成后清理Git资源
            if (status != null) {
                cleanupGitResources(status);
            }
        }
    }
    
    /**
     * 清理升级过程中的Git资源
     */
    private void cleanupGitResources(UpgradeStatus status) {
        try {
            GitRepository repository = (GitRepository) status.getContext().get("repository");
            if (repository != null) {
                log.info("开始清理Git仓库资源: {}", repository.getWorkingDirectory());
                
                // 1. 调用GitRepository的close方法
                repository.close();
                
                // 2. 强制多次垃圾回收，确保文件句柄释放
                for (int i = 0; i < 5; i++) {
                    status.getContext().put("repository", null);
                    System.gc();
                    System.runFinalization();
                    Thread.sleep(300);
                }
                
                log.info("Git仓库资源清理完成: {}", repository.getWorkingDirectory());
                status.getMessages().add(getCurrentTime() + " - Git仓库资源已清理");
            }
        } catch (Exception e) {
            log.warn("清理Git资源失败: {}", e.getMessage());
        }
    }

    @Override
    public UpgradeStatus getUpgradeStatus(String taskId) {
        return upgradeStatusMap.get(taskId);
    }

    @Override
    public boolean cancelUpgrade(String taskId) {
        UpgradeStatus status = upgradeStatusMap.get(taskId);
        if (status != null && UpgradeStatus.Status.RUNNING.equals(status.getStatus())) {
            status.setStatus(UpgradeStatus.Status.CANCELLED);
            status.setEndTime(java.time.LocalDateTime.now());
            status.getMessages().add(getCurrentTime() + " - 升级任务已取消");
            return true;
        }
        return false;
    }

    @Override
    public NacosConfig getSingleConfig(NacosSearchRequest request) {
        log.info("获取单个Nacos配置: {}/{}", request.getDataId(), request.getGroup());
        return nacosConfigService.getConfig(request.getDataId(), request.getGroup(), request.getNamespace()).orElse(null);
    }

    @Override
    public List<NacosConfig> searchConfigs(NacosSearchRequest request) {
        log.info("搜索Nacos配置: keyword={}, environment={}, group={}, namespace={}",
                request.getKeyword(), request.getEnvironment(), request.getGroup(), request.getNamespace());

        // 获取所有配置
        List<NacosConfig> allConfigs = nacosConfigService.listConfigs(
                request.getPageNum() != null ? request.getPageNum() : 0,
                request.getPageSize() != null ? request.getPageSize() : 10000,
                request.getGroup(),
                request.getNamespace()
        );

        // 如果没有关键词，返回所有配置
        if (StringUtils.isBlank(request.getKeyword())) {
            log.info("无搜索关键词，返回所有配置，共 {} 个", allConfigs.size());
            return allConfigs;
        }

        // 根据关键词过滤配置
        List<NacosConfig> filteredConfigs = allConfigs.stream()
                .filter(config -> {
                    String keyword = request.getKeyword().toLowerCase();
                    boolean matches = false;

                    // 按dataId搜索
                    if (config.getDataId() != null && config.getDataId().toLowerCase().contains(keyword)) {
                        matches = true;
                    }

                    // 按group搜索
                    if (!matches && config.getGroup() != null && config.getGroup().toLowerCase().contains(keyword)) {
                        matches = true;
                    }

                    // 按配置内容搜索（可选）
                    if (!matches && config.getContent() != null && config.getContent().toLowerCase().contains(keyword)) {
                        matches = true;
                    }

                    return matches;
                })
                .collect(Collectors.toList());

        log.info("搜索完成，关键词: {}, 找到 {} 个匹配配置", request.getKeyword(), filteredConfigs.size());
        return filteredConfigs;
    }

    @Override
    public List<NacosConfig> listAllConfigs(NacosSearchRequest request) {
        // TODO: 实现所有配置列表逻辑
        log.info("获取所有Nacos配置: {}", request.getEnvironment());
        return nacosConfigService.listConfigs(0, 10000, request.getGroup(), request.getNamespace());
    }

    // ==================== 模版方法的具体实现 ====================

    @Override
    protected void readExcelConfiguration(UpgradeRequest request, UpgradeStatus status) throws Exception {
        log.info("读取Excel配置文件");

        try {
            // 使用简化的ExcelConfigService读取所有Nacos配置
            List<ConfigurationDataCenter.NacosProperty> nacosProperties = excelConfigService.readAllNacosConfigs();

            int configCount = nacosProperties != null ? nacosProperties.size() : 0;

            // 将配置数据存储到上下文中供后续步骤使用
            status.getContext().put("nacos_properties", nacosProperties);
            status.getContext().put("nacos_config_count", configCount);

            status.getMessages().add(String.format("%s - Excel配置文件读取完成，成功读取 %d 个配置",
                    getCurrentTime(), configCount));

            log.debug("成功读取Nacos配置，属性数: {}", configCount);
        } catch (Exception e) {
            throw new RuntimeException("读取Excel配置失败: " + e.getMessage(), e);
        }
    }

    @Override
    protected void prepareGitRepository(UpgradeRequest request, UpgradeStatus status) throws Exception {
        log.info("准备Git仓库: {}", request.getServiceName());
        GitLabProject gitLabProject = gitProjectService.searchProjectsByName(request.getServiceName()).stream()
                .filter(v -> v.getName().equals(request.getServiceName()))
                .findFirst().orElse(null);
        if (gitLabProject == null) {
            throw new RuntimeException("Git仓库不存在: " + request.getServiceName());
        }
        // 构建Git克隆请求
        GitCloneRequest cloneRequest = GitCloneRequest.builder()
                .repositoryUrl(gitLabProject.getWebUrl())
                .localPath(Paths.get(gitConfig.getWorkspaceDirectory() + "/" + request.getServiceName()))
                .branch(gitConfig.getDefaultBranch())
                .credentials(gitConfig.getDefaultCredentials())
                .build();

        boolean bool = fileOperationService.fileExists(gitConfig.getWorkspaceDirectory() + "/" + request.getServiceName());
        if (bool) {
            // 使用暴力删除方法
            forceDeleteDirectory(gitConfig.getWorkspaceDirectory() + "/" + request.getServiceName(), status);
        }
        GitRepository repository = gitOperationService.cloneRepository(cloneRequest);
        // 创建升级分支
        String branchName = request.getBranchName() != null ? request.getBranchName() : "nacos-upgrade";
        GitBranchRequest branchRequest = GitBranchRequest.builder()
                .operation(GitBranchRequest.BranchOperation.CREATE_AND_SWITCH)
                .branchName(branchName)
                .baseBranch("master")
                .build();
        gitOperationService.branchOperation(repository, branchRequest);
        // 存储repository实例供后续步骤使用
        status.getContext().put("repository", repository);
        status.getContext().put("branchName", branchName);
        status.getContext().put("upgrade_service_path", Paths.get(gitConfig.getWorkspaceDirectory() + "/" + request.getServiceName()));
        status.getMessages().add(getCurrentTime() + " - Git仓库准备完成，分支: " + branchName);
    }

    @Override
    protected void searchApplicationProperties(UpgradeRequest request, UpgradeStatus status) throws Exception {
        log.info("搜索application.properties文件");

        GitRepository repository = (GitRepository) status.getContext().get("repository");
        if (repository == null) {
            throw new RuntimeException("Git仓库实例未找到");
        }
        // 使用 dm-file-utils 搜索文件
        Path workTreePath = repository.getLocalPath();
        Path propertiesPath = fileOperationService.findFilePath(workTreePath, "application.properties");
        if (Files.exists(propertiesPath)) {
            status.getContext().put("application_properties_path", propertiesPath);
            status.getMessages().add(getCurrentTime() + " - 找到application.properties文件");
        } else {
            // 如果不存在，创建一个
            Files.createDirectories(propertiesPath.getParent());
            Files.createFile(propertiesPath);
            status.getContext().put("application_properties_path", propertiesPath);
            status.getMessages().add(getCurrentTime() + " - 创建新的application.properties文件");
        }
    }

    @Override
    protected void replaceK8sWithNacosConfig(UpgradeRequest request, UpgradeStatus status) throws Exception {
        log.info("替换K8s环境变量为Nacos配置");

        try {
            // 获取基础数据
            List<ConfigurationDataCenter.NacosProperty> nacosProperties = excelConfigService.readConfigurations(ConfigurationType.NACOS, request.getEnvironment());
            List<ConfigurationDataCenter.NacosProperty> urlProperties = excelConfigService.readConfigurations(ConfigurationType.URL_MAPPING, request.getEnvironment());
            List<ConfigurationDataCenter.NacosProperty> keyValueProperties = excelConfigService.readConfigurations(ConfigurationType.KEY_VALUE, request.getEnvironment());
            Path propertiesPath = (Path) status.getContext().get("application_properties_path");
            List<String> applicationLines = fileOperationService.readFileLines(propertiesPath.toString(), Charsets.UTF_8);
            // 空值检查
            if (nacosProperties == null) {
                nacosProperties = new ArrayList<>();
            }
            if (applicationLines == null) {
                applicationLines = new ArrayList<>();
            }
            // 统计信息
            List<String> k8sEnv = applicationLines.stream()
                    .map(EnvironmentReplaceUtils::extractEnvironmentVariables)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
            log.info("开始处理 {} 行配置，发现 {} 个K8s环境变量", applicationLines.size(), k8sEnv.size());

            // 初始化数据结构
            List<String> processedLines = new ArrayList<>();
            List<UnresolvedEnvInfo> unresolvedEnvVars = new ArrayList<>();
            java.util.Set<String> usedNacosFileNames = new java.util.HashSet<>();
            int replacedCount = 0;

            // 逐行处理环境变量替换
            for (int lineIndex = 0; lineIndex < applicationLines.size(); lineIndex++) {
                String line = applicationLines.get(lineIndex);
                ProcessLineResult result = processLineForEnvironmentReplacement(
                        line, lineIndex + 1, nacosProperties, urlProperties, keyValueProperties, unresolvedEnvVars, usedNacosFileNames);
                processedLines.add(result.processedLine);
                replacedCount += result.replacementCount;
            }
            // 生成最终配置内容
            String applicationContent = String.join("\n", processedLines);
            // 使用 dm-template-core 进行模板处理
            TemplateEngine templateEngine = templateFactory.getTemplateEngine();
            // 构建应用配置模型
            ApplicationNacosConfigModel configModel = ApplicationNacosConfigModel.builder()
                    .content(applicationContent)
                    .build();

            // 生成配置内容
            TypedTemplateContext context = TypedTemplateContext.from(configModel);
            String configContent = templateEngine.process("application/application.ftl", context);

            // 保存处理结果到上下文
            status.getContext().put("unresolved_env_vars", unresolvedEnvVars);
            status.getContext().put("used_nacos_file_names", usedNacosFileNames);
            status.getContext().put("total_env_vars_count", k8sEnv.size());
            status.getContext().put("replaced_env_vars_count", replacedCount);
            status.getContext().put("application_nacos_content", configContent);

            // 记录处理结果
            log.info("K8s环境变量替换完成: 总变量 {}, 成功替换 {}, 未替换 {}, 使用Nacos文件 {}",
                    k8sEnv.size(), replacedCount, unresolvedEnvVars.size(), usedNacosFileNames.size());
            fileOperationService.writeFileContent(propertiesPath.getParent() + "/application-nacos.properties", configContent);
            status.getMessages().add(getCurrentTime() + " - K8s环境变量替换完成: 总变量 " + k8sEnv.size()
                    + ", 成功替换 " + replacedCount + ", 未替换 " + unresolvedEnvVars.size());

        } catch (Exception e) {
            log.error("K8s环境变量替换失败", e);
            status.getMessages().add(getCurrentTime() + " - K8s环境变量替换失败: " + e.getMessage());
            throw new RuntimeException("K8s环境变量替换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理单行环境变量替换的结果类
     */
    private static class ProcessLineResult {
        String processedLine;
        int replacementCount;

        ProcessLineResult(String processedLine, int replacementCount) {
            this.processedLine = processedLine;
            this.replacementCount = replacementCount;
        }
    }

    /**
     * 处理单行的环境变量替换
     */
    private ProcessLineResult processLineForEnvironmentReplacement(
            String line, int lineNumber, List<ConfigurationDataCenter.NacosProperty> nacosProperties,
            List<ConfigurationDataCenter.NacosProperty> urlProperties,
            List<ConfigurationDataCenter.NacosProperty> keyValueProperties,
            List<UnresolvedEnvInfo> unresolvedEnvVars, java.util.Set<String> usedNacosFileNames) {

        if (line == null || line.trim().isEmpty() || line.trim().startsWith("#")) {
            return new ProcessLineResult(line, 0);
        }

        String processedLine = line;
        int replacementCount = 0;

        // 处理URL属性替换（直接替换，不需要${}格式）
        if (urlProperties != null) {
            for (ConfigurationDataCenter.NacosProperty urlProperty : urlProperties) {
                if (urlProperty.getK8sEnv() != null && urlProperty.getNacosEnv() != null) {
                    if (processedLine.contains(urlProperty.getK8sEnv())) {
                        processedLine = processedLine.trim().replace("${" + urlProperty.getK8sEnv() + "}", urlProperty.getNacosEnv());
                        replacementCount++;
                        log.debug("URL替换: {} -> {}", urlProperty.getK8sEnv(), urlProperty.getNacosEnv());
                    }
                }
            }
        }

        // 提取当前行的所有环境变量（${}格式）
        List<String> envVars = EnvironmentReplaceUtils.extractEnvironmentVariables(processedLine);
        for (String envVar : envVars) {
            boolean found = false;
            // 在nacosProperties中查找匹配的k8sEnv
            for (ConfigurationDataCenter.NacosProperty property : nacosProperties) {
                if (property.getK8sEnv() != null && envVar.equalsIgnoreCase(property.getK8sEnv())) {
                    if ("custom_env".equalsIgnoreCase(property.getNacosFileName())) {
                        processedLine = processedLine.replace("${" + envVar + "}", property.getNacosEnv());
                    } else {
                        // 执行替换
                        processedLine = processedLine.replace("${" + envVar + "}", "${" + property.getNacosEnv() + "}");
                    }
                    // 记录使用的Nacos文件名
                    if (property.getNacosFileName() != null && !property.getNacosFileName().trim().isEmpty()) {
                        if ("custom_env".equalsIgnoreCase(property.getNacosFileName()) || "k8sEnv".equalsIgnoreCase(property.getNacosFileName())) {
                            // todo 自定义变量 可能需要记录
                        } else {
                            usedNacosFileNames.add(property.getNacosFileName());
                        }
                    }

                    found = true;
                    replacementCount++;
                    break;
                }
            }
            // 如果未找到匹配，记录未替换的环境变量信息
            if (!found) {
                UnresolvedEnvInfo unresolvedInfo = new UnresolvedEnvInfo(
                        envVar,
                        lineNumber,
                        line,
                        "未在Nacos配置映射中找到对应的环境变量"
                );
                unresolvedEnvVars.add(unresolvedInfo);
            }
        }
        Map<String, UnresolvedEnvInfo> unresolvedEnvInfoMap = unresolvedEnvVars.stream().collect(Collectors.toMap(
                UnresolvedEnvInfo::getK8sEnvVarName, v -> v, (v1, v2) -> v1));
        Map<String, ConfigurationDataCenter.NacosProperty> collect = keyValueProperties.stream()
                .collect(Collectors.toMap(ConfigurationDataCenter.NacosProperty::getK8sEnv, v -> v, (v1, v2) -> v1));
        // 再次用k8s环境变量处理下
        List<String> k8sVars = EnvironmentReplaceUtils.extractEnvironmentVariables(processedLine);
        for (String envVar : k8sVars) {
            // 在k8s中查找匹配的
            ConfigurationDataCenter.NacosProperty property = collect.get(envVar);
            if (Objects.isNull(property)) {
                break;
            }
            if (property.getK8sEnv() != null && envVar.equalsIgnoreCase(property.getK8sEnv())) {
                // 执行替换
                processedLine = processedLine.replace("${" + envVar + "}", property.getNacosEnv());
                replacementCount++;
                unresolvedEnvInfoMap.remove(envVar);
            }
        }
        unresolvedEnvVars = new ArrayList<>(unresolvedEnvInfoMap.values());
        return new ProcessLineResult(processedLine, replacementCount);
    }

    @Override
    protected void generateBootstrapAndPom(UpgradeRequest request, UpgradeStatus status) throws Exception {
        log.info("生成bootstrap和POM文件");
        TemplateEngine templateEngine = templateFactory.getTemplateEngine();
        Optional<NacosEnvironment> environment = nacosEnvironmentService.getEnvironment(request.getEnvironment());
        if (!environment.isPresent()) {
            status.getMessages().add(getCurrentTime() + " - 未找到 Aliyun MSE Nacos环境 - " + request.getEnvironment());
            environment = nacosEnvironmentService.getDefaultEnvironment();
            status.getMessages().add(getCurrentTime() + " - 使用默认环境 - " + environment.get().getName());
        }
        Optional<DmNacosGroupEnvironmentEnum> byGroupName = DmNacosGroupEnvironmentEnum.getByGroupName(request.getDmGroup());
        if (!byGroupName.isPresent()) {
            status.getMessages().add(getCurrentTime() + " - 未找到 dm group  - " + request.getDmGroup());
            return;
        }
        DmNacosGroupEnvironmentEnum dmNacosGroupEnvironmentEnum = byGroupName.get();
        HashSet<String> usedNacosFileNames = (HashSet<String>) status.getContext().get("used_nacos_file_names");
        List<BootstrapConfigModel.ExtensionConfig> extensionConfigs = usedNacosFileNames.stream().map(v ->
                        new BootstrapConfigModel.ExtensionConfig(v, "public", request.getAutoRefresh()))
                .collect(Collectors.toList());

        // 生成bootstrap配置
        NacosEnvironment nacosEnvironment = environment.get();
        BootstrapConfigModel bootstrapModel = BootstrapConfigModel.builder()
                .serviceName(request.getServiceName())
                .nacosServerAddr("${NACOS_ADDR:" + nacosEnvironment.getServerAddr() + "}")
                .nacosNamespace("${NACOS_NAMESPACE:" + nacosEnvironment.getNamespace() + "}")
                .nacosAccessKey("${" + dmNacosGroupEnvironmentEnum.getAccessKeyEnvVar() + "}")
                .nacosSecretKey("${" + dmNacosGroupEnvironmentEnum.getSecretKeyEnvVar() + "}")
                .extensionConfigs(extensionConfigs)
                .enableDiscovery(request.getUpgradeRegistry())
                .refresh(request.getAutoRefresh())
                .build();

        TypedTemplateContext bootstrapContext = TypedTemplateContext.from(bootstrapModel);
        String bootstrapContent = templateEngine.process("bootstrap/bootstrap-simple.ftl", bootstrapContext);
        Path applicationPath = (Path) status.getContext().get("application_properties_path");
        fileOperationService.writeFileContent(applicationPath.getParent() + "/bootstrap.properties", bootstrapContent);
        status.getMessages().add(getCurrentTime() + " - Bootstrap文件生成完成");

        PomConfigModel pomModel = PomConfigModel.builder()
                .version("1.0.5")
                .register(request.getUpgradeRegistry())
                .build();
        TypedTemplateContext pomContext = TypedTemplateContext.from(pomModel);
        String dependenciesContent = templateEngine.process("pom/pom-nacos-dependencies.ftl", pomContext);
        String parentContent = templateEngine.process("pom/pom-parent-replacement.ftl", pomContext);
        // 开始替换文件内容
        Path path = (Path) status.getContext().get("upgrade_service_path");
        Path pomPath = fileOperationService.findFilePath(path, "pom.xml");
        replacePomParent(status, pomPath, dependenciesContent, parentContent);
        status.getMessages().add(getCurrentTime() + " - POM文件已更新");
    }

    @Override
    protected void commitAndPush(UpgradeRequest request, UpgradeStatus status) throws Exception {
        log.info("提交代码并推送到远程分支");

        GitRepository repository = (GitRepository) status.getContext().get("repository");
        String branchName = (String) status.getContext().get("branchName");

        // 确保Git能够识别所有更改（包括删除的文件）
        try {
            org.eclipse.jgit.api.Git git = org.eclipse.jgit.api.Git.open(repository.getWorkingDirectory().toFile());

            // 检查工作区状态
            org.eclipse.jgit.api.Status gitStatus = git.status().call();
            log.info("Git状态检查 - 新增: {}, 修改: {}, 删除: {}, 未跟踪: {}",
                    gitStatus.getAdded().size(),
                    gitStatus.getModified().size(),
                    gitStatus.getRemoved().size(),
                    gitStatus.getUntracked().size());

            // 如果有删除的文件，记录到日志
            if (!gitStatus.getMissing().isEmpty()) {
                log.info("检测到丢失的文件（需要删除）: {}", gitStatus.getMissing());
                status.getMessages().add(getCurrentTime() + " - 检测到 " + gitStatus.getMissing().size() + " 个文件需要从Git中删除");
            }

            git.close();
        } catch (Exception e) {
            log.warn("检查Git状态失败", e);
        }

        LogStore logStore = new LogStore();
        logStore.setAllSteps(2);
        GitlabOperationsAccessor gitlabOperationsAccessor = new GitlabOperationsAccessor(gitCoreProperties.getBaseUrl(),
                gitCoreProperties.getToken(),
                request.getServiceName(),
                repository.getWorkingDirectory(), logStore);

        // gitlabOperationsAccessor.checkRunning(repository.getWorkingDirectory(), null);
        MergeRequest merge = gitlabOperationsAccessor.commitAndPush("自动升级到Nacos配置中心", "feat: 自动升级到Nacos配置中心");
        gitlabOperationsAccessor.createMergeRequestNote(merge.getIid(), "```shell\n" + logStore + "```");

        status.getMessages().add(getCurrentTime() + " - 代码已提交并推送到分支: " + branchName);
        status.getMessages().add(getCurrentTime() + " - 请手动创建合并请求");
    }

    @Override
    protected void publishNacos(UpgradeRequest request, UpgradeStatus status) {
        log.info("开始推送配置到Nacos服务器");

        try {
            if (!request.getPublishMseNacos()) {
                return;
            }
            // 1. 从上下文获取application_nacos_content
            String applicationNacosContent = (String) status.getContext().get("application_nacos_content");
            if (applicationNacosContent == null || applicationNacosContent.trim().isEmpty()) {
                throw new RuntimeException("未找到生成的application-nacos配置内容");
            }
            // 2. 构建配置参数
            String serviceName = request.getServiceName();
            String environment = request.getEnvironment();
            String dataId = serviceName + ".properties";
            String group = serviceName;
            String namespace = environment;
            status.getMessages().add(getCurrentTime() + " - 配置推送参数: " + String.format("dataId: %s, group: %s, namespace: %s", dataId, group, namespace));
            // 3. 检查云端是否存在配置
            boolean configExists = nacosConfigService.configExists(dataId, group, namespace);
            if (configExists) {
                log.info("检测到云端已存在配置: {}/{}/{}", namespace, group, dataId);
                // 4. 获取现有配置内容
                Optional<NacosConfig> existingConfigOpt = nacosConfigService.getConfig(dataId, group, namespace);

                if (existingConfigOpt.isPresent()) {
                    NacosConfig existingConfig = existingConfigOpt.get();
                    String existingContent = existingConfig.getContent();

                    if (existingContent != null && !existingContent.trim().isEmpty()) {
                        // 5. 创建备份配置
                        String backupDataId = createBackupDataId(dataId);
                        NacosConfig backupConfig = NacosConfig.builder()
                                .dataId(backupDataId)
                                .group(group)
                                .namespace(namespace)
                                .content(existingContent)
                                .type("properties")
                                .environment(environment)
                                .appName(serviceName)
                                .createTime(java.time.LocalDateTime.now())
                                .updateTime(java.time.LocalDateTime.now())
                                .status(com.just.nacos.model.NacosConfig.ConfigStatus.ACTIVE)
                                .build();
                        // 6. 推送备份配置
                        boolean backupResult = nacosConfigService.publishConfig(backupConfig);
                        if (backupResult) {
                            log.info("备份配置推送成功: {}", backupDataId);
                            status.getMessages().add(getCurrentTime() + " - 现有配置已备份为: " + backupDataId);
                        } else {
                            log.warn("备份配置推送失败: {}", backupDataId);
                            status.getMessages().add(getCurrentTime() + " - 备份配置推送失败: " + backupDataId);
                        }
                    } else {
                        log.info("现有配置内容为空，跳过备份步骤");
                    }
                } else {
                    log.warn("无法获取现有配置内容，跳过备份步骤");
                }
            } else {
                log.info("云端不存在配置，直接推送新配置");
            }

            // 7. 推送新的application-nacos配置
            NacosConfig newConfig = NacosConfig.builder()
                    .dataId(dataId)
                    .group(group)
                    .namespace(namespace)
                    .content(applicationNacosContent)
                    .type("properties")
                    .environment(environment)
                    .appName(serviceName)
                    .createTime(java.time.LocalDateTime.now())
                    .updateTime(java.time.LocalDateTime.now())
                    .status(com.just.nacos.model.NacosConfig.ConfigStatus.ACTIVE)
                    .build();

            // 8. 执行推送
            boolean publishResult = nacosConfigService.publishConfig(newConfig);

            if (publishResult) {
                log.info("配置推送成功: {}/{}/{}", namespace, group, dataId);
                status.getMessages().add(getCurrentTime() + " - 配置推送成功: " + dataId);

                // 9. 更新上下文信息
                status.getContext().put("nacos_publish_success", true);
                status.getContext().put("nacos_dataId", dataId);
                status.getContext().put("nacos_group", group);
                status.getContext().put("nacos_namespace", namespace);

            } else {
                log.error("配置推送失败: {}/{}/{}", namespace, group, dataId);
                status.getMessages().add(getCurrentTime() + " - 配置推送失败: " + dataId);
                throw new RuntimeException("配置推送失败: " + dataId);
            }

        } catch (Exception e) {
            log.error("推送配置到Nacos失败", e);
            status.getMessages().add(getCurrentTime() + " - 推送配置到Nacos失败: " + e.getMessage());
            throw new RuntimeException("推送配置到Nacos失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建备份配置的DataId
     * 格式: 原dataId + 时间戳 + "-backup.properties"
     *
     * @param originalDataId 原始dataId
     * @return 备份dataId
     */
    private String createBackupDataId(String originalDataId) {
        String timestamp = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        // 移除原有的.properties后缀，添加时间戳和backup后缀
        String baseName = originalDataId.replaceAll("\\.properties$", "");
        return baseName + "-" + timestamp + "-backup.properties";
    }

    @Override
    protected void handleUnresolvedEnvVars(UpgradeStatus status) {
        log.info("处理未解析的环境变量");

        // 从上下文中获取未解析的环境变量信息
        Object unresolvedEnvVarsObj = status.getContext().get("unresolved_env_vars");

        if (unresolvedEnvVarsObj instanceof List) {
            @SuppressWarnings("unchecked")
            List<UnresolvedEnvInfo> unresolvedEnvVars = (List<UnresolvedEnvInfo>) unresolvedEnvVarsObj;

            if (!unresolvedEnvVars.isEmpty()) {
                // 设置到状态对象中，但需要转换为字符串列表以兼容现有接口
                List<String> unresolvedVarNames = unresolvedEnvVars.stream()
                        .map(UnresolvedEnvInfo::getK8sEnvVarName)
                        .collect(java.util.stream.Collectors.toList());

                status.setUnresolvedEnvVars(unresolvedVarNames);

                // 同时保存完整的UnresolvedEnvInfo对象到context中，供前端使用
                status.getContext().put("unresolved_env_info_objects", unresolvedEnvVars);

                status.getMessages().add(getCurrentTime() + " - 发现 " + unresolvedEnvVars.size() + " 个未解析的环境变量，需要手动处理");

                // 记录详细信息到日志
                log.info("未解析的环境变量详情:");
                for (UnresolvedEnvInfo envInfo : unresolvedEnvVars) {
                    log.info("  - 变量: {}, 行号: {}, 原始行: {}",
                            envInfo.getK8sEnvVarName(),
                            envInfo.getLineNumber(),
                            envInfo.getOriginalLine());
                }
            } else {
                status.getMessages().add(getCurrentTime() + " - 所有环境变量已成功解析");
            }
        } else {
            log.warn("未找到未解析环境变量信息或格式不正确");
            status.getMessages().add(getCurrentTime() + " - 无法获取环境变量解析状态");
        }
    }

    private void replacePomParent(UpgradeStatus status, Path pomPath, String nacosDependencyContent, String partentContent) {
        try {
            if (pomPath == null) {
                status.getMessages().add(getCurrentTime() + " - 未在服务目录下找到 pom.xml 文件");
                return;
            }
            // 读取原文件内容
            boolean parentReplaced = true;
            String pomContext = new String(Files.readAllBytes(pomPath))
                    .replaceAll("(<parent[^>]*>[\\s\\S]*?<\\/parent>)", partentContent);
            Files.write(pomPath, pomContext.getBytes(StandardCharsets.UTF_8));

            if (parentReplaced) {
                status.getMessages().add(getCurrentTime() + " - √  替换【pom.xml】parent标签成功");
                status.getMessages().add(getCurrentTime() + " -> " + pomPath);
                // 在parent替换完成后添加nacos依赖
                addNacosDependency(status, pomPath, nacosDependencyContent);
            } else {
                status.getMessages().add(getCurrentTime() + " × 替换【pom.xml】parent标签失败");
            }

        } catch (IOException e) {
            status.getMessages().add(getCurrentTime() + " × 替换【pom.xml】parent标签失败, exception:" + e.getMessage());
        }
    }

    private void addNacosDependency(UpgradeStatus status, Path pomPath, String nacosDependencyContent) throws IOException {
        List<String> lines = Files.readAllLines(pomPath);
        List<String> newLines = new ArrayList<>();
        boolean inDependencies = false;
        boolean hasNacosDependency = false;
        boolean hasNacosDiscoveryDependency = false;
        boolean dependencyAdded = false;
        boolean discoveryDependencyAdded = false;

        for (String line : lines) {
            if (line.trim().contains("<dependencies>")) {
                inDependencies = true;
            } else if (line.trim().contains("</dependencies>")) {
                if (inDependencies) {
                    // 添加nacos-config依赖
                    if (!hasNacosDependency && !dependencyAdded && !hasNacosDiscoveryDependency && !discoveryDependencyAdded) {
                        newLines.add(nacosDependencyContent);
                        dependencyAdded = true;
                    }
                }
                inDependencies = false;
            } else if (inDependencies) {
                // 检查是否已存在nacos依赖
                if (line.contains("spring-cloud-starter-alibaba-nacos-config")) {
                    hasNacosDependency = true;
                }
                // 检查是否已存在nacos-discovery依赖
                if (line.contains("spring-cloud-starter-alibaba-nacos-discovery")) {
                    hasNacosDiscoveryDependency = true;
                }
            }
            newLines.add(line);  // 移动到这里，确保在添加新依赖后再添加</dependencies>
        }

        if (dependencyAdded || (discoveryDependencyAdded)) {
            Files.write(pomPath, newLines);
            if (dependencyAdded) {
                status.getMessages().add(getCurrentTime() + " - √  添加nacos-config依赖成功");
            }
            if (discoveryDependencyAdded) {
                status.getMessages().add(getCurrentTime() + " - √   添加nacos-discovery依赖成功");
            }
        } else if (hasNacosDependency) {
            status.getMessages().add(getCurrentTime() + " - i  nacos-config依赖已存在，无需添加");
        } else if (hasNacosDiscoveryDependency) {
            status.getMessages().add(getCurrentTime() + " - i  nacos-discovery依赖已存在，无需添加");
        } else if (!dependencyAdded && !hasNacosDependency) {
            status.getMessages().add(getCurrentTime() + " - × 未找到dependencies标签，无法添加nacos依赖");
        }
    }

    /**
     * 暴力删除目录
     * 使用最简单直接的删除方式
     *
     * @param directoryPath 要删除的目录路径
     * @param status 升级状态对象
     */
    private void forceDeleteDirectory(String directoryPath, UpgradeStatus status) {
        Path targetPath = Paths.get(directoryPath);

        if (!Files.exists(targetPath)) {
            status.getMessages().add(getCurrentTime() + " - 目录不存在，无需删除: " + directoryPath);
            return;
        }

        status.getMessages().add(getCurrentTime() + " - 开始强力删除目录: " + directoryPath);

        try {
            // 1. 强制关闭可能存在的Git连接
            forceCloseGitRepository(targetPath, status);
            
            // 2. 在Windows系统上先设置文件为可写
            if (System.getProperty("os.name").toLowerCase().contains("windows")) {
                makeDirectoryWritable(targetPath);
            }
            
            // 3. 多次尝试强制垃圾回收和删除
            for (int i = 0; i < 3; i++) {
                System.gc();
                System.runFinalization();
                Thread.sleep(500);
                
                try {
                    fileOperationService.deleteDirectory(directoryPath);
                    if (!Files.exists(targetPath)) {
                        status.getMessages().add(getCurrentTime() + " - 目录删除成功: " + directoryPath);
                        return;
                    }
                } catch (Exception e) {
                    log.warn("第{}次删除尝试失败: {}", i + 1, e.getMessage());
                }
            }
            
            // 4. 最后手段：重命名目录（避免阻塞）
            String timestamp = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            Path backupPath = targetPath.getParent().resolve(targetPath.getFileName() + "_tobedeleted_" + timestamp);
            Files.move(targetPath, backupPath);
            
            status.getMessages().add(getCurrentTime() + " - 无法删除，已重命名为待删除目录: " + backupPath.getFileName());
            log.warn("目录删除失败，已重命名为待删除: {} -> {}", directoryPath, backupPath);

        } catch (Exception e) {
            log.warn("强力删除完全失败，但继续执行: {}", e.getMessage());
            status.getMessages().add(getCurrentTime() + " - 删除失败但继续执行: " + e.getMessage());
            // 不抛出异常，让升级流程继续
        }
    }
    
    /**
     * 强制关闭Git仓库连接
     */
    private void forceCloseGitRepository(Path repoPath, UpgradeStatus status) {
        try {
            Path gitDir = repoPath.resolve(".git");
            if (Files.exists(gitDir)) {
                status.getMessages().add(getCurrentTime() + " - 检测到Git仓库，强制释放资源");
                
                // 尝试多种方式关闭Git仓库
                try {
                    // 方式1：直接打开并关闭
                    org.eclipse.jgit.api.Git git = org.eclipse.jgit.api.Git.open(repoPath.toFile());
                    git.getRepository().close();
                    git.close();
                } catch (Exception e) {
                    log.debug("方式1关闭失败: {}", e.getMessage());
                }
                
                try {
                    // 方式2：使用try-with-resources
                    try (org.eclipse.jgit.api.Git git = org.eclipse.jgit.api.Git.open(repoPath.toFile())) {
                        git.getRepository().close();
                    }
                } catch (Exception e) {
                    log.debug("方式2关闭失败: {}", e.getMessage());
                }
                
                // 强制多次垃圾回收
                for (int i = 0; i < 3; i++) {
                    System.gc();
                    System.runFinalization();
                    Thread.sleep(200);
                }
                
                status.getMessages().add(getCurrentTime() + " - Git仓库资源释放完成");
            }
        } catch (Exception e) {
            log.warn("强制关闭Git仓库时出现异常: {}", e.getMessage());
        }
    }

    /**
     * 在Windows系统上递归设置目录及其内容为可写
     */
    private void makeDirectoryWritable(Path directory) {
        try {
            Files.walk(directory)
                    .forEach(path -> {
                        try {
                            Files.setAttribute(path, "dos:readonly", false);
                        } catch (Exception e) {
                            // 忽略单个文件的设置失败
                        }
                    });
        } catch (Exception e) {
            log.debug("设置目录可写属性时出现异常: {}", e.getMessage());
        }
    }

}