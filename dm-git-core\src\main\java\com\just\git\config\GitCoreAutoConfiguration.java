package com.just.git.config;

import com.just.git.model.GitCredentials;
import com.just.git.service.GitOperationService;
import com.just.git.service.GitProjectService;
import com.just.git.service.GitProjectServiceImpl;
import com.just.git.service.JGitOperationServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Git核心模块自动配置
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(GitCoreProperties.class)
public class GitCoreAutoConfiguration {

    /**
     * 配置Git配置Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public GitConfig gitConfig(GitCoreProperties properties) {
        log.info("初始化Git配置: baseUrl={}", properties.getBaseUrl());
        GitCredentials credentials = null;
        if (StringUtils.isNotBlank(properties.getToken())) {
            credentials = GitCredentials.builder()
                    .authType(GitCredentials.AuthType.TOKEN)
                    .token(properties.getToken())
                    .build();
        }
        if (StringUtils.isNotBlank(properties.getLoginUserName()) && StringUtils.isNotBlank(properties.getLoginPassword())) {
            credentials = GitCredentials.builder()
                    .authType(GitCredentials.AuthType.USERNAME_PASSWORD)
                    .username(properties.getLoginUserName())
                    .password(properties.getLoginPassword())
                    .build();
        }

        return GitConfig.builder()
                .baseUrl(properties.getBaseUrl())
                .defaultBranch(properties.getDefaultBranch())
                .defaultTimeoutSeconds(properties.getDefaultTimeoutSeconds())
                .workspaceDirectory(properties.getWorkspaceDirectory())
                .autoCreateWorkspace(properties.isAutoCreateWorkspace())
                .verboseLogging(properties.isVerboseLogging())
                .defaultUserName(properties.getDefaultUserName())
                .defaultUserEmail(properties.getDefaultUserEmail())
                .defaultCredentials(credentials)
                .gitCoreProperties(properties)
                .build();
    }

    /**
     * 配置Git操作服务Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public GitOperationService gitOperationService(GitConfig gitConfig) {
        return new JGitOperationServiceImpl(gitConfig);
    }

    /**
     * 配置Git操作服务Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public GitProjectService gitProjectService(GitConfig gitConfig) {
        return new GitProjectServiceImpl(gitConfig);
    }
} 