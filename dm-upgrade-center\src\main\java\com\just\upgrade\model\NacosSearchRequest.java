package com.just.upgrade.model;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * Nacos搜索请求参数
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
public class NacosSearchRequest {

    /**
     * 环境名称
     */
    @NotBlank(message = "环境名称不能为空")
    private String environment;

    /**
     * 配置ID
     */
    private String dataId;

    /**
     * 分组名称（可选）
     */
    private String group = "";

    /**
     * 命名空间（可选）
     */
    private String namespace;

    /**
     * 文件名称（用于模糊搜索）
     */
    private String fileName;

    /**
     * 关键词（可选）
     */
    private String keyword;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 50;
} 