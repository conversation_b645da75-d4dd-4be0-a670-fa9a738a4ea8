package com.just.upgrade.service;

import com.just.nacos.model.NacosConfig;
import com.just.upgrade.model.*;

import java.util.List;

/**
 * Nacos升级服务接口
 *
 * <AUTHOR>
 * @since 2.0.0
 */
public interface NacosUpgradeService {

    /**
     * 升级Nacos服务
     *
     * @param request 升级请求
     * @return 任务ID
     */
    UpgradeStatus upgradeService(UpgradeRequest request);

    /**
     * 获取升级状态
     *
     * @param taskId 任务ID
     * @return 升级状态
     */
    UpgradeStatus getUpgradeStatus(String taskId);

    /**
     * 取消升级任务
     *
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean cancelUpgrade(String taskId);

    /**
     * 获取单个远程Nacos文件
     *
     * @param request 搜索请求
     * @return Nacos配置
     */
    NacosConfig getSingleConfig(NacosSearchRequest request);

    /**
     * 模糊搜索远程Nacos文件
     *
     * @param request 搜索请求
     * @return 配置列表
     */
    List<NacosConfig> searchConfigs(NacosSearchRequest request);

    /**
     * 获取环境的所有Nacos文件信息
     *
     * @param request 搜索请求
     * @return 配置列表
     */
    List<NacosConfig> listAllConfigs(NacosSearchRequest request);

} 