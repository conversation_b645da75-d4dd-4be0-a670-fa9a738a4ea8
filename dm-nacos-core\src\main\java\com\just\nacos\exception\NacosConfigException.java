package com.just.nacos.exception;

/**
 * Nacos配置操作异常
 *
 * <AUTHOR>
 * @since 2.0.0
 */
public class NacosConfigException extends NacosException {

    public static final String CONFIG_NOT_FOUND = "NACOS_CONFIG_NOT_FOUND";
    public static final String CONFIG_ALREADY_EXISTS = "NACOS_CONFIG_ALREADY_EXISTS";
    public static final String CONFIG_PUBLISH_FAILED = "NACOS_CONFIG_PUBLISH_FAILED";
    public static final String CONFIG_DELETE_FAILED = "NACOS_CONFIG_DELETE_FAILED";
    public static final String CONFIG_INVALID_FORMAT = "NACOS_CONFIG_INVALID_FORMAT";
    public static final String CONFIG_VALIDATION_FAILED = "NACOS_CONFIG_VALIDATION_FAILED";

    public NacosConfigException(String message) {
        super(message);
    }

    public NacosConfigException(String message, Throwable cause) {
        super(message, cause);
    }

    public NacosConfigException(String code, String message) {
        super(code, message);
    }

    public NacosConfigException(String code, String message, Throwable cause) {
        super(code, message, cause);
    }

    /**
     * 配置不存在异常
     */
    public static NacosConfigException configNotFound(String dataId, String group) {
        return new NacosConfigException(CONFIG_NOT_FOUND, 
                String.format("配置不存在: %s/%s", group, dataId));
    }

    /**
     * 配置已存在异常
     */
    public static NacosConfigException configAlreadyExists(String dataId, String group) {
        return new NacosConfigException(CONFIG_ALREADY_EXISTS, 
                String.format("配置已存在: %s/%s", group, dataId));
    }

    /**
     * 配置发布失败异常
     */
    public static NacosConfigException publishFailed(String dataId, String group, Throwable cause) {
        return new NacosConfigException(CONFIG_PUBLISH_FAILED, 
                String.format("配置发布失败: %s/%s", group, dataId), cause);
    }

    /**
     * 配置删除失败异常
     */
    public static NacosConfigException deleteFailed(String dataId, String group, Throwable cause) {
        return new NacosConfigException(CONFIG_DELETE_FAILED, 
                String.format("配置删除失败: %s/%s", group, dataId), cause);
    }

    /**
     * 配置格式无效异常
     */
    public static NacosConfigException invalidFormat(String format, String reason) {
        return new NacosConfigException(CONFIG_INVALID_FORMAT, 
                String.format("配置格式无效[%s]: %s", format, reason));
    }

    /**
     * 配置验证失败异常
     */
    public static NacosConfigException validationFailed(String field, String reason) {
        return new NacosConfigException(CONFIG_VALIDATION_FAILED, 
                String.format("配置验证失败[%s]: %s", field, reason));
    }
}