package com.just.file.service;

import com.just.common.constants.ErrorCodes;
import com.just.common.exception.BusinessException;
import com.just.common.utils.StringUtils;
import com.just.file.dto.FileOperationResult;
import com.just.file.utils.FileOperationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Stream;

/**
 * 文件操作服务
 * 提供统一的文件操作接口，包括读取、写入、复制、移动、删除等
 * 支持批量操作和异步操作
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
@Slf4j
@Service
public class FileOperationService {
    
    /**
     * 默认字符编码
     */
    private static final Charset DEFAULT_CHARSET = StandardCharsets.UTF_8;
    
    // ======================== 文件读取方法 ========================
    
    /**
     * 读取文件内容
     * 
     * @param filePath 文件路径
     * @return 文件内容
     * @throws BusinessException 读取失败时抛出
     */
    public String readFileContent(String filePath) {
        return readFileContent(filePath, DEFAULT_CHARSET);
    }
    
    /**
     * 读取文件内容
     * 
     * @param filePath 文件路径
     * @param charset 字符编码
     * @return 文件内容
     * @throws BusinessException 读取失败时抛出
     */
    public String readFileContent(String filePath, Charset charset) {
        validateFilePath(filePath, "读取文件");
        
        try {
            return FileOperationUtils.readFileToString(filePath, charset);
        } catch (Exception e) {
            log.error("读取文件失败: {}", filePath, e);
            throw new BusinessException(ErrorCodes.FILE_READ_ERROR, 
                    "读取文件失败: " + filePath, e);
        }
    }
    
    /**
     * 读取文件行
     * 
     * @param filePath 文件路径
     * @return 文件行列表
     * @throws BusinessException 读取失败时抛出
     */
    public List<String> readFileLines(String filePath) {
        return readFileLines(filePath, DEFAULT_CHARSET);
    }
    
    /**
     * 读取文件行
     * 
     * @param filePath 文件路径
     * @param charset 字符编码
     * @return 文件行列表
     * @throws BusinessException 读取失败时抛出
     */
    public List<String> readFileLines(String filePath, Charset charset) {
        validateFilePath(filePath, "读取文件行");
        
        try {
            return FileOperationUtils.readFileToLines(filePath, charset);
        } catch (Exception e) {
            log.error("读取文件行失败: {}", filePath, e);
            throw new BusinessException(ErrorCodes.FILE_READ_ERROR, 
                    "读取文件行失败: " + filePath, e);
        }
    }
    
    /**
     * 读取资源文件内容
     * 
     * @param resourcePath 资源路径
     * @return 文件内容
     * @throws BusinessException 读取失败时抛出
     */
    public String readResourceContent(String resourcePath) {
        return readResourceContent(resourcePath, DEFAULT_CHARSET);
    }
    
    /**
     * 读取资源文件内容
     * 
     * @param resourcePath 资源路径
     * @param charset 字符编码
     * @return 文件内容
     * @throws BusinessException 读取失败时抛出
     */
    public String readResourceContent(String resourcePath, Charset charset) {
        validateFilePath(resourcePath, "读取资源文件");
        
        try {
            return FileOperationUtils.readResourceToString(resourcePath, charset);
        } catch (Exception e) {
            log.error("读取资源文件失败: {}", resourcePath, e);
            throw new BusinessException(ErrorCodes.FILE_READ_ERROR, 
                    "读取资源文件失败: " + resourcePath, e);
        }
    }
    
    // ======================== 文件写入方法 ========================
    
    /**
     * 写入文件内容
     * 
     * @param filePath 文件路径
     * @param content 文件内容
     * @return 操作结果
     */
    public FileOperationResult writeFileContent(String filePath, String content) {
        return writeFileContent(filePath, content, false);
    }
    
    /**
     * 写入文件内容
     * 
     * @param filePath 文件路径
     * @param content 文件内容
     * @param append 是否追加
     * @return 操作结果
     */
    public FileOperationResult writeFileContent(String filePath, String content, boolean append) {
        return writeFileContent(filePath, content, DEFAULT_CHARSET, append);
    }
    
    /**
     * 写入文件内容
     * 
     * @param filePath 文件路径
     * @param content 文件内容
     * @param charset 字符编码
     * @param append 是否追加
     * @return 操作结果
     */
    public FileOperationResult writeFileContent(String filePath, String content, Charset charset, boolean append) {
        try {
            validateFilePath(filePath, "写入文件");
            return FileOperationUtils.writeStringToFile(filePath, content, charset, append);
        } catch (BusinessException e) {
            return FileOperationResult.failure("写入文件", filePath, e.getMessage(), e.getErrorMessage());
        }
    }
    
    /**
     * 写入文件行
     * 
     * @param filePath 文件路径
     * @param lines 文件行
     * @return 操作结果
     */
    public FileOperationResult writeFileLines(String filePath, Collection<String> lines) {
        return writeFileLines(filePath, lines, false);
    }
    
    /**
     * 写入文件行
     * 
     * @param filePath 文件路径
     * @param lines 文件行
     * @param append 是否追加
     * @return 操作结果
     */
    public FileOperationResult writeFileLines(String filePath, Collection<String> lines, boolean append) {
        return writeFileLines(filePath, lines, DEFAULT_CHARSET, append);
    }
    
    /**
     * 写入文件行
     * 
     * @param filePath 文件路径
     * @param lines 文件行
     * @param charset 字符编码
     * @param append 是否追加
     * @return 操作结果
     */
    public FileOperationResult writeFileLines(String filePath, Collection<String> lines, Charset charset, boolean append) {
        try {
            validateFilePath(filePath, "写入文件行");
            return FileOperationUtils.writeLinesToFile(filePath, lines, charset, append);
        } catch (BusinessException e) {
            return FileOperationResult.failure("写入文件行", filePath, e.getMessage(), e.getErrorMessage());
        }
    }
    
    // ======================== 文件操作方法 ========================
    
    /**
     * 复制文件
     * 
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @return 操作结果
     */
    public FileOperationResult copyFile(String sourcePath, String targetPath) {
        return copyFile(sourcePath, targetPath, false);
    }
    
    /**
     * 复制文件
     * 
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @param overwrite 是否覆盖
     * @return 操作结果
     */
    public FileOperationResult copyFile(String sourcePath, String targetPath, boolean overwrite) {
        try {
            validateFilePath(sourcePath, "复制文件源路径");
            validateFilePath(targetPath, "复制文件目标路径");
            return FileOperationUtils.copyFile(sourcePath, targetPath, overwrite);
        } catch (BusinessException e) {
            return FileOperationResult.failure("复制文件", sourcePath, e.getMessage(), e.getErrorMessage());
        }
    }
    
    /**
     * 移动文件
     * 
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @return 操作结果
     */
    public FileOperationResult moveFile(String sourcePath, String targetPath) {
        return moveFile(sourcePath, targetPath, false);
    }
    
    /**
     * 移动文件
     * 
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @param overwrite 是否覆盖
     * @return 操作结果
     */
    public FileOperationResult moveFile(String sourcePath, String targetPath, boolean overwrite) {
        try {
            validateFilePath(sourcePath, "移动文件源路径");
            validateFilePath(targetPath, "移动文件目标路径");
            return FileOperationUtils.moveFile(sourcePath, targetPath, overwrite);
        } catch (BusinessException e) {
            return FileOperationResult.failure("移动文件", sourcePath, e.getMessage(), e.getErrorMessage());
        }
    }
    
    /**
     * 删除文件
     * 
     * @param filePath 文件路径
     * @return 操作结果
     */
    public FileOperationResult deleteFile(String filePath) {
        try {
            validateFilePath(filePath, "删除文件");
            return FileOperationUtils.deleteFile(filePath);
        } catch (BusinessException e) {
            return FileOperationResult.failure("删除文件", filePath, e.getMessage(), e.getErrorMessage());
        }
    }
    
    /**
     * 备份文件
     * 
     * @param filePath 文件路径
     * @return 操作结果
     */
    public FileOperationResult backupFile(String filePath) {
        try {
            validateFilePath(filePath, "备份文件");
            return FileOperationUtils.backupFile(filePath);
        } catch (BusinessException e) {
            return FileOperationResult.failure("备份文件", filePath, e.getMessage(), e.getErrorMessage());
        }
    }
    
    // ======================== 目录操作方法 ========================
    
    /**
     * 创建目录
     * 
     * @param directoryPath 目录路径
     * @return 操作结果
     */
    public FileOperationResult createDirectory(String directoryPath) {
        try {
            validateFilePath(directoryPath, "创建目录");
            return FileOperationUtils.createDirectoryIfNotExists(directoryPath);
        } catch (BusinessException e) {
            return FileOperationResult.failure("创建目录", directoryPath, e.getMessage(), e.getErrorMessage());
        }
    }
    
    /**
     * 删除目录
     * 
     * @param directoryPath 目录路径
     * @return 操作结果
     */
    public FileOperationResult deleteDirectory(String directoryPath) {
        try {
            validateFilePath(directoryPath, "删除目录");
            return FileOperationUtils.deleteDirectory(directoryPath);
        } catch (BusinessException e) {
            return FileOperationResult.failure("删除目录", directoryPath, e.getMessage(), e.getErrorMessage());
        }
    }
    
    /**
     * 列出目录中的文件
     * 
     * @param directoryPath 目录路径
     * @return 文件列表
     * @throws BusinessException 操作失败时抛出
     */
    public List<String> listFiles(String directoryPath) {
        return listFiles(directoryPath, null);
    }
    
    /**
     * 列出目录中的文件（支持扩展名过滤）
     * 
     * @param directoryPath 目录路径
     * @param extension 文件扩展名
     * @return 文件列表
     * @throws BusinessException 操作失败时抛出
     */
    public List<String> listFiles(String directoryPath, String extension) {
        try {
            validateFilePath(directoryPath, "列出文件");
            return FileOperationUtils.listFiles(directoryPath, extension);
        } catch (Exception e) {
            log.error("列出文件失败: {}", directoryPath, e);
            throw new BusinessException(ErrorCodes.DIRECTORY_NOT_FOUND, 
                    "列出文件失败: " + directoryPath, e);
        }
    }

    /**
     * 列出目录中的文件（支持扩展名过滤）
     *
     * @param directoryPath 目录路径
     * @param fileName 文件名
     * @return 文件列表
     * @throws BusinessException 操作失败时抛出
     */
    public Path findFilePath(Path directoryPath, String fileName) {
        try {
            return findFile(directoryPath, fileName);
        } catch (Exception e) {
            log.error("列出文件失败: {}", directoryPath, e);
            throw new BusinessException(ErrorCodes.DIRECTORY_NOT_FOUND,
                    "列出文件失败: " + directoryPath, e);
        }
    }


    // ======================== 批量操作方法 ========================
    
    /**
     * 批量复制文件
     * 
     * @param fileMap 文件映射（源路径 -> 目标路径）
     * @param overwrite 是否覆盖
     * @return 批量操作结果
     */
    public FileOperationResult batchCopyFiles(Map<String, String> fileMap, boolean overwrite) {
        if (fileMap == null || fileMap.isEmpty()) {
            return FileOperationResult.failure("批量复制文件", "", "文件映射不能为空", "参数验证失败");
        }
        
        FileOperationResult batchResult = FileOperationResult.builder()
                .operationType("批量复制文件")
                .filePath(String.format("%d个文件", fileMap.size()))
                .build()
                .start();
        
        List<String> failedFiles = new ArrayList<>();
        int successCount = 0;
        
        for (Map.Entry<String, String> entry : fileMap.entrySet()) {
            String sourcePath = entry.getKey();
            String targetPath = entry.getValue();
            
            FileOperationResult result = copyFile(sourcePath, targetPath, overwrite);
            if (result.isSuccess()) {
                successCount++;
            } else {
                failedFiles.add(sourcePath);
                log.warn("复制文件失败: {} -> {}, 原因: {}", sourcePath, targetPath, result.getMessage());
            }
        }
        
        batchResult.setProcessedCount(fileMap.size());
        batchResult.setFailedCount(failedFiles.size());
        batchResult.setFailedFiles(failedFiles);
        batchResult.setSuccess(failedFiles.isEmpty());
        batchResult.setMessage(String.format("批量复制完成，成功: %d, 失败: %d", successCount, failedFiles.size()));
        
        if (!failedFiles.isEmpty()) {
            batchResult.setErrorDetail(String.format("失败文件: %s", String.join(", ", failedFiles)));
        }
        
        return batchResult.finish();
    }
    
    /**
     * 批量删除文件
     * 
     * @param filePaths 文件路径列表
     * @return 批量操作结果
     */
    public FileOperationResult batchDeleteFiles(List<String> filePaths) {
        if (filePaths == null || filePaths.isEmpty()) {
            return FileOperationResult.failure("批量删除文件", "", "文件路径列表不能为空", "参数验证失败");
        }
        
        FileOperationResult batchResult = FileOperationResult.builder()
                .operationType("批量删除文件")
                .filePath(String.format("%d个文件", filePaths.size()))
                .build()
                .start();
        
        List<String> failedFiles = new ArrayList<>();
        int successCount = 0;
        
        for (String filePath : filePaths) {
            FileOperationResult result = deleteFile(filePath);
            if (result.isSuccess()) {
                successCount++;
            } else {
                failedFiles.add(filePath);
                log.warn("删除文件失败: {}, 原因: {}", filePath, result.getMessage());
            }
        }
        
        batchResult.setProcessedCount(filePaths.size());
        batchResult.setFailedCount(failedFiles.size());
        batchResult.setFailedFiles(failedFiles);
        batchResult.setSuccess(failedFiles.isEmpty());
        batchResult.setMessage(String.format("批量删除完成，成功: %d, 失败: %d", successCount, failedFiles.size()));
        
        if (!failedFiles.isEmpty()) {
            batchResult.setErrorDetail(String.format("失败文件: %s", String.join(", ", failedFiles)));
        }
        
        return batchResult.finish();
    }
    
    // ======================== 异步操作方法 ========================
    
    /**
     * 异步写入文件
     * 
     * @param filePath 文件路径
     * @param content 文件内容
     * @return 异步操作结果
     */
    public CompletableFuture<FileOperationResult> writeFileContentAsync(String filePath, String content) {
        return CompletableFuture.supplyAsync(() -> writeFileContent(filePath, content));
    }
    
    /**
     * 异步复制文件
     * 
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @return 异步操作结果
     */
    public CompletableFuture<FileOperationResult> copyFileAsync(String sourcePath, String targetPath) {
        return CompletableFuture.supplyAsync(() -> copyFile(sourcePath, targetPath));
    }
    
    /**
     * 异步批量操作
     * 
     * @param fileMap 文件映射
     * @param overwrite 是否覆盖
     * @return 异步操作结果
     */
    public CompletableFuture<FileOperationResult> batchCopyFilesAsync(Map<String, String> fileMap, boolean overwrite) {
        return CompletableFuture.supplyAsync(() -> batchCopyFiles(fileMap, overwrite));
    }
    
    // ======================== 文件信息方法 ========================
    
    /**
     * 检查文件是否存在
     * 
     * @param filePath 文件路径
     * @return 是否存在
     */
    public boolean fileExists(String filePath) {
        return FileOperationUtils.exists(filePath);
    }
    
    /**
     * 检查是否为文件
     * 
     * @param filePath 文件路径
     * @return 是否为文件
     */
    public boolean isFile(String filePath) {
        return FileOperationUtils.isFile(filePath);
    }
    
    /**
     * 检查是否为目录
     * 
     * @param directoryPath 目录路径
     * @return 是否为目录
     */
    public boolean isDirectory(String directoryPath) {
        return FileOperationUtils.isDirectory(directoryPath);
    }
    
    /**
     * 获取文件大小
     * 
     * @param filePath 文件路径
     * @return 文件大小（字节）
     * @throws BusinessException 操作失败时抛出
     */
    public long getFileSize(String filePath) {
        try {
            validateFilePath(filePath, "获取文件大小");
            return FileOperationUtils.getFileSize(filePath);
        } catch (Exception e) {
            log.error("获取文件大小失败: {}", filePath, e);
            throw new BusinessException(ErrorCodes.FILE_READ_ERROR, 
                    "获取文件大小失败: " + filePath, e);
        }
    }
    
    /**
     * 获取文件最后修改时间
     * 
     * @param filePath 文件路径
     * @return 最后修改时间
     * @throws BusinessException 操作失败时抛出
     */
    public LocalDateTime getLastModifiedTime(String filePath) {
        try {
            validateFilePath(filePath, "获取文件修改时间");
            return FileOperationUtils.getLastModifiedTime(filePath);
        } catch (Exception e) {
            log.error("获取文件修改时间失败: {}", filePath, e);
            throw new BusinessException(ErrorCodes.FILE_READ_ERROR, 
                    "获取文件修改时间失败: " + filePath, e);
        }
    }
    
    // ======================== 临时文件方法 ========================
    
    /**
     * 创建临时文件
     * 
     * @param prefix 文件名前缀
     * @param suffix 文件名后缀
     * @return 临时文件路径
     * @throws BusinessException 创建失败时抛出
     */
    public String createTempFile(String prefix, String suffix) {
        try {
            return FileOperationUtils.createTempFile(prefix, suffix);
        } catch (Exception e) {
            log.error("创建临时文件失败", e);
            throw new BusinessException(ErrorCodes.FILE_CREATE_ERROR, 
                    "创建临时文件失败", e);
        }
    }
    
    /**
     * 创建临时目录
     * 
     * @param prefix 目录名前缀
     * @return 临时目录路径
     * @throws BusinessException 创建失败时抛出
     */
    public String createTempDirectory(String prefix) {
        try {
            return FileOperationUtils.createTempDirectory(prefix);
        } catch (Exception e) {
            log.error("创建临时目录失败", e);
            throw new BusinessException(ErrorCodes.DIRECTORY_CREATE_ERROR, 
                    "创建临时目录失败", e);
        }
    }
    
    // ======================== 私有方法 ========================

    private Path findFile(Path directory, String fileName) throws IOException {
        if (!Files.exists(directory)) {
            return null;
        }
        try (Stream<Path> paths = Files.walk(directory)) {
            return paths
                    .filter(Files::isRegularFile)
                    .filter(path -> path.getFileName().toString().equals(fileName))
                    .findFirst()
                    .orElse(null);
        }
    }

    /**
     * 验证文件路径
     * 
     * @param filePath 文件路径
     * @param operation 操作名称
     * @throws BusinessException 验证失败时抛出
     */
    private void validateFilePath(String filePath, String operation) {
        if (StringUtils.isBlank(filePath)) {
            throw BusinessException.paramNull("filePath (" + operation + ")");
        }
    }
}