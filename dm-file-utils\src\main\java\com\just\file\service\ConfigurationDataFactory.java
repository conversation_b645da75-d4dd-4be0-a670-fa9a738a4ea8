package com.just.file.service;

import com.just.file.config.ExcelConfigProperties;
import com.just.file.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Sets;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.just.file.model.ConfigurationType.NACOS;
import static com.just.file.model.ConfigurationType.URL_MAPPING;

/**
 * 配置数据工厂
 * 负责自动加载所有配置信息并提供统一的访问接口
 *
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ConfigurationDataFactory {

    private final ExcelConfigProperties configProperties;
    private final List<ConfigurationProcessor> processors;
    private final List<LoadingEnvAware> loadingEnvAwares;

    /**
     * 配置数据缓存
     * Key: ConfigurationType, Value: 完整的配置数据中心对象
     */
    private final Map<ConfigurationType, ConfigurationDataCenter> configurationCache =
            new ConcurrentHashMap<>();

    /**
     * 处理器映射
     */
    private final Map<ConfigurationType, ConfigurationProcessor> processorMap = new HashMap<>();

    /**
     * 初始化方法，自动加载所有配置
     */
    @PostConstruct
    public void initialize() {
        // 初始化处理器映射
        initializeProcessorMap();
        // 加载所有配置
        loadAllConfigurations();
        log.info("配置数据工厂初始化完成，已加载配置类型数: {}", configurationCache.size());
    }

    /**
     * 获取完整的配置数据中心对象
     */
    public ConfigurationDataCenter getConfigurationDataCenter(ConfigurationType type) {
        return configurationCache.get(type);
    }

    /**
     * 获取指定类型的所有配置
     */
    public List<ConfigurationDataCenter.NacosProperty> getConfigurations(ConfigurationType type) {
        ConfigurationDataCenter dataCenter = configurationCache.get(type);
        if (dataCenter == null) {
            return Collections.emptyList();
        }
        return dataCenter.getNacosProperties();
    }

    /**
     * 获取指定类型和环境的配置
     */
    public List<ConfigurationDataCenter.NacosProperty> getConfigurations(ConfigurationType type, String environment) {
        List<ConfigurationDataCenter.NacosProperty> allConfigs = getConfigurations(type);
        List<String> sheetNames = allConfigs.stream().map(ConfigurationDataCenter.NacosProperty::getSheetName).distinct().collect(Collectors.toList());
        // 必须完全匹配。模糊匹配可能导致错误
        if (sheetNames.contains(environment)){
            // 说明该配置文件的sheetName有环境
            return allConfigs.stream()
                    .filter(config -> environment == null || environment.equals(config.getEnvironment()))
                    .filter(config -> Objects.equals(config.getSheetName(), environment))
                    .collect(Collectors.toList());
        }
        // 按环境过滤
        return allConfigs.stream()
                .filter(config -> environment == null || environment.equals(config.getEnvironment()))
                .collect(Collectors.toList());
    }

    /**
     * 获取所有Nacos配置
     */
    public List<ConfigurationDataCenter.NacosProperty> getAllNacosConfigurations() {
        return getConfigurations(NACOS);
    }

    /**
     * 获取指定环境的URL映射配置
     */
    public List<ConfigurationDataCenter.NacosProperty> getUrlMappings(String environment) {
        return getConfigurations(URL_MAPPING, environment);
    }

    /**
     * 获取所有URL映射配置（所有环境）
     */
    public List<ConfigurationDataCenter.NacosProperty> getAllUrlMappings() {
        return getConfigurations(URL_MAPPING);
    }


    /**
     * 获取指定类型的支持环境列表
     */
    public Set<String> getSupportedEnvironments(ConfigurationType type) {
        List<ConfigurationDataCenter.NacosProperty> configs = getConfigurations(type);
        return configs.stream()
                .map(ConfigurationDataCenter.NacosProperty::getEnvironment)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 刷新所有配置
     */
    public void refreshAll() {
        log.info("开始刷新所有配置");
        configurationCache.clear();
        loadAllConfigurations();
        log.info("所有配置刷新完成");
    }

    /**
     * 刷新指定类型的配置
     */
    public void refresh(ConfigurationType type) {
        log.info("开始刷新配置类型: {}", type);
        configurationCache.remove(type);
        loadConfiguration(type);
        log.info("配置类型 {} 刷新完成", type);
    }

    /**
     * 获取配置统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();

        // 总配置数
        int totalConfigs = 0;
        Map<String, Integer> typeStats = new HashMap<>();
        Map<String, Integer> envStats = new HashMap<>();

        for (Map.Entry<ConfigurationType, ConfigurationDataCenter> entry : configurationCache.entrySet()) {
            ConfigurationType type = entry.getKey();
            List<ConfigurationDataCenter.NacosProperty> configs = getConfigurations(type);

            int configCount = configs.size();
            totalConfigs += configCount;
            typeStats.put(type.getCode(), configCount);

            // 按环境统计
            configs.stream()
                    .map(ConfigurationDataCenter.NacosProperty::getEnvironment)
                    .filter(Objects::nonNull)
                    .forEach(env -> envStats.merge(env, 1, Integer::sum));
        }

        stats.put("totalConfigurations", totalConfigs);
        stats.put("configurationsByType", typeStats);
        stats.put("configurationsByEnvironment", envStats);

        return stats;
    }

    // ==================== 私有方法 ====================

    /**
     * 初始化处理器映射
     */
    private void initializeProcessorMap() {
        for (ConfigurationProcessor processor : processors) {
            processorMap.put(processor.getSupportedType(), processor);
            log.debug("注册配置处理器: {} -> {}", processor.getSupportedType(), processor.getClass().getSimpleName());
        }
        log.info("已注册配置处理器数量: {}", processorMap.size());
    }

    /**
     * 加载所有配置
     */
    private void loadAllConfigurations() {
        // 遍历所有配置文件
        for (Map.Entry<String, ExcelConfigProperties.FileConfig> entry : configProperties.getFiles().entrySet()) {
            String configKey = entry.getKey();
            ExcelConfigProperties.FileConfig fileConfig = entry.getValue();
            if (!fileConfig.isEnabled()) {
                log.debug("配置文件 {} 未启用，跳过加载", configKey);
                continue;
            }
            try {
                ConfigurationType type = ConfigurationType.fromCode(configKey);
                List<String> supportedEnvironments = fileConfig.getSupportedEnvironments();
                for (String supportedEnvironment : supportedEnvironments) {
                    loadConfiguration(supportedEnvironment, type, fileConfig);
                }
            } catch (Exception e) {
                log.error("加载配置失败: {}", configKey, e);
            }
        }
    }

    /**
     * 加载指定类型的配置
     */
    private void loadConfiguration(ConfigurationType type) {
        Optional<ExcelConfigProperties.FileConfig> fileConfigOpt =
                configProperties.getFileConfig(type.getCode());
        if (fileConfigOpt.isPresent()) {
            ExcelConfigProperties.FileConfig fileConfig = fileConfigOpt.get();
            List<String> supportedEnvironments = fileConfig.getSupportedEnvironments();
            for (String supportedEnvironment : supportedEnvironments) {
                loadConfiguration(supportedEnvironment, type, fileConfigOpt.get());
            }
        } else {
            log.warn("未找到配置类型 {} 的文件配置", type);
        }
    }

    /**
     * 加载指定类型和文件配置的配置
     */
    private void loadConfiguration(String supportedEnvironment, ConfigurationType type, ExcelConfigProperties.FileConfig fileConfig) {
        ConfigurationProcessor processor = processorMap.get(type);
        if (processor == null) {
            log.warn("未找到配置类型 {} 的处理器", type);
            return;
        }
        if (!processor.validate(fileConfig)) {
            log.warn("配置文件 {} 验证失败", fileConfig.getPath());
            return;
        }
        try {
            log.info("开始加载配置类型: {}, 文件: {}", type, fileConfig.getPath());

            ProcessingContext processingContext = this.initializeProcessing(fileConfig, supportedEnvironment);
            for (LoadingEnvAware loadingEnvAware : loadingEnvAwares) {
                loadingEnvAware.beforeLoading(type, supportedEnvironment, fileConfig, processingContext);
            }
            // 一次性处理配置数据和元数据，避免重复读取文件
            ProcessingResult result = processor.processWithMetadata(fileConfig, processingContext);
            for (LoadingEnvAware loadingEnvAware : loadingEnvAwares) {
                loadingEnvAware.afterLoading(type, supportedEnvironment, fileConfig, result);
            }

            if (!result.isSuccess()) {
                log.error("处理配置失败: {}", result.getErrorMessage());
                return;
            }

            List<ConfigurationDataCenter.NacosProperty> allConfigs = result.getConfigurations();
            ConfigurationMetadata metadata = result.getMetadata();

            // 根据配置类型构建 ConfigurationDataCenter
            ConfigurationDataCenter.ConfigurationDataCenterBuilder builder = ConfigurationDataCenter.builder();

            // 设置元数据映射
            Map<ConfigurationType, ConfigurationMetadata> metadataMap = new HashMap<>();
            metadataMap.put(type, metadata);
            builder.metadataMap(metadataMap);
            builder.nacosProperties(allConfigs);
            ConfigurationDataCenter dataCenter = builder.build();
            ConfigurationDataCenter orDefault = configurationCache.getOrDefault(type, dataCenter);
            Set<ConfigurationDataCenter.NacosProperty> nacosProperties = Sets.newHashSet();
            nacosProperties.addAll(orDefault.getNacosProperties());
            nacosProperties.addAll(dataCenter.getNacosProperties());
            dataCenter.setNacosProperties(new ArrayList<>(nacosProperties));
            configurationCache.put(type, dataCenter);
            // 按环境分组统计
            Map<String, List<ConfigurationDataCenter.NacosProperty>> envConfigs = allConfigs.stream()
                    .collect(Collectors.groupingBy(ConfigurationDataCenter.NacosProperty::getEnvironment));

            log.info("成功加载配置类型: {}, 总数: {}, 环境数: {}, 处理耗时: {}ms",
                    type, allConfigs.size(), envConfigs.size(),
                    metadata.getProcessingDuration().toMillis());

        } catch (Exception e) {
            log.error("加载配置失败: 类型={}, 文件={}", type, fileConfig.getPath(), e);
        }
    }

    /**
     * 初始化处理上下文
     */
    private ProcessingContext initializeProcessing(ExcelConfigProperties.FileConfig fileConfig, String environment) {
        return new ProcessingContext(System.currentTimeMillis(), fileConfig.getSheets().size(), environment);
    }
} 