package com.just.git.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.eclipse.jgit.api.Git;

import java.io.IOException;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Git仓库封装
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GitRepository {
    
    /**
     * JGit实例
     */
    @JsonIgnore
    private Git git;
    
    /**
     * 本地仓库路径
     */
    private Path localPath;
    
    /**
     * 远程仓库URL
     */
    private String repositoryUrl;
    
    /**
     * 当前分支名称
     */
    private String currentBranch;
    
    /**
     * 是否为干净状态（无未提交修改）
     */
    private boolean isClean;
    
    /**
     * 仓库状态
     */
    private RepositoryStatus status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdatedAt;
    
    /**
     * Git认证信息
     */
    private GitCredentials credentials;
    
    /**
     * 仓库状态枚举
     */
    public enum RepositoryStatus {
        CLEAN,          // 干净状态
        MODIFIED,       // 有修改
        STAGED,         // 有暂存
        CONFLICTED,     // 有冲突
        DETACHED_HEAD,  // 分离HEAD
        UNKNOWN         // 未知状态
    }
    
    /**
     * 创建Git仓库实例
     */
    public static GitRepository of(Git git, Path localPath, String repositoryUrl) {
        return GitRepository.builder()
                .git(git)
                .localPath(localPath)
                .repositoryUrl(repositoryUrl)
                .createdAt(LocalDateTime.now())
                .lastUpdatedAt(LocalDateTime.now())
                .build();
    }
    
    /**
     * 获取当前分支名称
     */
    public String getCurrentBranch() {
        if (git != null) {
            try {
                return git.getRepository().getBranch();
            } catch (IOException e) {
                // 忽略异常，返回缓存的分支名
            }
        }
        return currentBranch;
    }
    
    /**
     * 更新最后更新时间
     */
    public void updateLastModified() {
        this.lastUpdatedAt = LocalDateTime.now();
    }
    
    /**
     * 检查Git实例是否有效
     */
    public boolean isValid() {
        return git != null && git.getRepository() != null;
    }
    
    /**
     * 安全关闭Git仓库
     */
    public void close() {
        if (git != null) {
            git.close();
        }
    }
    
    /**
     * 获取仓库根目录
     */
    public Path getWorkingDirectory() {
        if (git != null && git.getRepository() != null) {
            return git.getRepository().getWorkTree().toPath();
        }
        return localPath;
    }
    
    /**
     * 获取.git目录
     */
    public Path getGitDirectory() {
        if (git != null && git.getRepository() != null) {
            return git.getRepository().getDirectory().toPath();
        }
        return localPath != null ? localPath.resolve(".git") : null;
    }
    
    /**
     * 验证仓库有效性
     */
    public List<String> validate() {
        List<String> issues = new ArrayList<>();
        
        if (git == null) {
            issues.add("Git实例为空");
        }
        
        if (localPath == null) {
            issues.add("本地路径为空");
        }
        
        if (repositoryUrl == null || repositoryUrl.trim().isEmpty()) {
            issues.add("仓库URL为空");
        }
        
        if (git != null && git.getRepository() == null) {
            issues.add("Git仓库实例无效");
        }
        
        return issues;
    }
    
    @Override
    public String toString() {
        return String.format("GitRepository{localPath=%s, repositoryUrl='%s', currentBranch='%s', status=%s}", 
                           localPath, repositoryUrl, currentBranch, status);
    }
} 