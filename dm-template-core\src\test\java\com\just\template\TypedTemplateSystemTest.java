package com.just.template;

import com.just.template.core.TypedTemplateContext;
import com.just.template.exception.TemplateException;
import com.just.template.factory.TypedTemplateFactory;
import com.just.template.model.ApplicationNacosConfigModel;
import com.just.template.model.BootstrapConfigModel;
import com.just.template.model.PomConfigModel;

/**
 * 类型安全模板系统测试
 * 验证新设计的所有功能
 *
 * <AUTHOR>
 * @since 2.0.0
 */
public class TypedTemplateSystemTest {

    public static void main(String[] args) {
        System.out.println("🧪 开始类型安全模板系统测试\n");

        try {
            testBootstrapConfiguration();
            testApplicationNacosConfiguration();
            testPomConfiguration();

            System.out.println("\n🎉 所有测试通过！");
            printSuccessSummary();
        } catch (Exception e) {
            System.err.println("❌ 测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试Bootstrap配置
     */
    private static void testBootstrapConfiguration() throws TemplateException {
        System.out.println("=== 1. Bootstrap配置测试 ===");
        TypedTemplateFactory factory = new TypedTemplateFactory();
        // 测试基本配置
        BootstrapConfigModel config = BootstrapConfigModel.builder()
                .serviceName("user-service")
                .nacosServerAddr("${NACOS_ADDR}")
                .nacosNamespace("${NACOS_NAMESPACE}")
                .nacosAccessKey("${NACOS_ACCESS_KEY}")
                .nacosSecretKey("${NACOS_SECRET_KEY}")
                .enableDiscovery(false)
                .build();

        System.out.println("✅ 基本配置创建成功：" + config.getServiceName());

        // 测试扩展配置
        config.addExtensionConfig("shared-config.properties", "DEFAULT_GROUP", true);
        config.addExtensionConfig("database.properties", "DB_GROUP", false);

        System.out.println("✅ 扩展配置添加成功：" + config.getExtensionConfigs().size() + " 个配置");
        // 创建类型安全上下文
        String str = factory.generateBootstrap(config);
        System.out.println("✅ TypedTemplateContext创建成功：" + str);

        System.out.println("=== Bootstrap配置测试完成 ===\n");
    }

    /**
     * 测试Application Nacos配置
     */
    private static void testApplicationNacosConfiguration() throws TemplateException {
        System.out.println("=== 2. Application Nacos配置测试 ===");
        TypedTemplateFactory factory = new TypedTemplateFactory();
        // 测试基本配置
        ApplicationNacosConfigModel config = ApplicationNacosConfigModel.builder()
                .content("1234")
                .build();
        // 创建类型安全上下文
        String s = factory.generateApplicationNacos(config);
        System.out.println("✅ TypedTemplateContext创建成功：" + s);

        System.out.println("=== Application Nacos配置测试完成 ===\n");
    }

    /**
     * 测试Application Nacos配置
     */
    private static void testPomConfiguration() throws TemplateException {
        System.out.println("=== 2. Application Nacos配置测试 ===");
        TypedTemplateFactory factory = new TypedTemplateFactory();
        // 测试基本配置
        PomConfigModel config = PomConfigModel.builder()
                .version("1.0.5")
                .register(false)
                .build();
        // 创建类型安全上下文
        String s = factory.generatePomParentDependency(config);
        String s1 = factory.generateNacosPomDependency(config);

        System.out.println("✅ TypedTemplateContext创建成功：" + s);
        System.out.println("✅ TypedTemplateContext创建成功：" + s1);

        System.out.println("=== testPomConfiguration Nacos配置测试完成 ===\n");
    }



    /**
     * 测试参数验证
     */
    private static void testParameterValidation() {
        System.out.println("=== 4. 参数验证测试 ===");

        // 测试缺少必需参数的情况
        try {
            BootstrapConfigModel.builder()
                    .serviceName("test-service")
                    // .nacosServerAddr("...") // 缺少必需参数
                    .build();
            System.out.println("❌ 验证失败：应该抛出异常");
        } catch (IllegalArgumentException e) {
            System.out.println("✅ 必需参数验证正常：" + e.getMessage());
        }

        // 测试无效值验证
        try {
            ApplicationNacosConfigModel config = ApplicationNacosConfigModel.builder()
                    .content("test-service").build(); // 无效值
            config.validate();
            System.out.println("❌ 值验证失败：应该抛出异常");
        } catch (IllegalArgumentException e) {
            System.out.println("✅ 值验证正常：" + e.getMessage());
        }

        // 测试有效配置
        try {
            BootstrapConfigModel validConfig = BootstrapConfigModel.builder()
                    .serviceName("valid-service")
                    .nacosServerAddr("127.0.0.1:8848")
                    .nacosNamespace("dev")
                    .build();
            System.out.println("✅ 有效配置验证通过：" + validConfig.getServiceName());
        } catch (Exception e) {
            System.out.println("❌ 意外的验证失败：" + e.getMessage());
        }

        System.out.println("=== 参数验证测试完成 ===\n");
    }

    /**
     * 打印成功总结
     */
    private static void printSuccessSummary() {
        System.out.println("📊 测试总结：");
        System.out.println("✅ Bootstrap配置模型：类型安全、IDE友好");
        System.out.println("✅ Application配置模型：便捷方法、语义清晰");
        System.out.println("✅ TypedTemplateContext：自动映射、类型安全");
        System.out.println("✅ 构建器模式：链式调用、自动验证");
        System.out.println("✅ 参数验证：编译时检查、运行时保护");
        System.out.println("✅ 模板工厂：类型检查、参数说明");

        System.out.println("\n🔄 对比旧设计：");
        System.out.println("📈 参数明确性：从❌不知道参数名 -> ✅IDE自动补全");
        System.out.println("📈 类型安全：从❌运行时错误 -> ✅编译时检查");
        System.out.println("📈 开发体验：从❌手动查文档 -> ✅自动提示参数");
        System.out.println("📈 错误发现：从❌模板渲染时 -> ✅配置构建时");

        System.out.println("\n🚀 新设计优势：");
        System.out.println("💡 再也不用深入模板内部查看参数了！");
        System.out.println("💡 IDE会自动提示所有可用的配置方法！");
        System.out.println("💡 编译时就能发现参数类型错误！");
        System.out.println("💡 构建器模式让配置代码更清晰易读！");
    }
} 