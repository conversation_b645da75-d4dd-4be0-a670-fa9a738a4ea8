# DM Nacos Core 模块

## 概述

`dm-nacos-core` 是项目的Nacos配置管理核心模块，提供完整的配置中心集成和管理功能。

## 主要功能

### 🔧 配置管理
- 配置的增删改查操作
- 多环境配置支持
- 配置格式验证和格式化
- 配置监听和变更通知
- 配置备份和恢复

### 🏷️ 属性管理
- 从Excel文件读取配置属性
- 属性类型验证和转换
- 敏感属性加密/解密
- 属性搜索和过滤
- 批量属性处理

### 🌍 环境管理
- 多环境配置管理
- 环境状态监控
- 环境配置同步
- 环境变量管理
- 环境连接测试

### 📝 模板功能
- 基于模板生成配置
- 自定义模板创建和管理
- 模板变量验证
- 多环境模板配置
- 模板使用统计

## 核心组件

### 配置属性类
```java
@ConfigurationProperties(prefix = "dm.nacos")
public class NacosCoreProperties {
    private String serverAddr = "127.0.0.1:8848";
    private String namespace = "public";
    // ... 更多配置
}
```

### 数据模型
- `NacosConfig`: 配置信息模型
- `NacosProperty`: 配置属性模型  
- `NacosEnvironment`: 环境配置模型

### 核心服务
- `NacosConfigService`: 配置管理服务
- `NacosPropertyService`: 属性管理服务
- `NacosEnvironmentService`: 环境管理服务
- `NacosTemplateService`: 模板服务

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>com.just</groupId>
    <artifactId>dm-nacos-core</artifactId>
    <version>2.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 配置文件

```yaml
dm:
  nacos:
    enabled: true
    server-addr: 127.0.0.1:8848
    namespace: public
    username: nacos
    password: nacos
    default-group: DEFAULT_GROUP
    
    # 环境配置
    environments:
      dev:
        name: dev
        description: 开发环境
        namespace: dev-namespace
        enabled: true
      test:
        name: test
        description: 测试环境
        namespace: test-namespace
        enabled: true
```

### 3. 使用示例

#### 配置管理
```java
@Autowired
private NacosConfigService configService;

// 发布配置
NacosConfig config = NacosConfig.builder()
    .dataId("application.properties")
    .group("DEFAULT_GROUP")
    .content("server.port=8080")
    .type("properties")
    .build();

configService.publishConfig(config);

// 获取配置
Optional<NacosConfig> result = configService.getConfig("application.properties", "DEFAULT_GROUP");
```

#### 属性管理
```java
@Autowired
private NacosPropertyService propertyService;

// 解析配置内容
List<NacosProperty> properties = propertyService.parseProperties(
    "server.port=8080\napp.name=demo", 
    "properties", 
    "dev"
);

// 转换为配置内容
String content = propertyService.convertToConfigContent(properties, "yaml");
```

#### 环境管理
```java
@Autowired
private NacosEnvironmentService environmentService;

// 创建环境
NacosEnvironment env = NacosEnvironment.builder()
    .name("staging")
    .displayName("预发布环境")
    .namespace("staging-ns")
    .type(EnvironmentType.STAGING)
    .enabled(true)
    .build();

environmentService.createEnvironment(env);

// 测试环境连接
String result = environmentService.testEnvironmentConnection("staging");
```

#### 模板功能
```java
@Autowired
private NacosTemplateService templateService;

// 创建自定义模板
templateService.createCustomTemplate(
    "app-template",
    "server.port=${port}\nspring.application.name=${appName}",
    "应用配置模板"
);

// 使用模板生成配置
Map<String, Object> variables = Map.of(
    "port", "8080",
    "appName", "my-app"
);

NacosConfig config = templateService.generateConfigFromTemplate(
    "app-template", 
    variables, 
    "dev"
);
```

## 集成其他模块

### 与文件工具模块集成
```java
// 从Excel读取配置
List<NacosProperty> properties = propertyService.processPropertiesFromExcel(
    "config.xlsx", 
    "prod"
);
```

### 与模板核心模块集成
```java
// 使用FreeMarker模板生成配置
NacosConfig config = templateService.generateConfigFromTemplate(
    "bootstrap-template",
    variables,
    "prod"
);
```

### 与Git核心模块集成
```java
// 配置版本控制和备份将在后续版本中实现
```

## 配置选项

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `dm.nacos.enabled` | `true` | 是否启用Nacos模块 |
| `dm.nacos.server-addr` | `127.0.0.1:8848` | Nacos服务器地址 |
| `dm.nacos.namespace` | `public` | 默认命名空间 |
| `dm.nacos.username` | `nacos` | 用户名 |
| `dm.nacos.password` | `nacos` | 密码 |
| `dm.nacos.default-group` | `DEFAULT_GROUP` | 默认配置组 |
| `dm.nacos.connection-timeout` | `3000` | 连接超时时间(ms) |
| `dm.nacos.enable-cache` | `true` | 是否启用缓存 |
| `dm.nacos.enable-backup` | `true` | 是否启用备份 |

## 异常处理

模块定义了专门的异常类：

- `NacosException`: 基础异常类
- `NacosConfigException`: 配置操作异常  
- `NacosEnvironmentException`: 环境操作异常

## 测试

运行测试：
```bash
mvn test -Dtest=NacosCoreBasicTest
```

## 扩展功能

### 自定义配置验证器
```java
@Component
public class CustomConfigValidator implements ConfigValidator {
    @Override
    public String validate(NacosConfig config) {
        // 自定义验证逻辑
        return "验证通过";
    }
}
```

### 自定义模板引擎
```java
@Component
public class CustomTemplateEngine implements TemplateEngine {
    @Override
    public String process(String templateName, Map<String, Object> variables) {
        // 自定义模板处理逻辑
        return processedContent;
    }
}
```

## 注意事项

1. **Nacos服务器**: 需要启动Nacos服务器才能使用完整功能
2. **命名空间**: 确保配置的命名空间在Nacos中存在
3. **权限**: 确保提供的用户名密码有足够权限
4. **网络**: 确保应用能够访问Nacos服务器地址
5. **依赖**: 某些功能需要其他模块支持（如Excel读取需要dm-file-utils）

## 版本兼容性

- Java 8+
- Spring Boot 2.x
- Nacos 2.x
- Maven 3.6+

## 未来计划

- [ ] 配置加密/解密功能增强
- [ ] 配置审计和变更历史
- [ ] 更多内置模板
- [ ] 配置性能监控
- [ ] 配置自动同步机制
- [ ] Web管理界面集成

## 许可证

本项目采用 MIT 许可证。