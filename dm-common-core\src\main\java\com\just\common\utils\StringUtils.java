package com.just.common.utils;

import org.apache.commons.lang3.RandomStringUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 字符串工具类
 * 对Apache Commons Lang3 StringUtils的增强版
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
public final class StringUtils extends org.apache.commons.lang3.StringUtils {
    
    private StringUtils() {
        // 私有构造函数，防止实例化
    }
    
    // ======================== 常用正则表达式 ========================
    
    /**
     * 邮箱格式正则表达式
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    
    /**
     * 手机号格式正则表达式（中国大陆）
     */
    private static final Pattern PHONE_PATTERN = Pattern.compile(
            "^1[3-9]\\d{9}$");
    
    /**
     * IPv4地址格式正则表达式
     */
    private static final Pattern IPV4_PATTERN = Pattern.compile(
            "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");
    
    /**
     * URL格式正则表达式
     */
    private static final Pattern URL_PATTERN = Pattern.compile(
            "^https?://[a-zA-Z0-9.-]+(?:\\.[a-zA-Z]{2,})+(?:/[^\\s]*)?$");
    
    /**
     * 环境变量占位符正则表达式
     */
    private static final Pattern ENV_VAR_PATTERN = Pattern.compile(
            "\\$\\{([^}]+)\\}");
    
    // ======================== 判断方法 ========================
    
    /**
     * 判断字符串是否为有效邮箱格式
     * 
     * @param email 邮箱字符串
     * @return 是否为有效邮箱
     */
    public static boolean isValidEmail(String email) {
        return isNotBlank(email) && EMAIL_PATTERN.matcher(email).matches();
    }
    
    /**
     * 判断字符串是否为有效手机号格式
     * 
     * @param phone 手机号字符串
     * @return 是否为有效手机号
     */
    public static boolean isValidPhone(String phone) {
        return isNotBlank(phone) && PHONE_PATTERN.matcher(phone).matches();
    }
    
    /**
     * 判断字符串是否为有效IPv4地址格式
     * 
     * @param ip IP地址字符串
     * @return 是否为有效IPv4地址
     */
    public static boolean isValidIPv4(String ip) {
        return isNotBlank(ip) && IPV4_PATTERN.matcher(ip).matches();
    }
    
    /**
     * 判断字符串是否为有效URL格式
     * 
     * @param url URL字符串
     * @return 是否为有效URL
     */
    public static boolean isValidUrl(String url) {
        return isNotBlank(url) && URL_PATTERN.matcher(url).matches();
    }
    
    /**
     * 判断字符串是否包含中文字符
     * 
     * @param str 字符串
     * @return 是否包含中文
     */
    public static boolean containsChinese(String str) {
        if (isEmpty(str)) {
            return false;
        }
        return str.matches(".*[\\u4e00-\\u9fa5].*");
    }
    
    /**
     * 判断字符串是否为纯数字
     * 
     * @param str 字符串
     * @return 是否为纯数字
     */
    public static boolean isPureNumber(String str) {
        return isNotBlank(str) && str.matches("\\d+");
    }
    
    /**
     * 判断字符串是否为有效的Java标识符
     * 
     * @param str 字符串
     * @return 是否为有效标识符
     */
    public static boolean isValidJavaIdentifier(String str) {
        if (isEmpty(str)) {
            return false;
        }
        
        if (!Character.isJavaIdentifierStart(str.charAt(0))) {
            return false;
        }
        
        for (int i = 1; i < str.length(); i++) {
            if (!Character.isJavaIdentifierPart(str.charAt(i))) {
                return false;
            }
        }
        
        return true;
    }
    
    // ======================== 转换方法 ========================
    
    /**
     * 将字符串转换为驼峰命名法
     * 
     * @param str 字符串
     * @param delimiter 分隔符
     * @return 驼峰命名的字符串
     */
    public static String toCamelCase(String str, String delimiter) {
        if (isEmpty(str)) {
            return str;
        }
        
        String[] parts = str.split(Pattern.quote(delimiter));
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < parts.length; i++) {
            String part = parts[i];
            if (isEmpty(part)) {
                continue;
            }
            
            if (i == 0) {
                result.append(part.toLowerCase());
            } else {
                result.append(capitalize(part.toLowerCase()));
            }
        }
        
        return result.toString();
    }
    
    /**
     * 将驼峰命名转换为下划线命名
     * 
     * @param str 驼峰命名字符串
     * @return 下划线命名字符串
     */
    public static String camelToUnderscore(String str) {
        if (isEmpty(str)) {
            return str;
        }
        
        return str.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }
    
    /**
     * 将下划线命名转换为驼峰命名
     * 
     * @param str 下划线命名字符串
     * @return 驼峰命名字符串
     */
    public static String underscoreToCamel(String str) {
        return toCamelCase(str, "_");
    }
    
    /**
     * 将字符串转换为Pascal命名法（首字母大写的驼峰）
     * 
     * @param str 字符串
     * @param delimiter 分隔符
     * @return Pascal命名的字符串
     */
    public static String toPascalCase(String str, String delimiter) {
        String camelCase = toCamelCase(str, delimiter);
        return isEmpty(camelCase) ? camelCase : capitalize(camelCase);
    }
    
    /**
     * 将字符串转换为kebab命名法（短横线分隔）
     * 
     * @param str 字符串
     * @return kebab命名的字符串
     */
    public static String toKebabCase(String str) {
        if (isEmpty(str)) {
            return str;
        }
        
        return str.replaceAll("([a-z])([A-Z])", "$1-$2")
                  .replaceAll("_", "-")
                  .toLowerCase();
    }
    
    // ======================== 格式化方法 ========================
    
    /**
     * 格式化字节大小
     * 
     * @param bytes 字节数
     * @return 格式化的大小字符串
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        }
        
        String[] units = {"KB", "MB", "GB", "TB", "PB"};
        int unitIndex = 0;
        double size = bytes;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", size, units[unitIndex]);
    }
    
    /**
     * 格式化时间长度
     * 
     * @param milliseconds 毫秒数
     * @return 格式化的时间字符串
     */
    public static String formatDuration(long milliseconds) {
        if (milliseconds < 1000) {
            return milliseconds + "ms";
        }
        
        long seconds = milliseconds / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return String.format("%dd %dh %dm %ds", days, hours % 24, minutes % 60, seconds % 60);
        } else if (hours > 0) {
            return String.format("%dh %dm %ds", hours, minutes % 60, seconds % 60);
        } else if (minutes > 0) {
            return String.format("%dm %ds", minutes, seconds % 60);
        } else {
            return seconds + "s";
        }
    }
    
    /**
     * 掩码敏感信息
     * 
     * @param str 原始字符串
     * @param start 开始位置
     * @param end 结束位置
     * @param mask 掩码字符
     * @return 掩码后的字符串
     */
    public static String mask(String str, int start, int end, char mask) {
        if (isEmpty(str) || start < 0 || end > str.length() || start >= end) {
            return str;
        }
        
        StringBuilder sb = new StringBuilder(str);
        for (int i = start; i < end; i++) {
            sb.setCharAt(i, mask);
        }
        return sb.toString();
    }
    
    /**
     * 掩码手机号中间4位
     * 
     * @param phone 手机号
     * @return 掩码后的手机号
     */
    public static String maskPhone(String phone) {
        if (isEmpty(phone) || phone.length() != 11) {
            return phone;
        }
        return mask(phone, 3, 7, '*');
    }
    
    /**
     * 掩码邮箱用户名部分
     * 
     * @param email 邮箱
     * @return 掩码后的邮箱
     */
    public static String maskEmail(String email) {
        if (isEmpty(email) || !email.contains("@")) {
            return email;
        }
        
        int atIndex = email.indexOf("@");
        String username = email.substring(0, atIndex);
        String domain = email.substring(atIndex);
        
        if (username.length() <= 2) {
            return mask(username, 1, username.length(), '*') + domain;
        } else {
            return mask(username, 1, username.length() - 1, '*') + domain;
        }
    }
    
    // ======================== 集合操作方法 ========================
    
    /**
     * 将字符串数组连接为字符串
     * 
     * @param array 字符串数组
     * @param separator 分隔符
     * @return 连接后的字符串
     */
    public static String joinArray(String[] array, String separator) {
        if (array == null || array.length == 0) {
            return "";
        }
        return String.join(separator, array);
    }
    
    /**
     * 将集合连接为字符串
     * 
     * @param collection 集合
     * @param separator 分隔符
     * @return 连接后的字符串
     */
    public static String joinCollection(Collection<String> collection, String separator) {
        if (collection == null || collection.isEmpty()) {
            return "";
        }
        return String.join(separator, collection);
    }
    
    /**
     * 根据分隔符分割字符串为List（去除空白元素）
     * 
     * @param str 字符串
     * @param separator 分隔符
     * @return 分割后的List
     */
    public static List<String> splitToList(String str, String separator) {
        if (isEmpty(str)) {
            return new ArrayList<>();
        }
        
        return Arrays.stream(str.split(Pattern.quote(separator)))
                     .map(String::trim)
                     .filter(s -> !s.isEmpty())
                     .collect(Collectors.toList());
    }
    
    /**
     * 根据分隔符分割字符串为Set（去除空白元素和重复元素）
     * 
     * @param str 字符串
     * @param separator 分隔符
     * @return 分割后的Set
     */
    public static Set<String> splitToSet(String str, String separator) {
        if (isEmpty(str)) {
            return new HashSet<>();
        }
        
        return Arrays.stream(str.split(Pattern.quote(separator)))
                     .map(String::trim)
                     .filter(s -> !s.isEmpty())
                     .collect(Collectors.toSet());
    }
    
    // ======================== 环境变量处理方法 ========================
    
    /**
     * 提取字符串中的环境变量占位符
     * 
     * @param str 包含环境变量的字符串
     * @return 环境变量名称列表
     */
    public static List<String> extractEnvironmentVariables(String str) {
        if (isEmpty(str)) {
            return new ArrayList<>();
        }
        
        List<String> variables = new ArrayList<>();
        java.util.regex.Matcher matcher = ENV_VAR_PATTERN.matcher(str);
        
        while (matcher.find()) {
            variables.add(matcher.group(1));
        }
        
        return variables;
    }
    
    /**
     * 替换字符串中的环境变量占位符
     * 
     * @param str 包含环境变量的字符串
     * @param variables 环境变量映射
     * @return 替换后的字符串
     */
    public static String replaceEnvironmentVariables(String str, Map<String, String> variables) {
        if (isEmpty(str) || variables == null || variables.isEmpty()) {
            return str;
        }
        
        String result = str;
        for (Map.Entry<String, String> entry : variables.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            String value = entry.getValue();
            if (value != null) {
                result = result.replace(placeholder, value);
            }
        }
        
        return result;
    }
    
    // ======================== 随机字符串生成方法 ========================
    
    /**
     * 生成随机字符串（包含字母和数字）
     * 
     * @param length 长度
     * @return 随机字符串
     */
    public static String randomAlphanumeric(int length) {
        return RandomStringUtils.randomAlphanumeric(length);
    }
    
    /**
     * 生成随机数字字符串
     * 
     * @param length 长度
     * @return 随机数字字符串
     */
    public static String randomNumeric(int length) {
        return RandomStringUtils.randomNumeric(length);
    }
    
    /**
     * 生成随机字母字符串
     * 
     * @param length 长度
     * @return 随机字母字符串
     */
    public static String randomAlphabetic(int length) {
        return RandomStringUtils.randomAlphabetic(length);
    }
    
    /**
     * 生成UUID字符串（无连字符）
     * 
     * @return UUID字符串
     */
    public static String randomUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 生成短UUID字符串（8位）
     * 
     * @return 短UUID字符串
     */
    public static String shortUUID() {
        return randomUUID().substring(0, 8);
    }
}