#spring.profiles.active=dev
server.servlet.context-path=/onshore-bond-abs
# druid
spring.datasource.druid.stat-view-servlet.enabled=true
spring.datasource.druid.stat-view-servlet.allow=
spring.datasource.druid.filter.stat.enabled=true
spring.datasource.druid.web-stat-filter.enabled=true
# mysql
abs.datasource.url=jdbc:mysql://${PUBLIC_MYSQL_HOST_PRICE}:${PUBLIC_MYSQL_PORT_PRICE}/bond_abs?useUnicode\=true&characterEncoding\=UTF-8&zeroDateTimeBehavior\=convertToNull
abs.datasource.username=${PUBLIC_MYSQL_USER_PRICE}
abs.datasource.password=${PUBLIC_MYSQL_PASSWD_PRICE}
abs.datasource.driver-class-name=com.mysql.jdbc.Driver
abs.datasource.db-type=mysql
abs.datasource.use-global-data-source-stat=true
abs.datasource.pool-prepared-statements=true
abs.datasource.filters=stat,wall
abs.datasource.test-while-idle=true
abs.datasource.initial-size=2
abs.datasource.min-idle=2
abs.datasource.max-active=20
abs.datasource.time-between-eviction-runs-millis=60000
abs.datasource.druid.min-evictable-idle-time-millis=300000
abs.datasource.validation-query=SELECT 1
cnabs.datasource.url=jdbc:mysql://${PUBLIC_POLARDB_HOST_DATA}:${PUBLIC_POLARDB_PORT_DATA}/${POLARDB_PROFILES_ACTIVE:}cnabs?useUnicode\=true&characterEncoding\=UTF-8&zeroDateTimeBehavior\=convertToNull
cnabs.datasource.username=${PUBLIC_POLARDB_USER_DATA}
cnabs.datasource.password=${PUBLIC_POLARDB_PASSWORD_DATA}
cnabs.datasource.driver-class-name=com.mysql.jdbc.Driver
cnabs.datasource.db-type=mysql
cnabs.datasource.use-global-data-source-stat=true
cnabs.datasource.pool-prepared-statements=true
cnabs.datasource.filters=stat,wall
cnabs.datasource.test-while-idle=true
cnabs.datasource.initial-size=2
cnabs.datasource.min-idle=2
cnabs.datasource.max-active=20
cnabs.datasource.time-between-eviction-runs-millis=60000
cnabs.datasource.druid.min-evictable-idle-time-millis=300000
cnabs.datasource.validation-query=SELECT 1
spring.jackson.serialization.write-dates-as-timestamps=true
# mybatis
mapper.mappers[0]=com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper
mybatis.configuration.lazy-loading-enabled=false
mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis.configuration.map-underscore-to-camel-case=true
# file
spring.multipart.maxFileSize=100Mb
spring.multipart.maxRequestSize=1000Mb


bond.basic.api.url=${ONSHORE_BOND_BASIC_URL}
bond.rating.api.url=${ONSHORE_BOND_RATING_URL}
bond.price.api.url=${ONSHORE_BOND_PRICE_URL}
com.api.url=${ONSHORE_COM_SERVICE_URL}
onshore.management.api.url=${ONSHORE_MANAGEMENT_SERVICE_URL}


file.api.url=${ONSHORE_CG_FILE_SERVICE_URL}

logging.level.com.innodealing.onshore.abs.service.internal=debug
logging.level.com.innodealing.onshore.abs.mapper=debug

feign.httpclient.enabled=true

search.api.url=${INTERNATIONAL_SEARCH_SERVICE_URL}

#elasticsearch
es.url=${PUBLIC_ELASTICSEARCH_REST_URIS}

sentiment.api.url=${ONSHORE_SENTIMENT_SERVICE_URL}
# redis
spring.redis.host=${PUBLIC_CODIS_HOST}
spring.redis.port=${PUBLIC_CODIS_PORT}
spring.redis.password=${PUBLIC_CODIS_PASSWD}
spring.redis.database=${PUBLIC_CODIS_DATABASE}

spring.data.elasticsearch.cluster-name=${PUBLIC_ELASTICSEARCH_CLUSTER_NAME}
spring.data.elasticsearch.cluster-nodes=${PUBLIC_ELASTICSEARCH_HOST}:${PUBLIC_ELASTICSEARCH_TRANSPORT}


