package com.just.file.service;

import com.just.file.config.ExcelConfigProperties;
import com.just.file.model.ConfigurationType;
import com.just.file.model.ProcessingContext;
import com.just.file.model.ProcessingResult;
import org.springframework.beans.factory.Aware;

/**
 * 加载私有变量扩展接口
 * <AUTHOR>
 */
public interface LoadingEnvAware extends Aware {

    /**
     * 私有变量加载完成之前出发
     * @param type 配置类型
     * @param fileConfig 文件配置
     */
    void beforeLoading(ConfigurationType type,String environment, ExcelConfigProperties.FileConfig fileConfig, ProcessingContext processingContext);

    /**
     *  私有变量加载完成之后出发
     * @param type 配置类型
     * @param fileConfig 文件配置
     * @param result 处理结果
     */
    void afterLoading(ConfigurationType type,String environment, ExcelConfigProperties.FileConfig fileConfig, ProcessingResult result);

}
