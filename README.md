# Nacos Properties Tool

基于 Spring Shell 的 Nacos 配置管理工具，支持阿里云 MSE 微服务引擎。

## 功能特性

- 支持多环境配置管理 (dev/qa/uat/prd)
- 支持配置列表查询和展示
- 支持配置内容的获取和发布
- 支持属性文件的自动生成
- 支持配置差异比对
- 支持命令行交互式操作
- 支持命令自动补全
- 美化的命令输出格式

## 环境要求

- JDK 1.8+
- Maven 3.6+
- Spring Boot 2.7.5
- Spring Shell

## 快速开始

### 构建

```bash
mvn clean package -DskipTests
```

### 运行

```bash
java -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -jar target/nacos-property-1.0.jar
```

## 命令使用说明

所有命令都以 `nacos` 为前缀，支持以下操作：

### 1. 查看配置列表

```bash
nacos config list --env <环境> --namespace <命名空间>
```

参数说明：
- `--env`: 必填，环境名称 (dev/qa/uat/prd)
- `--namespace`: 可选，命名空间

示例：
```bash
nacos config list --env dev --namespace dev
```

### 2. 生成配置属性

```bash
nacos config upgrade --serviceName <服务名> --path <服务路径> --env <环境> [--refresh] [--publish]
```

参数说明：
- `--serviceName`: 必填，服务名称
- `--path`: 可选，服务路径(默认本地)
- `--env`: 必填，环境名称
- `--refresh`: 可选，是否自动刷新 (默认: false)
- `--publish`: 可选，是否推送到Nacos (默认: false)

示例：
```bash
nacos config upgrade --serviceName myapp --path /path/to/myapp --env dev --refresh true --publish true
```

### 3. 检查配置差异

```bash
nacos check --env <环境>
```

参数说明：
- `--env`: 必填，环境名称

示例：
```bash
nacos check --env dev
```

### 4. 查看环境列表

```bash
nacos env list
```

### 5. 获取配置内容

```bash
nacos config get --dataId <配置ID> --group <分组> --env <环境> [--namespace <命名空间>]
```

参数说明：
- `--dataId`: 必填，配置ID
- `--group`: 必填，配置分组
- `--env`: 必填，环境名称
- `--namespace`: 可选，命名空间
- `--accessKey`: 可选，访问密钥
- `--secretKey`: 可选，访问密钥Secret

示例：
```bash
nacos config get --dataId app.properties --group DEFAULT_GROUP --env dev
```

### 6. 发布配置

```bash
nacos publish-config --dataId <配置ID> --group <分组> --file <文件路径> --env <环境> [--namespace <命名空间>]
```

参数说明：
- `--dataId`: 必填，配置ID
- `--group`: 必填，配置分组
- `--file`: 必填，配置文件路径
- `--env`: 必填，环境名称
- `--namespace`: 可选，命名空间
- `--accessKey`: 可选，访问密钥
- `--secretKey`: 可选，访问密钥Secret

示例：
```bash
nacos publish-config --dataId app.properties --group DEFAULT_GROUP --file config.properties --env dev
```

## 配置说明

在 `application.properties` 中配置以下参数：

```properties
# Spring应用配置
spring.application.name=nacos-property
spring.main.banner-mode=console
spring.banner.charset=UTF-8

# Spring Shell配置
spring.shell.history.enabled=true
spring.shell.command.script.enabled=true
spring.shell.command.history.enabled=true
spring.shell.command.completion.enabled=true
spring.shell.interactive.enabled=true
spring.shell.interactive.autosuggestion=true

# Nacos默认配置
nacos.service.name=
nacos.service.path=
nacos.env=dev
nacos.refresh=true
nacos.publish=false
```

## 注意事项

1. 确保有正确的阿里云MSE访问权限
2. 敏感信息（如AccessKey）建议通过环境变量传入
3. 建议在使用publish命令前先通过config get验证配置
4. 配置文件修改后及时备份

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交改动 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情 