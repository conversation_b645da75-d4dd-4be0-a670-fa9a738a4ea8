package com.just.upgrade.config;

import com.just.file.config.ExcelConfigProperties;
import com.just.file.model.ConfigurationDataCenter;
import com.just.file.service.ExcelConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Excel配置预加载服务
 * 简化设计，在应用启动时预加载配置数据
 * 
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExcelConfigPreloadService implements CommandLineRunner {

    private final ExcelConfigService excelConfigService;
    private final ExcelConfigProperties configProperties;
    
    /**
     * 预加载状态缓存
     */
    private final Map<String, PreloadStatus> preloadStatusMap = new ConcurrentHashMap<>();
    
    /**
     * 预加载开始时间
     */
    private LocalDateTime preloadStartTime;
    
    /**
     * 预加载完成时间
     */
    private LocalDateTime preloadCompleteTime;

    @Override
    public void run(String... args) {
        log.info("=== Excel配置预加载服务启动 ===");
        preloadStartTime = LocalDateTime.now();
        // 异步预加载所有配置
        asyncPreloadAllConfigurations();
    }

    /**
     * 异步预加载所有配置数据
     */
    @Async("configPreloadExecutor")
    public CompletableFuture<Void> asyncPreloadAllConfigurations() {
        try {
            log.info("开始异步预加载所有Excel配置数据...");
            
            // 预加载Nacos配置
            CompletableFuture<Void> nacosPreload = preloadNacosConfigurations();
            
            // 预加载URL配置
            CompletableFuture<Void> urlPreload = preloadUrlConfigurations();
            
            // 等待所有预加载完成
            CompletableFuture<Void> allPreload = CompletableFuture.allOf(nacosPreload, urlPreload);
            
            allPreload.thenRun(() -> {
                preloadCompleteTime = LocalDateTime.now();
                logPreloadSummary();
            }).exceptionally(throwable -> {
                log.error("配置预加载过程中出现异常", throwable);
                return null;
            });
            
            return allPreload;
            
        } catch (Exception e) {
            log.error("配置预加载失败", e);
            throw new RuntimeException("配置预加载失败", e);
        }
    }

    /**
     * 预加载Nacos配置
     */
    @Async("configPreloadExecutor")
    public CompletableFuture<Void> preloadNacosConfigurations() {
        return CompletableFuture.runAsync(() -> {
            try {
                log.info("开始预加载Nacos配置...");
                
                List<ConfigurationDataCenter.NacosProperty> configs = excelConfigService.readAllNacosConfigs();
                
                PreloadStatus status = new PreloadStatus();
                status.setConfigType("nacos");
                status.setConfigKey("all");
                status.setPreloadTime(LocalDateTime.now());
                status.setSuccess(true);
                status.setDataSize(configs != null ? configs.size() : 0);
                
                preloadStatusMap.put("nacos_all", status);
                log.info("Nacos配置预加载成功，数据量: {}", status.getDataSize());
                
            } catch (Exception e) {
                log.error("预加载Nacos配置时发生异常", e);
                
                PreloadStatus status = new PreloadStatus();
                status.setConfigType("nacos");
                status.setConfigKey("all");
                status.setPreloadTime(LocalDateTime.now());
                status.setSuccess(false);
                status.setErrorMessage(e.getMessage());
                status.setDataSize(0);
                
                preloadStatusMap.put("nacos_all", status);
            }
        });
    }

    /**
     * 预加载URL配置
     */
    @Async("configPreloadExecutor")
    public CompletableFuture<Void> preloadUrlConfigurations() {
        return CompletableFuture.runAsync(() -> {
            try {
                log.info("开始预加载URL配置...");
                
                List<ConfigurationDataCenter.NacosProperty> allUrlConfigs = excelConfigService.readAllUrlMappings();
                
                PreloadStatus status = new PreloadStatus();
                status.setConfigType("url");
                status.setConfigKey("all");
                status.setPreloadTime(LocalDateTime.now());
                status.setSuccess(true);
                status.setDataSize(allUrlConfigs != null ? allUrlConfigs.size() : 0);
                
                preloadStatusMap.put("url_all", status);
                log.info("URL配置预加载成功，数据量: {}", status.getDataSize());
                
            } catch (Exception e) {
                log.error("预加载URL配置时发生异常", e);
                
                PreloadStatus status = new PreloadStatus();
                status.setConfigType("url");
                status.setConfigKey("all");
                status.setPreloadTime(LocalDateTime.now());
                status.setSuccess(false);
                status.setErrorMessage(e.getMessage());
                status.setDataSize(0);
                
                preloadStatusMap.put("url_all", status);
            }
        });
    }

    /**
     * 获取预加载状态报告
     */
    public PreloadReport getPreloadReport() {
        PreloadReport report = new PreloadReport();
        report.setStartTime(preloadStartTime);
        report.setCompleteTime(preloadCompleteTime);
        report.setStatusMap(new ConcurrentHashMap<>(preloadStatusMap));
        
        // 统计信息
        long totalConfigs = preloadStatusMap.size();
        long successConfigs = preloadStatusMap.values().stream()
            .filter(PreloadStatus::isSuccess)
            .count();
        long failureConfigs = totalConfigs - successConfigs;
        
        report.setTotalConfigs((int) totalConfigs);
        report.setSuccessConfigs((int) successConfigs);
        report.setFailureConfigs((int) failureConfigs);
        
        if (preloadStartTime != null && preloadCompleteTime != null) {
            report.setDurationSeconds(
                java.time.Duration.between(preloadStartTime, preloadCompleteTime).getSeconds());
        }
        
        return report;
    }

    /**
     * 记录预加载摘要信息
     */
    private void logPreloadSummary() {
        PreloadReport report = getPreloadReport();
        
        log.info("=== Excel配置预加载完成摘要 ===");
        log.info("预加载耗时: {}秒", report.getDurationSeconds());
        log.info("总配置数: {}", report.getTotalConfigs());
        log.info("成功配置数: {}", report.getSuccessConfigs());
        log.info("失败配置数: {}", report.getFailureConfigs());
        
        if (report.getFailureConfigs() > 0) {
            log.warn("预加载失败的配置:");
            preloadStatusMap.values().stream()
                .filter(status -> !status.isSuccess())
                .forEach(status -> 
                    log.warn("  - {}_{}: {}", status.getConfigType(), status.getConfigKey(), status.getErrorMessage()));
        }
        
        log.info("=== 预加载摘要结束 ===");
    }

    /**
     * 预加载状态
     */
    public static class PreloadStatus {
        private String configType;
        private String configKey;
        private boolean success;
        private String errorMessage;
        private int dataSize;
        private LocalDateTime preloadTime;

        // Getters and Setters
        public String getConfigType() { return configType; }
        public void setConfigType(String configType) { this.configType = configType; }
        
        public String getConfigKey() { return configKey; }
        public void setConfigKey(String configKey) { this.configKey = configKey; }
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public int getDataSize() { return dataSize; }
        public void setDataSize(int dataSize) { this.dataSize = dataSize; }
        
        public LocalDateTime getPreloadTime() { return preloadTime; }
        public void setPreloadTime(LocalDateTime preloadTime) { this.preloadTime = preloadTime; }
    }

    /**
     * 预加载报告
     */
    public static class PreloadReport {
        private LocalDateTime startTime;
        private LocalDateTime completeTime;
        private long durationSeconds;
        private int totalConfigs;
        private int successConfigs;
        private int failureConfigs;
        private Map<String, PreloadStatus> statusMap;

        // Getters and Setters
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        
        public LocalDateTime getCompleteTime() { return completeTime; }
        public void setCompleteTime(LocalDateTime completeTime) { this.completeTime = completeTime; }
        
        public long getDurationSeconds() { return durationSeconds; }
        public void setDurationSeconds(long durationSeconds) { this.durationSeconds = durationSeconds; }
        
        public int getTotalConfigs() { return totalConfigs; }
        public void setTotalConfigs(int totalConfigs) { this.totalConfigs = totalConfigs; }
        
        public int getSuccessConfigs() { return successConfigs; }
        public void setSuccessConfigs(int successConfigs) { this.successConfigs = successConfigs; }
        
        public int getFailureConfigs() { return failureConfigs; }
        public void setFailureConfigs(int failureConfigs) { this.failureConfigs = failureConfigs; }
        
        public Map<String, PreloadStatus> getStatusMap() { return statusMap; }
        public void setStatusMap(Map<String, PreloadStatus> statusMap) { this.statusMap = statusMap; }
    }
} 