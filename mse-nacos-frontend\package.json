{"name": "mse-nacos-frontend", "version": "1.3.0", "type": "module", "description": "MSE Nacos管理前端界面", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "ant-design-vue": "^4.1.2", "axios": "^1.6.0", "@ant-design/icons-vue": "^7.0.1", "dayjs": "^1.11.10"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "@vue/eslint-config-prettier": "^9.0.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.0.3", "vite": "^5.0.0"}}