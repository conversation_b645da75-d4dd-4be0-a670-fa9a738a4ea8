package com.just.git.utils;

import com.just.git.model.GitCredentials;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.eclipse.jgit.transport.CredentialsProvider;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.regex.Pattern;

/**
 * Git工具类
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
public class GitUtils {
    
    private static final Pattern GIT_URL_PATTERN = Pattern.compile(
        "^(https?|git|ssh)://[\\w\\-_]+([\\w\\-\\._]+)+([\\w\\-\\.,@?^=%&:/~\\+#]*[\\w\\-\\@?^=%&/~\\+#])?$"
    );
    
    private static final Pattern BRANCH_NAME_PATTERN = Pattern.compile("^[a-zA-Z0-9]([a-zA-Z0-9._-]*[a-zA-Z0-9])?$");
    
    /**
     * 私有构造函数，防止实例化
     */
    private GitUtils() {
        throw new IllegalStateException("工具类不能被实例化");
    }
    
    /**
     * 验证Git URL格式
     */
    public static boolean isValidGitUrl(String url) {
        return url != null && GIT_URL_PATTERN.matcher(url.trim()).matches();
    }
    
    /**
     * 验证分支名称格式
     */
    public static boolean isValidBranchName(String branchName) {
        return branchName != null && 
               branchName.length() <= 255 && 
               BRANCH_NAME_PATTERN.matcher(branchName).matches();
    }
    
    /**
     * 检查目录是否为Git仓库
     */
    public static boolean isGitRepository(Path directory) {
        if (directory == null || !Files.exists(directory)) {
            return false;
        }
        
        Path gitDir = directory.resolve(".git");
        return Files.exists(gitDir) && Files.isDirectory(gitDir);
    }
    
    /**
     * 安全删除目录及其内容
     */
    public static boolean deleteDirectorySafely(Path directory) {
        if (directory == null || !Files.exists(directory)) {
            return true;
        }
        
        try {
            Files.walk(directory)
                    .sorted((p1, p2) -> p2.compareTo(p1)) // 先删除子文件/目录
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            log.warn("删除文件失败: {}, 错误: {}", path, e.getMessage());
                        }
                    });
            return !Files.exists(directory);
        } catch (IOException e) {
            log.error("删除目录失败: {}", directory, e);
            return false;
        }
    }
    
    /**
     * 创建目录（如果不存在）
     */
    public static boolean ensureDirectoryExists(Path directory) {
        if (directory == null) {
            return false;
        }
        
        if (Files.exists(directory)) {
            return Files.isDirectory(directory);
        }
        
        try {
            Files.createDirectories(directory);
            return true;
        } catch (IOException e) {
            log.error("创建目录失败: {}", directory, e);
            return false;
        }
    }
    
    /**
     * 从Git认证信息创建JGit凭据提供者
     */
    public static CredentialsProvider createCredentialsProvider(GitCredentials credentials) {
        if (credentials == null || !credentials.isValid()) {
            return null;
        }
        
        switch (credentials.getAuthType()) {
            case TOKEN:
                // Token认证：用户名为token，密码为空或token
                return new UsernamePasswordCredentialsProvider("PRIVATE_TOKEN", credentials.getToken());
                
            case USERNAME_PASSWORD:
                return new UsernamePasswordCredentialsProvider(
                    credentials.getUsername(), 
                    credentials.getPassword()
                );
                
            case SSH_KEY:
                // SSH密钥认证需要特殊处理，这里返回null，由调用方处理
                log.warn("SSH密钥认证暂未实现，请使用Token或用户名密码认证");
                return null;
                
            default:
                return null;
        }
    }
    
    /**
     * 标准化Git URL
     */
    public static String normalizeGitUrl(String url) {
        if (url == null) {
            return null;
        }
        
        String normalized = url.trim();
        
        // 确保以.git结尾
        if (!normalized.endsWith(".git")) {
            normalized += ".git";
        }
        
        return normalized;
    }
    
    /**
     * 从URL提取仓库名称
     */
    public static String extractRepositoryName(String gitUrl) {
        if (gitUrl == null || gitUrl.trim().isEmpty()) {
            return null;
        }
        
        String url = gitUrl.trim();
        
        // 移除.git后缀
        if (url.endsWith(".git")) {
            url = url.substring(0, url.length() - 4);
        }
        
        // 提取最后一个路径段
        int lastSlashIndex = url.lastIndexOf('/');
        if (lastSlashIndex >= 0 && lastSlashIndex < url.length() - 1) {
            return url.substring(lastSlashIndex + 1);
        }
        
        return url;
    }
    
    /**
     * 验证文件路径是否在Git仓库内
     */
    public static boolean isPathInRepository(Path repositoryRoot, Path filePath) {
        if (repositoryRoot == null || filePath == null) {
            return false;
        }
        
        try {
            Path normalizedRepo = repositoryRoot.toRealPath();
            Path normalizedFile = filePath.toRealPath();
            return normalizedFile.startsWith(normalizedRepo);
        } catch (IOException e) {
            log.warn("验证路径失败: repo={}, file={}", repositoryRoot, filePath, e);
            return false;
        }
    }
    
    /**
     * 获取相对于仓库根目录的路径
     */
    public static String getRelativePath(Path repositoryRoot, Path filePath) {
        if (repositoryRoot == null || filePath == null) {
            return null;
        }
        
        try {
            Path normalizedRepo = repositoryRoot.toRealPath();
            Path normalizedFile = filePath.toRealPath();
            
            if (normalizedFile.startsWith(normalizedRepo)) {
                return normalizedRepo.relativize(normalizedFile).toString().replace('\\', '/');
            }
            
            return null;
        } catch (IOException e) {
            log.warn("获取相对路径失败: repo={}, file={}", repositoryRoot, filePath, e);
            return null;
        }
    }
    
    /**
     * 安全关闭Git资源
     */
    public static void closeGitSafely(Git git) {
        if (git != null) {
            try {
                git.close();
            } catch (Exception e) {
                log.warn("关闭Git资源时出现异常", e);
            }
        }
    }
} 