package com.just.nacos.exception;

import com.just.common.exception.BusinessException;

/**
 * Nacos相关异常基类
 *
 * <AUTHOR>
 * @since 2.0.0
 */
public class NacosException extends BusinessException {

    public NacosException(String message) {
        super(message);
    }

    public NacosException(String message, Throwable cause) {
        super(message, cause);
    }

    public NacosException(String code, String message) {
        super(code, message);
    }

    public NacosException(String code, String message, Throwable cause) {
        super(code, message, cause);
    }
}