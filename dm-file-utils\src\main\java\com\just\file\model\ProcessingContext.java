package com.just.file.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 处理上下文，用于在各个步骤间传递状态
 */
public class ProcessingContext {
    private final long startTime;
    private final String environment;
    private final List<ConfigurationDataCenter.NacosProperty> allProperties = new ArrayList<>();
    private final List<ConfigurationMetadata.ValidationError> validationErrors = new ArrayList<>();
    private int totalRows = 0;
    private int validRows = 0;
    private int totalSheets = 0;

    public ProcessingContext(long startTime,int totalSheets, String environment) {
        this.startTime = startTime;
        this.environment = environment;
        this.totalSheets = totalSheets;
    }

    public void addValidProperty(ConfigurationDataCenter.NacosProperty property) {
        allProperties.add(property);
        validRows++;
    }

    public void addTotalRows(int rows) {
        totalRows += rows;
    }

    public void addValidationError(ConfigurationMetadata.ValidationError error) {
        validationErrors.add(error);
    }

    public void setTotalSheets(int totalSheets) {
        this.totalSheets = totalSheets;
    }

    // Getters
    public long getStartTime() {
        return startTime;
    }

    public String getEnvironment() {
        return environment;
    }

    public List<ConfigurationDataCenter.NacosProperty> getAllProperties() {
        return allProperties;
    }

    public List<ConfigurationMetadata.ValidationError> getValidationErrors() {
        return validationErrors;
    }

    public int getTotalRows() {
        return totalRows;
    }

    public int getValidRows() {
        return validRows;
    }

    public int getTotalSheets() {
        return totalSheets;
    }
}