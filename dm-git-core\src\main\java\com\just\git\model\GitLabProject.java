package com.just.git.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Git项目信息模型（基于JGit）
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GitLabProject {
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 项目名称
     */
    private String name;
    
    /**
     * 项目路径
     *
     */
    private String path;
    /**
     * 项目路径
     */
    private String pathWithNamespace;
    /**
     * 项目描述
     */
    private String description;
    
    /**
     * 项目Git仓库地址
     */
    private String httpUrlToRepo;
    /**
     * 项目Git仓库地址
     */
    private String sshUrlToRepo;

    /**
     * Git仓库地址
     */
    private String webUrl;
    /**
     * 默认分支
     */
    private String defaultBranch;
    
    /**
     * 最后提交ID
     */
    private String lastCommitId;
    
    /**
     * 最后提交信息
     */
    private String lastCommitMessage;
    
    /**
     * 最后提交时间
     */
    private LocalDateTime lastCommitTime;
    
    /**
     * 最后提交作者
     */
    private String lastCommitAuthor;
    
    /**
     * 是否为裸仓库
     */
    private Boolean bareRepository;
    
    /**
     * 仓库是否存在
     */
    private Boolean exists;
    
    /**
     * 是否可访问（认证成功）
     */
    private Boolean accessible;

    /**
     * 检查项目名称是否匹配
     * 
     * @param projectName 项目名称
     * @return 是否匹配
     */
    public boolean nameMatches(String projectName) {
        if (projectName == null) {
            return false;
        }
        return projectName.equalsIgnoreCase(this.name) 
            || projectName.equalsIgnoreCase(this.path);
    }
    
    /**
     * 获取项目显示信息
     * 
     * @return 显示信息
     */
    public String getDisplayInfo() {
        StringBuilder info = new StringBuilder();
        info.append("项目: ").append(this.name).append("\n");
        info.append("地址: ").append(this.webUrl).append("\n");
        if (this.description != null) {
            info.append("描述: ").append(this.description).append("\n");
        }
        info.append("默认分支: ").append(this.defaultBranch != null ? this.defaultBranch : "未知").append("\n");
        info.append("存在状态: ").append(Boolean.TRUE.equals(this.exists) ? "存在" : "不存在").append("\n");
        info.append("访问状态: ").append(Boolean.TRUE.equals(this.accessible) ? "可访问" : "不可访问");
        if (this.lastCommitId != null) {
            info.append("\n最后提交: ").append(this.lastCommitId.substring(0, Math.min(8, this.lastCommitId.length())));
        }
        if (this.lastCommitMessage != null) {
            info.append("\n提交信息: ").append(this.lastCommitMessage);
        }
        
        return info.toString();
    }
    
    /**
     * 检查项目是否可用
     * 
     * @return 是否可用
     */
    public boolean isAvailable() {
        return Boolean.TRUE.equals(this.exists) && Boolean.TRUE.equals(this.accessible);
    }
} 