package com.just.file.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.*;

/**
 * Excel配置属性
 * 使用Spring @ConfigurationProperties实现配置外部化
 *
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
@Data
@ConfigurationProperties(prefix = "excel.config")
public class ExcelConfigProperties {

    /**
     * 文件配置映射
     */
    private Map<String, FileConfig> files = new HashMap<>();

    /**
     * 默认配置初始化
     */
    public ExcelConfigProperties() {
    }

    /**
     * 文件配置
     */
    @Data
    public static class FileConfig {
        /**
         * 文件路径（支持classpath:和绝对路径）
         */
        private String path;

        /**
         * 支持的工作表列表
         */
        private List<String> sheets = new ArrayList<>();

        /**
         * 文件编码
         */
        private String encoding = "UTF-8";

        /**
         * 是否启用
         */
        private boolean enabled = true;

        /**
         * 文件类型
         */
        private String type = "excel";

        /**
         * 描述信息
         */
        private String description;

        /**
         * 支持的环境列表
         */
        private List<String> supportedEnvironments = Arrays.asList("dev", "qa", "uat", "prod");
    }


    /**
     * 获取文件配置
     */
    public Optional<FileConfig> getFileConfig(String configKey) {
        return Optional.ofNullable(files.get(configKey));
    }

    /**
     * 获取配置的所有工作表
     */
    public List<String> getConfigSheets(String configKey) {
        return getFileConfig(configKey)
                .map(FileConfig::getSheets)
                .orElse(Collections.emptyList());
    }
} 