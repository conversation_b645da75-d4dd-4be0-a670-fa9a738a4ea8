package com.just.file.service;

import com.just.file.model.ConfigurationDataCenter;
import com.just.file.model.ConfigurationType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Excel配置服务
 * 重构后的版本，基于工厂模式提供统一的配置访问接口
 * 保持向后兼容性的同时，内部使用新的工厂架构
 * 
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExcelConfigService {
    
    private final ConfigurationDataFactory configurationDataFactory;
    
    /**
     * 读取所有Nacos配置
     */
    public List<ConfigurationDataCenter.NacosProperty> readAllNacosConfigs() {
        log.info("开始读取所有Nacos配置");
        List<ConfigurationDataCenter.NacosProperty> configs = configurationDataFactory.getAllNacosConfigurations();
        log.info("成功读取所有Nacos配置，总属性数: {}", configs.size());
        return configs;
    }

    /**
     * 读取URL映射配置
     */
    public List<ConfigurationDataCenter.NacosProperty> readUrlMappings(String environment) {
        log.info("开始读取URL映射配置，环境: {}", environment);
        List<ConfigurationDataCenter.NacosProperty> configs = configurationDataFactory.getUrlMappings(environment);
        log.info("成功读取URL映射配置，环境: {}，映射数: {}", environment, configs.size());
        return configs;
    }

    
    // ==================== 扩展方法 ====================
    
    /**
     * 获取所有URL映射配置（所有环境）
     */
    public List<ConfigurationDataCenter.NacosProperty> readAllUrlMappings() {
        log.info("开始读取所有URL映射配置");
        List<ConfigurationDataCenter.NacosProperty> configs = configurationDataFactory.getAllUrlMappings();
        log.info("成功读取所有URL映射配置，总映射数: {}", configs.size());
        return configs;
    }
    
    /**
     * 获取指定类型的配置
     */
    public List<ConfigurationDataCenter.NacosProperty> readConfigurations(ConfigurationType type) {
        log.info("开始读取配置类型: {}", type);
        List<ConfigurationDataCenter.NacosProperty> configs = configurationDataFactory.getConfigurations(type);
        log.info("成功读取配置类型: {}，总数: {}", type, configs.size());
        return configs;
    }
    
    /**
     * 获取指定类型和环境的配置
     */
    public List<ConfigurationDataCenter.NacosProperty> readConfigurations(ConfigurationType type, String environment) {
        log.info("开始读取配置类型: {}，环境: {}", type, environment);
        List<ConfigurationDataCenter.NacosProperty> configs = configurationDataFactory.getConfigurations(type, environment);
        log.info("成功读取配置类型: {}，环境: {}，总数: {}", type, environment, configs.size());
        return configs;
    }
    
    /**
     * 刷新所有配置
     */
    public void refreshAllConfigurations() {
        log.info("开始刷新所有配置");
        configurationDataFactory.refreshAll();
        log.info("所有配置刷新完成");
    }
    
    /**
     * 刷新指定类型的配置
     */
    public void refreshConfiguration(ConfigurationType type) {
        log.info("开始刷新配置类型: {}", type);
        configurationDataFactory.refresh(type);
        log.info("配置类型 {} 刷新完成", type);
    }
}