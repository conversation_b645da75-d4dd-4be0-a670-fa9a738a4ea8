package com.just.nacos;

import com.just.nacos.model.NacosConfig;
import com.just.nacos.service.NacosConfigService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * NacosConfigService 测试类
 * 基于AliyunMseActionService的Nacos配置管理测试
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class NacosConfigServiceTest {

    @Autowired
    private NacosConfigService nacosConfigService;

    private static final String TEST_NAMESPACE = "dev";
    private static final String TEST_GROUP = "dev";
    private static final String TEST_DATA_ID = "test-config.properties";
    private static final String TEST_CONTENT = "# 测试配置文件\n" +
            "app.name=test-application\n" +
            "app.version=1.0.0\n" +
            "app.environment=dev\n" +
            "\n" +
            "# 数据库配置\n" +
            "spring.datasource.url=*************************************" +
            "spring.datasource.username=root\n" +
            "spring.datasource.password=123456\n" +
            "\n" +
            "# Redis配置\n" +
            "spring.redis.host=localhost\n" +
            "spring.redis.port=6379\n" +
            "spring.redis.database=0";

    @Test
    public void testPublishConfig() {
        log.info("========== 测试发布配置 ==========");
        
        // 创建测试配置
        NacosConfig config = NacosConfig.builder()
                .dataId(TEST_DATA_ID)
                .group(TEST_GROUP)
                .namespace(TEST_NAMESPACE)
                .content(TEST_CONTENT)
                .type("properties")
                .environment("dev")
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .status(NacosConfig.ConfigStatus.ACTIVE)
                .build();

        try {
            // 执行发布
            boolean result = nacosConfigService.publishConfig(config);
            
            log.info("配置发布结果: {}", result ? "成功" : "失败");
            log.info("配置详情: dataId={}, group={}, namespace={}", TEST_DATA_ID, TEST_GROUP, TEST_NAMESPACE);
            
            assertTrue(result, "配置发布应该成功");
            
        } catch (Exception e) {
            log.error("配置发布失败", e);
            fail("配置发布不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testGetConfig() {
        log.info("========== 测试获取配置 ==========");
        
        try {
            // 获取配置
            Optional<NacosConfig> configOpt = nacosConfigService.getConfig(TEST_DATA_ID, TEST_GROUP, TEST_NAMESPACE);
            
            if (configOpt.isPresent()) {
                NacosConfig config = configOpt.get();
                log.info("获取配置成功:");
                log.info("  dataId: {}", config.getDataId());
                log.info("  group: {}", config.getGroup());
                log.info("  namespace: {}", config.getNamespace());
                log.info("  type: {}", config.getType());
                log.info("  status: {}", config.getStatus());
                
                assertEquals(TEST_DATA_ID, config.getDataId());
                assertEquals(TEST_GROUP, config.getGroup());
                assertEquals(TEST_NAMESPACE, config.getNamespace());
                assertNotNull(config.getType());
                
            } else {
                log.warn("未找到指定配置: {}/{}/{}", TEST_NAMESPACE, TEST_GROUP, TEST_DATA_ID);
                // 这里不一定要fail，因为可能是由于MSE API的延迟或权限问题
            }
            
        } catch (Exception e) {
            log.error("获取配置失败", e);
            fail("获取配置不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testListConfigs() {
        log.info("========== 测试获取配置列表 ==========");
        
        try {
            // 获取dev环境的所有配置
            List<NacosConfig> configs = nacosConfigService.listConfigs(1, 20, null, TEST_NAMESPACE);
            
            log.info("获取到配置列表，总数: {}", configs.size());
            
            if (!configs.isEmpty()) {
                log.info("配置列表详情:");
                for (int i = 0; i < Math.min(configs.size(), 10); i++) {
                    NacosConfig config = configs.get(i);
                    log.info("  [{}] dataId: {}, group: {}, type: {}", 
                            i + 1, config.getDataId(), config.getGroup(), config.getType());
                }
                
                if (configs.size() > 10) {
                    log.info("  ... 还有 {} 个配置", configs.size() - 10);
                }
            } else {
                log.info("当前环境暂无配置");
            }
            
            assertNotNull(configs);
            
        } catch (Exception e) {
            log.error("获取配置列表失败", e);
            fail("获取配置列表不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testConfigExists() {
        log.info("========== 测试检查配置是否存在 ==========");
        
        try {
            // 检查配置是否存在
            boolean exists = nacosConfigService.configExists(TEST_DATA_ID, TEST_GROUP, TEST_NAMESPACE);
            log.info("配置 {}/{}/{} 是否存在: {}", TEST_NAMESPACE, TEST_GROUP, TEST_DATA_ID, exists);
            
            // 检查不存在的配置
            boolean notExists = nacosConfigService.configExists("non-existent-config", TEST_GROUP, TEST_NAMESPACE);
            log.info("不存在的配置检查结果: {}", notExists);
            
            assertFalse(notExists, "不存在的配置应该返回false");
            
        } catch (Exception e) {
            log.error("检查配置存在性失败", e);
            fail("检查配置存在性不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testValidateConfigFormat() {
        log.info("========== 测试配置格式验证 ==========");
        
        // 测试Properties格式
        String propertiesContent = "app.name=test\napp.version=1.0";
        String propertiesResult = nacosConfigService.validateConfigFormat(propertiesContent, "properties");
        log.info("Properties格式验证结果: {}", propertiesResult);
        assertEquals("格式验证通过", propertiesResult);
        
        // 测试JSON格式
        String jsonContent = "{\"app\":{\"name\":\"test\",\"version\":\"1.0\"}}";
        String jsonResult = nacosConfigService.validateConfigFormat(jsonContent, "json");
        log.info("JSON格式验证结果: {}", jsonResult);
        assertEquals("格式验证通过", jsonResult);
        
        // 测试YAML格式
        String yamlContent = "app:\n  name: test\n  version: 1.0";
        String yamlResult = nacosConfigService.validateConfigFormat(yamlContent, "yaml");
        log.info("YAML格式验证结果: {}", yamlResult);
        assertEquals("格式验证通过", yamlResult);
        
        // 测试无效JSON格式
        String invalidJson = "{\"app\":\"test";
        String invalidResult = nacosConfigService.validateConfigFormat(invalidJson, "json");
        log.info("无效JSON验证结果: {}", invalidResult);
        assertTrue(invalidResult.contains("格式验证失败"));
    }

    @Test
    public void testFormatConfigContent() {
        log.info("========== 测试配置内容格式化 ==========");
        
        // 测试JSON格式化
        String compactJson = "{\"app\":{\"name\":\"test\",\"version\":\"1.0\"}}";
        String formattedJson = nacosConfigService.formatConfigContent(compactJson, "json");
        log.info("格式化前JSON: {}", compactJson);
        log.info("格式化后JSON:\n{}", formattedJson);
        assertTrue(formattedJson.contains("\n"), "格式化后的JSON应该包含换行符");
        
        // 测试YAML格式化
        String compactYaml = "app: {name: test, version: 1.0}";
        String formattedYaml = nacosConfigService.formatConfigContent(compactYaml, "yaml");
        log.info("格式化前YAML: {}", compactYaml);
        log.info("格式化后YAML:\n{}", formattedYaml);
        
        // 测试Properties格式（不格式化）
        String properties = "app.name=test\napp.version=1.0";
        String formattedProperties = nacosConfigService.formatConfigContent(properties, "properties");
        log.info("Properties内容: {}", formattedProperties);
        assertEquals(properties, formattedProperties);
    }

    @Test
    public void testEnvironmentConnection() {
        log.info("========== 测试环境连接 ==========");
        
        try {
            // 通过获取配置列表来测试连接
            List<NacosConfig> configs = nacosConfigService.listConfigs(1, 1, null, TEST_NAMESPACE);
            log.info("环境连接测试成功，可以正常获取配置列表");
            assertNotNull(configs);
            
        } catch (Exception e) {
            log.error("环境连接测试失败", e);
            fail("环境连接测试不应该失败: " + e.getMessage());
        }
    }
} 