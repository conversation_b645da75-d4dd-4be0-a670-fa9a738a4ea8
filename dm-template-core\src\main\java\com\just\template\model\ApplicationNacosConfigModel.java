package com.just.template.model;

import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * Application Nacos配置模板参数模型
 * 对应 application.ftl 模板
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
@Builder
public class ApplicationNacosConfigModel {

    /**
     * 服务名称
     * 对应模板中的 ${serviceName}
     */
    private String content;


    /**
     * 验证必需的参数
     *
     * @throws IllegalArgumentException 如果缺少必需参数
     */
    public void validate() {
        if (content == null || content.trim().isEmpty()) {
            throw new IllegalArgumentException("content is required for Application configuration");
        }
    }
}