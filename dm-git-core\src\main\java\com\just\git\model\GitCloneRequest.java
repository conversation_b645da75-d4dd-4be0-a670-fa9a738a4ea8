package com.just.git.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.nio.file.Path;

/**
 * Git克隆请求
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GitCloneRequest {
    
    /**
     * 服务名称（用于构建仓库URL）
     */
    private String serviceName;
    
    /**
     * 仓库URL（可选，优先级高于serviceName）
     */
    private String repositoryUrl;
    
    /**
     * 本地克隆路径
     */
    private Path localPath;
    
    /**
     * 分支名称（默认为master）
     */
    private String branch = "master";
    
    /**
     * Git认证信息
     */
    private GitCredentials credentials;
    /**
     * 是否克隆所有分支
     */
    private boolean cloneAllBranches = false;
    
    /**
     * 是否在克隆后检出指定分支
     */
    private boolean checkoutBranch = true;
    
    /**
     * 验证请求参数
     */
    public void validate() {
        if (serviceName == null && repositoryUrl == null) {
            throw new IllegalArgumentException("serviceName或repositoryUrl必须至少指定一个");
        }
        
        if (localPath == null) {
            throw new IllegalArgumentException("localPath不能为空");
        }
        
        if (branch == null || branch.trim().isEmpty()) {
            throw new IllegalArgumentException("branch不能为空");
        }

    }
    
    /**
     * 获取有效的仓库URL
     */
    public String getEffectiveRepositoryUrl(String baseUrl) {
        if (repositoryUrl != null && !repositoryUrl.trim().isEmpty()) {
            return repositoryUrl;
        }
        
        if (serviceName != null && !serviceName.trim().isEmpty() && baseUrl != null) {
            return String.format("%s/microservice/%s.git", baseUrl, serviceName);
        }
        
        throw new IllegalArgumentException("无法构建有效的仓库URL");
    }
} 