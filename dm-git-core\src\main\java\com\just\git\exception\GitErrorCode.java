package com.just.git.exception;

/**
 * Git操作错误码
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
public enum GitErrorCode {
    
    /**
     * 克隆失败
     */
    CLONE_FAILED("CLONE_FAILED", "克隆仓库失败"),
    
    /**
     * 分支切换失败
     */
    BRANCH_SWITCH_FAILED("BRANCH_SWITCH_FAILED", "分支切换失败"),
    
    /**
     * 分支创建失败
     */
    BRANCH_CREATE_FAILED("BRANCH_CREATE_FAILED", "分支创建失败"),
    
    /**
     * 提交失败
     */
    COMMIT_FAILED("COMMIT_FAILED", "代码提交失败"),
    
    /**
     * 推送失败
     */
    PUSH_FAILED("PUSH_FAILED", "代码推送失败"),
    
    /**
     * 拉取失败
     */
    PULL_FAILED("PULL_FAILED", "代码拉取失败"),
    
    /**
     * 认证失败
     */
    AUTHENTICATION_FAILED("AUTHENTICATION_FAILED", "Git认证失败"),
    
    /**
     * 仓库不存在
     */
    REPOSITORY_NOT_FOUND("REPOSITORY_NOT_FOUND", "Git仓库不存在"),
    
    /**
     * 网络错误
     */
    NETWORK_ERROR("NETWORK_ERROR", "网络连接错误"),
    
    /**
     * 文件冲突
     */
    MERGE_CONFLICT("MERGE_CONFLICT", "代码合并冲突"),
    
    /**
     * 仓库状态异常
     */
    REPOSITORY_STATE_ERROR("REPOSITORY_STATE_ERROR", "仓库状态异常"),
    
    /**
     * 未知错误
     */
    UNKNOWN_ERROR("UNKNOWN_ERROR", "未知Git操作错误");
    
    private final String code;
    private final String description;
    
    GitErrorCode(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return String.format("%s: %s", code, description);
    }
} 