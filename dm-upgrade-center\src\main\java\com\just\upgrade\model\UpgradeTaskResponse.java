package com.just.upgrade.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 升级任务响应
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpgradeTaskResponse {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 创建时间
     */
    private long createTime;

    /**
     * 预估完成时间
     */
    private Long estimatedCompletionTime;

    /**
     * 任务描述
     */
    private String description;
}