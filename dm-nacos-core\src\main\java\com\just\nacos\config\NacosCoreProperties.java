package com.just.nacos.config;

import com.just.nacos.enums.NacosEnvironmentEnum;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Nacos核心模块配置属性
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
@ConfigurationProperties(prefix = "dm.nacos")
public class NacosCoreProperties {

    /**
     * 连接超时时间（毫秒）
     */
    private long connectionTimeout = 3000L;

    /**
     * 请求超时时间（毫秒）
     */
    private long requestTimeout = 10000L;

    /**
     * 是否启用SSL
     */
    private boolean enableSsl = false;

    /**
     * 环境配置映射
     * key: 环境名称（如dev、test、prod）
     * value: 环境配置信息
     */
    private Map<String, EnvironmentConfig> environments;

    /**
     * 支持的配置格式
     */
    private List<String> supportedFormats = Arrays.asList("properties", "yaml", "yml", "json", "xml");

    /**
     * 是否启用配置监听
     */
    private boolean enableListener = true;

    /**
     * 是否启用配置缓存
     */
    private boolean enableCache = true;

    /**
     * 缓存刷新间隔（秒）
     */
    private long cacheRefreshInterval = 300L;

    /**
     * 是否启用配置备份
     */
    private boolean enableBackup = true;

    /**
     * 备份保留天数
     */
    private int backupRetentionDays = 30;

    /**
     * 环境配置类
     */
    @Data
    public static class EnvironmentConfig {
        /**
         * 环境名称
         */
        private String name;

        /**
         * 环境描述
         */
        private String description;

        /**
         * 命名空间ID
         */
        private String namespace;

        /**
         * 默认分组
         */
        private String defaultGroup;

        /**
         * 配置前缀
         */
        private String configPrefix = "";

        /**
         * 是否启用
         */
        private boolean enabled = true;
        /**
         * endpoint
         */
        private String endPoint="mse.cn-shanghai.aliyuncs.com";
        /**
         * endpoint
         */
        private String regionId = "cn-shanghai";
        private String instanceId;
        private String serverAddr;
        private String accessKey;
        private String secretKey;
    }

    /**
     * 验证配置有效性
     */
    public void validate() {
        if (connectionTimeout <= 0) {
            throw new IllegalArgumentException("连接超时时间必须大于0");
        }

        if (requestTimeout <= 0) {
            throw new IllegalArgumentException("请求超时时间必须大于0");
        }

        if (cacheRefreshInterval <= 0) {
            throw new IllegalArgumentException("缓存刷新间隔必须大于0");
        }

        if (backupRetentionDays <= 0) {
            throw new IllegalArgumentException("备份保留天数必须大于0");
        }
    }

    /**
     * 获取指定环境的配置
     *
     * @param envName 环境名称
     * @return 环境配置
     */
    public EnvironmentConfig getEnvironmentConfig(NacosEnvironmentEnum envName) {
        if (environments == null || envName == null) {
            return null;
        }
        return environments.get(envName.getEnv());
    }

    /**
     * 获取环境的服务器地址
     *
     * @param envName 环境名称
     * @return 服务器地址
     */
    public String getServerAddrForEnvironment(NacosEnvironmentEnum envName) {
        EnvironmentConfig envConfig = getEnvironmentConfig(envName);
        if (envConfig != null && envConfig.getServerAddr() != null) {
            return envConfig.getServerAddr();
        }
        return envConfig.getServerAddr();
    }

    /**
     * 获取环境的命名空间
     *
     * @param envName 环境名称
     * @return 命名空间ID
     */
    public String getNamespaceForEnvironment(NacosEnvironmentEnum envName) {
        EnvironmentConfig envConfig = getEnvironmentConfig(envName);
        if (envConfig != null && envConfig.getNamespace() != null) {
            return envConfig.getNamespace();
        }
        return envConfig.getNamespace();
    }

    /**
     * 获取环境的默认分组
     *
     * @param envName 环境名称
     * @return 默认分组
     */
    public String getDefaultGroupForEnvironment(NacosEnvironmentEnum envName) {
        EnvironmentConfig envConfig = getEnvironmentConfig(envName);
        if (envConfig != null && envConfig.getDefaultGroup() != null) {
            return envConfig.getDefaultGroup();
        }
        return envConfig.getDefaultGroup();
    }
}