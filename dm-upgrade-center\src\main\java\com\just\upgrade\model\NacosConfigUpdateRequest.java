package com.just.upgrade.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * Nacos配置更新请求
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NacosConfigUpdateRequest {

    /**
     * 配置ID
     */
    @NotBlank(message = "配置ID不能为空")
    @Size(max = 255, message = "配置ID长度不能超过255个字符")
    private String dataId;

    /**
     * 配置组
     */
    @NotBlank(message = "配置组不能为空")
    @Size(max = 128, message = "配置组长度不能超过128个字符")
    private String group;

    /**
     * 命名空间
     */
    @Size(max = 128, message = "命名空间长度不能超过128个字符")
    private String namespace;

    /**
     * 配置内容
     */
    @NotNull(message = "配置内容不能为null")
    @Size(max = 1048576, message = "配置内容长度不能超过1MB")
    private String content;

    /**
     * 配置类型
     */
    @Size(max = 32, message = "配置类型长度不能超过32个字符")
    private String type = "text";

    /**
     * 环境名称
     */
    @NotBlank(message = "环境名称不能为空")
    @Size(max = 32, message = "环境名称长度不能超过32个字符")
    private String environment;

    /**
     * 应用名称
     */
    @Size(max = 128, message = "应用名称长度不能超过128个字符")
    private String appName;

    /**
     * 备注信息
     */
    @Size(max = 512, message = "备注信息长度不能超过512个字符")
    private String description;

    /**
     * 是否立即发布
     */
    private Boolean publishImmediately = true;

    /**
     * 是否备份原配置
     */
    private Boolean backupOriginal = true;

    /**
     * 验证请求的有效性
     *
     * @return 验证结果
     */
    public boolean isValid() {
        return dataId != null && !dataId.trim().isEmpty() &&
               group != null && !group.trim().isEmpty() &&
               environment != null && !environment.trim().isEmpty() &&
               content != null;
    }

    /**
     * 获取配置的完整标识
     *
     * @return 配置标识
     */
    public String getFullIdentifier() {
        return String.format("%s/%s/%s", 
            namespace != null ? namespace : "default", group, dataId);
    }

    /**
     * 设置默认值
     */
    public void setDefaults() {
        if (type == null || type.trim().isEmpty()) {
            type = "text";
        }
        if (namespace == null || namespace.trim().isEmpty()) {
            namespace = "public";
        }
        if (publishImmediately == null) {
            publishImmediately = true;
        }
        if (backupOriginal == null) {
            backupOriginal = true;
        }
    }
}