package com.just.file.model;

/**
 * 配置类型枚举
 * 
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
public enum ConfigurationType {
    
    /**
     * Nacos配置
     */
    NACOS("nacos", "Nacos环境变量配置"),
    
    /**
     * URL映射配置
     */
    URL_MAPPING("url", "URL映射配置"),
    
    /**
     * 通用键值对配置
     */
    KEY_VALUE("key-value", "通用键值对配置"),
    
    /**
     * 原始数据
     */
    RAW_DATA("raw", "原始数据");

    private final String code;
    private final String description;

    ConfigurationType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取配置类型
     */
    public static ConfigurationType fromCode(String code) {
        for (ConfigurationType type : values()) {
            if (type.code.equalsIgnoreCase(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的配置类型: " + code);
    }

    /**
     * 检查代码是否有效
     */
    public static boolean isValidCode(String code) {
        try {
            fromCode(code);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
} 