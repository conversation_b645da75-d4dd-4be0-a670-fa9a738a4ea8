package com.just.file.service;

import com.just.file.config.ExcelConfigProperties;
import com.just.file.model.*;
import com.just.file.utils.ExcelReaderHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * URL映射配置处理器
 * 专门处理URL映射配置
 * 
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UrlMappingConfigurationProcessor implements ConfigurationProcessor {
    
    private final ExcelReaderHelper excelReaderHelper;
    
    @Override
    public ConfigurationType getSupportedType() {
        return ConfigurationType.URL_MAPPING;
    }
    
    @Override
    public ProcessingResult processWithMetadata(ExcelConfigProperties.FileConfig fileConfig,  ProcessingContext context) {
        String environment = context.getEnvironment();
        log.info("开始处理URL映射配置，文件: {}，环境: {}", fileConfig.getPath(), environment);
        try {
            List<String> sheetsToProcess = determineSheetsToProcess(fileConfig, environment);
            processSelectedSheets(fileConfig, sheetsToProcess, context);
            ConfigurationMetadata metadata = buildProcessingMetadata(fileConfig, context);
            
            log.info("成功处理URL映射配置，环境: {}，映射数: {}", environment, context.getAllProperties().size());
            return ProcessingResult.success(ConfigurationType.URL_MAPPING, context.getAllProperties(), metadata);
            
        } catch (Exception e) {
            log.error("处理URL映射配置失败，环境: {}", environment, e);
            return ProcessingResult.failure(ConfigurationType.URL_MAPPING, "处理失败: " + e.getMessage());
        }
    }

    @Override
    public boolean validate(ExcelConfigProperties.FileConfig fileConfig) {
        return fileConfig != null && 
               fileConfig.getPath() != null && 
               fileConfig.getSheets() != null && 
               !fileConfig.getSheets().isEmpty() &&
               fileConfig.isEnabled();
    }
    
    // ==================== 私有方法：处理步骤拆分 ====================
    /**
     * 确定要处理的工作表
     */
    private List<String> determineSheetsToProcess(ExcelConfigProperties.FileConfig fileConfig, String environment) {
        return environment != null ? 
            Collections.singletonList(environment) : fileConfig.getSheets();
    }
    
    /**
     * 处理选定的工作表
     */
    private void processSelectedSheets(ExcelConfigProperties.FileConfig fileConfig, 
                                     List<String> sheetsToProcess, ProcessingContext context) {
        for (String sheetName : sheetsToProcess) {
            if (!validateSheetExists(fileConfig, sheetName, context)) {
                continue;
            }
            
            try {
                processSheet(fileConfig.getPath(), sheetName, context);
                log.debug("成功处理工作表: {}", sheetName);
            } catch (Exception e) {
                handleSheetError(sheetName, e, context);
            }
        }
        context.setTotalSheets(sheetsToProcess.size());
    }
    
    /**
     * 验证工作表是否存在
     */
    private boolean validateSheetExists(ExcelConfigProperties.FileConfig fileConfig, 
                                      String sheetName, ProcessingContext context) {
        if (!fileConfig.getSheets().contains(sheetName)) {
            log.warn("工作表 {} 不在配置的工作表列表中，跳过处理", sheetName);
            addSheetNotFoundError(sheetName, context);
            return false;
        }
        return true;
    }
    
    /**
     * 处理单个工作表
     */
    private void processSheet(String filePath, String sheetName, ProcessingContext context) {
        List<List<Object>> rows = excelReaderHelper.readSheet(filePath, sheetName);
        context.addTotalRows(rows.size());
        
        for (int i = 0; i < rows.size(); i++) {
            List<Object> row = rows.get(i);
            if (!excelReaderHelper.isEmptyRow(row)) {
                processRow(filePath, sheetName, row, i + 1, context);
            }
        }
    }
    
    /**
     * 处理单行数据
     */
    private void processRow(String filePath, String sheetName, List<Object> row, int rowIndex, ProcessingContext context) {
        try {
            String line = excelReaderHelper.getStringValue(row, 0);
            
            if (isValidKeyValuePair(line)) {
                ConfigurationDataCenter.NacosProperty property = parseKeyValuePair(line, filePath, sheetName, rowIndex);
                if (property != null) {
                    context.addValidProperty(property);
                }
            } else {
                addInvalidKeyValueError(context, rowIndex, line);
            }
        } catch (Exception e) {
            addRowProcessingError(context, rowIndex, e, String.valueOf(row));
        }
    }
    
    /**
     * 添加工作表未找到错误
     */
    private void addSheetNotFoundError(String sheetName, ProcessingContext context) {
        context.addValidationError(ConfigurationMetadata.ValidationError.builder()
            .errorCode("SHEET_NOT_FOUND")
            .message("工作表不在配置列表中")
            .rowIndex(0)
            .columnIndex(0)
            .errorValue(sheetName)
            .level(ConfigurationMetadata.ErrorLevel.WARN)
            .build());
    }
    
    /**
     * 添加无效键值对错误
     */
    private void addInvalidKeyValueError(ProcessingContext context, int rowIndex, String line) {
        context.addValidationError(ConfigurationMetadata.ValidationError.builder()
            .errorCode("INVALID_KEY_VALUE_PAIR")
            .message("无效的键值对格式")
            .rowIndex(rowIndex)
            .columnIndex(0)
            .errorValue(line)
            .level(ConfigurationMetadata.ErrorLevel.WARN)
            .build());
    }
    
    /**
     * 添加行处理错误
     */
    private void addRowProcessingError(ProcessingContext context, int rowIndex, Exception e, String errorValue) {
        context.addValidationError(ConfigurationMetadata.ValidationError.builder()
            .errorCode("ROW_PROCESSING_ERROR")
            .message("处理行数据失败: " + e.getMessage())
            .rowIndex(rowIndex)
            .columnIndex(0)
            .errorValue(errorValue)
            .level(ConfigurationMetadata.ErrorLevel.ERROR)
            .build());
    }
    
    /**
     * 处理工作表错误
     */
    private void handleSheetError(String sheetName, Exception e, ProcessingContext context) {
        log.warn("处理工作表 {} 失败: {}", sheetName, e.getMessage());
        context.addValidationError(ConfigurationMetadata.ValidationError.builder()
            .errorCode("SHEET_READ_ERROR")
            .message("读取工作表失败: " + e.getMessage())
            .rowIndex(0)
            .columnIndex(0)
            .errorValue(sheetName)
            .level(ConfigurationMetadata.ErrorLevel.ERROR)
            .build());
    }
    
    /**
     * 构建处理元数据
     */
    private ConfigurationMetadata buildProcessingMetadata(ExcelConfigProperties.FileConfig fileConfig, ProcessingContext context) {
        long endTime = System.currentTimeMillis();
        long duration = endTime - context.getStartTime();
        
        return ConfigurationMetadata.builder()
            .totalRows(context.getTotalRows())
            .validRows(context.getValidRows())
            .readDuration(java.time.Duration.ofMillis(duration))
            .processingDuration(java.time.Duration.ofMillis(duration))
            .readStartTime(java.time.LocalDateTime.now().minusNanos(duration * 1000000))
            .readEndTime(java.time.LocalDateTime.now())
            .sheetCount(context.getTotalSheets())
            .validationErrors(context.getValidationErrors())
            .processorInfo(this.getClass().getSimpleName())
            .cacheHit(false)
            .dataSource(fileConfig.getPath())
            .build();
    }
    
    // ==================== 业务逻辑方法 ====================
    
    /**
     * 验证是否为有效的键值对
     */
    private boolean isValidKeyValuePair(String line) {
        return line != null && line.contains(":") && !line.startsWith(":") && !line.endsWith(":");
    }
    
    /**
     * 解析键值对
     */
    private ConfigurationDataCenter.NacosProperty parseKeyValuePair(String line, String filePath, String sheetName, int rowIndex) {
        String[] parts = line.split(":", 2);
        String key = parts[0].trim();
        String value = parts[1].trim();
        
        // 移除引号
        value = removeQuotes(value);
        
        // 处理URL转换
        if (isK8sUrl(value)) {
            value = processUrl(value);
        }
        
        return ConfigurationDataCenter.NacosProperty.builder()
                .fileName(filePath)
                .environment(sheetName)
                .sheetName(sheetName)
                .type(ConfigurationType.URL_MAPPING)
                .rowIndex(rowIndex)
                .k8sEnv(key)
                .nacosEnv(value)
                .build();
    }
    
    /**
     * 移除值两端的引号
     */
    private String removeQuotes(String value) {
        if (value.startsWith("\"") && value.endsWith("\"")) {
            return value.substring(1, value.length() - 1);
        }
        return value;
    }
    
    /**
     * 判断是否为K8s URL
     */
    private boolean isK8sUrl(String value) {
        return (value.startsWith("http://") || value.startsWith("https://")) && 
               value.contains("svc.cluster.local");
    }
    
    /**
     * 处理URL转换（K8s -> 简化格式）
     */
    private String processUrl(String value) {
        String urlWithoutProtocol = value.substring(value.indexOf("//") + 2);
        String[] urlParts = urlWithoutProtocol.split("/", 2);
        
        if (urlParts.length > 0) {
            String domain = urlParts[0];
            String serviceName = extractServiceName(domain);
            
            String newValue = "http://" + serviceName;
            if (urlParts.length > 1) {
                newValue += "/" + urlParts[1];
            }
            return newValue;
        }
        return value;
    }
    
    /**
     * 提取服务名称
     */
    private String extractServiceName(String domain) {
        String serviceName = domain;
        
        if (domain.contains(".")) {
            serviceName = domain.substring(0, domain.indexOf("."));
        }
        
        if (serviceName.contains(":")) {
            serviceName = serviceName.substring(0, serviceName.indexOf(":"));
        }
        
        return serviceName;
    }


} 