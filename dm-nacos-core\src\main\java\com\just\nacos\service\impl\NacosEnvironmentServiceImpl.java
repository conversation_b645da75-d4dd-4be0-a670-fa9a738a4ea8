package com.just.nacos.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.just.nacos.config.NacosCoreProperties;
import com.just.nacos.exception.NacosEnvironmentException;
import com.just.nacos.enums.EnvironmentStatus;
import com.just.nacos.model.NacosEnvironment;
import com.just.nacos.enums.NacosEnvironmentEnum;
import com.just.nacos.service.NacosEnvironmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Nacos环境管理服务实现
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@Service
public class NacosEnvironmentServiceImpl implements NacosEnvironmentService {

    private final NacosCoreProperties properties;
    private final ObjectMapper objectMapper;
    
    // 内存中的环境配置存储（实际项目中可能使用数据库）
    private final Map<String, NacosEnvironment> environmentsCache = new ConcurrentHashMap<>();

    public NacosEnvironmentServiceImpl(NacosCoreProperties properties) {
        this.properties = properties;
        this.objectMapper = new ObjectMapper();
    }

    @PostConstruct
    public void init() {
        // 初始化预配置的环境
        initializePredefinedEnvironments();
        log.info("Nacos环境管理服务初始化完成，当前环境数量: {}", environmentsCache.size());
    }

    @Override
    public boolean createEnvironment(NacosEnvironment environment) {
        try {
            validateEnvironmentForCreation(environment);
            if (environmentExists(environment.getName())) {
                throw NacosEnvironmentException.environmentAlreadyExists(environment.getName());
            }
            
            // 设置创建时间
            environment.setCreateTime(LocalDateTime.now());
            environment.setUpdateTime(LocalDateTime.now());
            
            // 验证配置
            String validationResult = validateEnvironmentConfig(environment);
            if (!validationResult.equals("验证通过")) {
                throw NacosEnvironmentException.configInvalid(environment.getName(), validationResult);
            }
            
            // 保存到缓存
            environmentsCache.put(environment.getName(), environment);
            
            log.info("环境创建成功: {}", environment.getName());
            return true;
            
        } catch (Exception e) {
            log.error("创建环境失败: {}", environment.getName(), e);
            return false;
        }
    }

    @Override
    public boolean updateEnvironment(NacosEnvironment environment) {
        try {
            if (!environmentExists(environment.getName())) {
                throw NacosEnvironmentException.environmentNotFound(environment.getName());
            }
            
            // 获取现有环境
            NacosEnvironment existing = environmentsCache.get(environment.getName());
            
            // 保留创建时间，更新修改时间
            environment.setCreateTime(existing.getCreateTime());
            environment.setUpdateTime(LocalDateTime.now());
            
            // 验证配置
            String validationResult = validateEnvironmentConfig(environment);
            if (!validationResult.equals("验证通过")) {
                throw NacosEnvironmentException.configInvalid(environment.getName(), validationResult);
            }
            
            // 更新缓存
            environmentsCache.put(environment.getName(), environment);
            
            log.info("环境更新成功: {}", environment.getName());
            return true;
            
        } catch (Exception e) {
            log.error("更新环境失败: {}", environment.getName(), e);
            return false;
        }
    }

    @Override
    public boolean deleteEnvironment(String environmentName) {
        try {
            if (!environmentExists(environmentName)) {
                throw NacosEnvironmentException.environmentNotFound(environmentName);
            }
            
            NacosEnvironment environment = environmentsCache.get(environmentName);
            
            // 检查是否为默认环境
            if (Boolean.TRUE.equals(environment.getIsDefault())) {
                throw NacosEnvironmentException.configInvalid(environmentName, "不能删除默认环境");
            }
            
            // 从缓存中移除
            environmentsCache.remove(environmentName);
            
            log.info("环境删除成功: {}", environmentName);
            return true;
            
        } catch (Exception e) {
            log.error("删除环境失败: {}", environmentName, e);
            return false;
        }
    }

    @Override
    public Optional<NacosEnvironment> getEnvironment(String environmentName) {
        return Optional.ofNullable(environmentsCache.get(environmentName));
    }

    @Override
    public List<NacosEnvironment> listEnvironments() {
        return new ArrayList<>(environmentsCache.values());
    }

    @Override
    public List<NacosEnvironment> listAvailableEnvironments() {
        return environmentsCache.values().stream()
                .filter(NacosEnvironment::isAvailable)
                .collect(Collectors.toList());
    }

    @Override
    public List<NacosEnvironment> searchEnvironments(String keyword) {
        if (!StringUtils.hasText(keyword)) {
            return listEnvironments();
        }
        
        String lowerKeyword = keyword.toLowerCase();
        return environmentsCache.values().stream()
                .filter(env -> 
                    env.getName().toLowerCase().contains(lowerKeyword) ||
                    (env.getDisplayName() != null && env.getDisplayName().toLowerCase().contains(lowerKeyword)) ||
                    (env.getDescription() != null && env.getDescription().toLowerCase().contains(lowerKeyword))
                )
                .collect(Collectors.toList());
    }

    @Override
    public boolean environmentExists(String environmentName) {
        return environmentsCache.containsKey(environmentName);
    }

    @Override
    public boolean isEnvironmentAvailable(String environmentName) {
        return getEnvironment(environmentName)
                .map(NacosEnvironment::isAvailable)
                .orElse(false);
    }

    @Override
    public boolean activateEnvironment(String environmentName) {
        try {
            Optional<NacosEnvironment> envOpt = getEnvironment(environmentName);
            if (!envOpt.isPresent()) {
                throw NacosEnvironmentException.environmentNotFound(environmentName);
            }
            
            NacosEnvironment environment = envOpt.get();
            environment.setStatus(EnvironmentStatus.ACTIVE);
            environment.setEnabled(true);
            environment.setUpdateTime(LocalDateTime.now());
            
            log.info("环境激活成功: {}", environmentName);
            return true;
            
        } catch (Exception e) {
            log.error("激活环境失败: {}", environmentName, e);
            return false;
        }
    }

    @Override
    public boolean deactivateEnvironment(String environmentName) {
        try {
            Optional<NacosEnvironment> envOpt = getEnvironment(environmentName);
            if (!envOpt.isPresent()) {
                throw NacosEnvironmentException.environmentNotFound(environmentName);
            }
            
            NacosEnvironment environment = envOpt.get();
            
            // 检查是否为默认环境
            if (Boolean.TRUE.equals(environment.getIsDefault())) {
                throw NacosEnvironmentException.configInvalid(environmentName, "不能停用默认环境");
            }
            
            environment.setStatus(EnvironmentStatus.INACTIVE);
            environment.setEnabled(false);
            environment.setUpdateTime(LocalDateTime.now());
            
            log.info("环境停用成功: {}", environmentName);
            return true;
            
        } catch (Exception e) {
            log.error("停用环境失败: {}", environmentName, e);
            return false;
        }
    }

    @Override
    public boolean setDefaultEnvironment(String environmentName) {
        try {
            Optional<NacosEnvironment> envOpt = getEnvironment(environmentName);
            if (!envOpt.isPresent()) {
                throw NacosEnvironmentException.environmentNotFound(environmentName);
            }
            
            // 清除所有环境的默认标志
            environmentsCache.values().forEach(env -> env.setIsDefault(false));
            
            // 设置新的默认环境
            NacosEnvironment environment = envOpt.get();
            environment.setIsDefault(true);
            environment.setUpdateTime(LocalDateTime.now());
            
            log.info("默认环境设置成功: {}", environmentName);
            return true;
            
        } catch (Exception e) {
            log.error("设置默认环境失败: {}", environmentName, e);
            return false;
        }
    }

    @Override
    public Optional<NacosEnvironment> getDefaultEnvironment() {
        return environmentsCache.values().stream()
                .filter(env -> Boolean.TRUE.equals(env.getIsDefault()))
                .findFirst();
    }

    @Override
    public String testEnvironmentConnection(String environmentName) {
        try {
            Optional<NacosEnvironment> envOpt = getEnvironment(environmentName);
            if (!envOpt.isPresent()) {
                return "环境不存在: " + environmentName;
            }
            
            NacosEnvironment environment = envOpt.get();
            
            // 构建连接测试报告
            StringBuilder report = new StringBuilder();
            report.append("环境连接测试报告\n");
            report.append("================\n");
            report.append("环境: ").append(environment.getDisplayName() != null ? environment.getDisplayName() : environment.getName()).append("\n");
            report.append("命名空间: ").append(environment.getNamespace()).append("\n");
            report.append("服务器地址: ").append(environment.getServerAddr()).append("\n");
            report.append("测试时间: ").append(LocalDateTime.now()).append("\n\n");
            
            // TODO: 实际的连接测试逻辑
            report.append("连接状态: 待实现\n");
            report.append("响应时间: 待实现\n");
            report.append("服务状态: 待实现\n");
            
            return report.toString();
            
        } catch (Exception e) {
            log.error("测试环境连接失败: {}", environmentName, e);
            return "连接测试失败: " + e.getMessage();
        }
    }

    @Override
    public String initializeEnvironment(String environmentName) {
        try {
            Optional<NacosEnvironment> envOpt = getEnvironment(environmentName);
            if (!envOpt.isPresent()) {
                return "环境不存在: " + environmentName;
            }
            
            StringBuilder result = new StringBuilder();
            result.append("环境初始化报告\n");
            result.append("==============\n");
            result.append("环境: ").append(environmentName).append("\n");
            result.append("初始化时间: ").append(LocalDateTime.now()).append("\n\n");
            
            // TODO: 创建基础配置
            result.append("创建基础配置: 待实现\n");
            result.append("初始化命名空间: 待实现\n");
            result.append("设置默认配置组: 待实现\n");
            
            return result.toString();
            
        } catch (Exception e) {
            log.error("初始化环境失败: {}", environmentName, e);
            return "初始化失败: " + e.getMessage();
        }
    }

    @Override
    public String cleanupEnvironment(String environmentName, boolean confirm) {
        try {
            if (!confirm) {
                return "请确认清理操作，设置confirm=true";
            }
            
            Optional<NacosEnvironment> envOpt = getEnvironment(environmentName);
            if (!envOpt.isPresent()) {
                return "环境不存在: " + environmentName;
            }
            
            StringBuilder result = new StringBuilder();
            result.append("环境清理报告\n");
            result.append("============\n");
            result.append("环境: ").append(environmentName).append("\n");
            result.append("清理时间: ").append(LocalDateTime.now()).append("\n\n");
            
            // TODO: 删除所有配置
            result.append("清理配置: 待实现\n");
            
            return result.toString();
            
        } catch (Exception e) {
            log.error("清理环境失败: {}", environmentName, e);
            return "清理失败: " + e.getMessage();
        }
    }

    @Override
    public String copyEnvironmentConfig(String sourceEnv, String targetEnv, boolean overwrite) {
        try {
            StringBuilder result = new StringBuilder();
            result.append("环境配置复制报告\n");
            result.append("================\n");
            result.append("源环境: ").append(sourceEnv).append("\n");
            result.append("目标环境: ").append(targetEnv).append("\n");
            result.append("覆盖模式: ").append(overwrite ? "是" : "否").append("\n");
            result.append("复制时间: ").append(LocalDateTime.now()).append("\n\n");
            
            // TODO: 实现配置复制逻辑
            result.append("配置复制: 待实现\n");
            
            return result.toString();
            
        } catch (Exception e) {
            log.error("复制环境配置失败: {} -> {}", sourceEnv, targetEnv, e);
            return "复制失败: " + e.getMessage();
        }
    }

    @Override
    public String syncEnvironmentConfig(String sourceEnv, List<String> targetEnvs, 
                                       List<String> includePatterns, List<String> excludePatterns) {
        try {
            StringBuilder result = new StringBuilder();
            result.append("环境配置同步报告\n");
            result.append("================\n");
            result.append("源环境: ").append(sourceEnv).append("\n");
            result.append("目标环境: ").append(String.join(", ", targetEnvs)).append("\n");
            result.append("包含模式: ").append(includePatterns != null ? String.join(", ", includePatterns) : "无").append("\n");
            result.append("排除模式: ").append(excludePatterns != null ? String.join(", ", excludePatterns) : "无").append("\n");
            result.append("同步时间: ").append(LocalDateTime.now()).append("\n\n");
            
            // TODO: 实现配置同步逻辑
            result.append("配置同步: 待实现\n");
            
            return result.toString();
            
        } catch (Exception e) {
            log.error("同步环境配置失败: {}", sourceEnv, e);
            return "同步失败: " + e.getMessage();
        }
    }

    @Override
    public String getEnvironmentStatistics(String environmentName) {
        try {
            Optional<NacosEnvironment> envOpt = getEnvironment(environmentName);
            if (!envOpt.isPresent()) {
                return "环境不存在: " + environmentName;
            }
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("environmentName", environmentName);
            stats.put("namespace", envOpt.get().getNamespace());
            stats.put("status", envOpt.get().getStatus());
            stats.put("enabled", envOpt.get().getEnabled());
            stats.put("isDefault", envOpt.get().getIsDefault());
            stats.put("statisticsTime", LocalDateTime.now());
            
            // TODO: 添加更多统计信息
            stats.put("configCount", "待实现");
            stats.put("groupCount", "待实现");
            
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(stats);
            
        } catch (JsonProcessingException e) {
            log.error("获取环境统计失败: {}", environmentName, e);
            return "获取统计失败: " + e.getMessage();
        }
    }

    @Override
    public String backupEnvironmentConfig(String environmentName, String backupPath) {
        try {
            StringBuilder result = new StringBuilder();
            result.append("环境配置备份报告\n");
            result.append("================\n");
            result.append("环境: ").append(environmentName).append("\n");
            result.append("备份路径: ").append(backupPath).append("\n");
            result.append("备份时间: ").append(LocalDateTime.now()).append("\n\n");
            
            // TODO: 实现配置备份逻辑
            result.append("配置备份: 待实现\n");
            
            return result.toString();
            
        } catch (Exception e) {
            log.error("备份环境配置失败: {}", environmentName, e);
            return "备份失败: " + e.getMessage();
        }
    }

    @Override
    public String restoreEnvironmentConfig(String environmentName, String backupPath, boolean overwrite) {
        try {
            StringBuilder result = new StringBuilder();
            result.append("环境配置恢复报告\n");
            result.append("================\n");
            result.append("环境: ").append(environmentName).append("\n");
            result.append("备份路径: ").append(backupPath).append("\n");
            result.append("覆盖模式: ").append(overwrite ? "是" : "否").append("\n");
            result.append("恢复时间: ").append(LocalDateTime.now()).append("\n\n");
            
            // TODO: 实现配置恢复逻辑
            result.append("配置恢复: 待实现\n");
            
            return result.toString();
            
        } catch (Exception e) {
            log.error("恢复环境配置失败: {}", environmentName, e);
            return "恢复失败: " + e.getMessage();
        }
    }

    @Override
    public String validateEnvironmentConfig(NacosEnvironment environment) {
        try {
            if (environment == null) {
                return "环境对象不能为空";
            }
            
            if (!StringUtils.hasText(environment.getName())) {
                return "环境名称不能为空";
            }
            
            if (!StringUtils.hasText(environment.getNamespace())) {
                return "命名空间不能为空";
            }
            
            if (!StringUtils.hasText(environment.getDefaultGroup())) {
                return "默认配置组不能为空";
            }
            
            // 环境名称格式验证
            if (!environment.getName().matches("^[a-zA-Z][a-zA-Z0-9_-]*$")) {
                return "环境名称格式无效，必须以字母开头，只能包含字母、数字、下划线和短横线";
            }
            
            return "验证通过";
            
        } catch (Exception e) {
            return "验证失败: " + e.getMessage();
        }
    }

    @Override
    public List<String> getEnvironmentNamespaces(String environmentName) {
        try {
            Optional<NacosEnvironment> envOpt = getEnvironment(environmentName);
            if (envOpt.isPresent()) {
                return Arrays.asList(envOpt.get().getNamespace());
            }
            
        } catch (Exception e) {
            log.error("获取环境命名空间失败: {}", environmentName, e);
        }
        
        return new ArrayList<>();
    }

    @Override
    public List<String> getEnvironmentGroups(String environmentName, String namespace) {
        try {
            // TODO: 实现获取配置组逻辑
            log.warn("getEnvironmentGroups功能需要实现");
            
        } catch (Exception e) {
            log.error("获取环境配置组失败: {}", environmentName, e);
        }
        
        return new ArrayList<>();
    }

    @Override
    public String monitorEnvironmentStatus(String environmentName) {
        try {
            Optional<NacosEnvironment> envOpt = getEnvironment(environmentName);
            if (!envOpt.isPresent()) {
                return "环境不存在: " + environmentName;
            }
            
            StringBuilder status = new StringBuilder();
            status.append("环境状态监控\n");
            status.append("============\n");
            status.append("环境: ").append(environmentName).append("\n");
            status.append("监控时间: ").append(LocalDateTime.now()).append("\n\n");
            
            // TODO: 实现状态监控逻辑
            status.append("服务状态: 待实现\n");
            status.append("连接状态: 待实现\n");
            status.append("性能指标: 待实现\n");
            
            return status.toString();
            
        } catch (Exception e) {
            log.error("监控环境状态失败: {}", environmentName, e);
            return "监控失败: " + e.getMessage();
        }
    }

    @Override
    public boolean setEnvironmentVariable(String environmentName, String key, String value) {
        try {
            Optional<NacosEnvironment> envOpt = getEnvironment(environmentName);
            if (!envOpt.isPresent()) {
                return false;
            }
            
            NacosEnvironment environment = envOpt.get();
            if (environment.getVariables() == null) {
                environment.setVariables(new HashMap<>());
            }
            
            environment.getVariables().put(key, value);
            environment.setUpdateTime(LocalDateTime.now());
            
            log.info("环境变量设置成功: {} = {}", key, value);
            return true;
            
        } catch (Exception e) {
            log.error("设置环境变量失败: {}.{}", environmentName, key, e);
            return false;
        }
    }

    @Override
    public Optional<String> getEnvironmentVariable(String environmentName, String key) {
        return getEnvironment(environmentName)
                .map(env -> env.getVariable(key));
    }

    @Override
    public boolean removeEnvironmentVariable(String environmentName, String key) {
        try {
            Optional<NacosEnvironment> envOpt = getEnvironment(environmentName);
            if (!envOpt.isPresent()) {
                return false;
            }
            
            NacosEnvironment environment = envOpt.get();
            if (environment.getVariables() != null) {
                environment.getVariables().remove(key);
                environment.setUpdateTime(LocalDateTime.now());
                return true;
            }
            
        } catch (Exception e) {
            log.error("删除环境变量失败: {}.{}", environmentName, key, e);
        }
        
        return false;
    }

    @Override
    public int setEnvironmentVariables(String environmentName, Map<String, String> variables) {
        int successCount = 0;
        
        for (Map.Entry<String, String> entry : variables.entrySet()) {
            if (setEnvironmentVariable(environmentName, entry.getKey(), entry.getValue())) {
                successCount++;
            }
        }
        
        return successCount;
    }

    // === 私有方法 ===

    /**
     * 初始化预配置的环境
     */
    private void initializePredefinedEnvironments() {
        // 从配置文件中读取预配置的环境
        if (properties.getEnvironments() != null) {
            for (Map.Entry<String, NacosCoreProperties.EnvironmentConfig> entry : properties.getEnvironments().entrySet()) {
                String envName = entry.getKey();
                NacosCoreProperties.EnvironmentConfig envConfig = entry.getValue();
                NacosEnvironment environment = NacosEnvironment.builder()
                        .name(envName)
                        .displayName(envConfig.getDescription())
                        .description(envConfig.getDescription())
                        .namespace(envConfig.getNamespace())
                        .defaultGroup(envConfig.getDefaultGroup())
                        .type(NacosEnvironmentEnum.getByEnv(envName))
                        .status(envConfig.isEnabled() ? EnvironmentStatus.ACTIVE : EnvironmentStatus.INACTIVE)
                        .serverAddr(envConfig.getServerAddr())
                        .configPrefix(envConfig.getConfigPrefix())
                        .accessKey(envConfig.getAccessKey())
                        .secretKey(envConfig.getSecretKey())
                        .enabled(envConfig.isEnabled())
                        .endPoint(envConfig.getEndPoint())
                        .instanceId(envConfig.getInstanceId())
                        .isDefault(false)
                        .createTime(LocalDateTime.now())
                        .updateTime(LocalDateTime.now())
                        .build();
                environmentsCache.put(envName, environment);
            }
        }
        

        // 设置第一个环境为默认环境
        if (!getDefaultEnvironment().isPresent() && !environmentsCache.isEmpty()) {
            String firstEnvName = environmentsCache.keySet().iterator().next();
            setDefaultEnvironment(firstEnvName);
        }
    }


    /**
     * 验证环境创建参数
     */
    private void validateEnvironmentForCreation(NacosEnvironment environment) {
        if (environment == null) {
            throw new IllegalArgumentException("环境对象不能为空");
        }
        
        if (!StringUtils.hasText(environment.getName())) {
            throw new IllegalArgumentException("环境名称不能为空");
        }
    }
}