package com.just.nacos;

import com.just.nacos.model.NacosConfig;
import com.just.nacos.service.NacosConfigService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * NacosConfigService 集成测试类
 * 测试dev环境的发布配置、拉取配置、获取全部配置信息等功能
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class NacosConfigServiceIntegrationTest {

    @Autowired
    private NacosConfigService nacosConfigService;

    private static final String TEST_NAMESPACE = "dev";
    private static final String TEST_GROUP = "dev";
    private static final String TEST_DATA_ID = "test-config.properties";
    private static final String TEST_CONTENT = "# 测试配置文件\n" +
            "app.name=test-application\n" +
            "app.version=1.0.0\n" +
            "app.environment=dev\n" +
            "\n" +
            "# 数据库配置\n" +
            "spring.datasource.url=*************************************" +
            "spring.datasource.username=root\n" +
            "spring.datasource.password=123456\n" +
            "\n" +
            "# Redis配置\n" +
            "spring.redis.host=localhost\n" +
            "spring.redis.port=6379\n" +
            "spring.redis.database=0";

    @Test
    public void testPublishConfig() {
        log.info("========== 测试发布配置 ==========");
        
        // 创建测试配置
        NacosConfig config = NacosConfig.builder()
                .dataId(TEST_DATA_ID)
                .group(TEST_GROUP)
                .namespace(TEST_NAMESPACE)
                .content(TEST_CONTENT)
                .type("properties")
                .environment("dev")
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .status(NacosConfig.ConfigStatus.ACTIVE)
                .build();

        try {
            // 执行发布
            boolean result = nacosConfigService.publishConfig(config);
            
            log.info("配置发布结果: {}", result ? "成功" : "失败");
            log.info("配置详情: dataId={}, group={}, namespace={}", TEST_DATA_ID, TEST_GROUP, TEST_NAMESPACE);
            
            assertTrue(result, "配置发布应该成功");
            
        } catch (Exception e) {
            log.error("配置发布失败", e);
            fail("配置发布不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testGetConfig() {
        log.info("========== 测试获取配置 ==========");
        
        try {
            // 先发布一个配置以确保存在
            testPublishConfig();
            
            // 获取配置
            Optional<NacosConfig> configOpt = nacosConfigService.getConfig(TEST_DATA_ID, TEST_GROUP, TEST_NAMESPACE);
            
            if (configOpt.isPresent()) {
                NacosConfig config = configOpt.get();
                log.info("获取配置成功:");
                log.info("  dataId: {}", config.getDataId());
                log.info("  group: {}", config.getGroup());
                log.info("  namespace: {}", config.getNamespace());
                log.info("  type: {}", config.getType());
                log.info("  status: {}", config.getStatus());
                
                assertEquals(TEST_DATA_ID, config.getDataId());
                assertEquals(TEST_GROUP, config.getGroup());
                assertEquals(TEST_NAMESPACE, config.getNamespace());
                assertNotNull(config.getType());
                
            } else {
                log.warn("未找到指定配置: {}/{}/{}", TEST_NAMESPACE, TEST_GROUP, TEST_DATA_ID);
                // 这里不一定要fail，因为可能是由于MSE API的延迟或权限问题
            }
            
        } catch (Exception e) {
            log.error("获取配置失败", e);
            fail("获取配置不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testListConfigs() {
        log.info("========== 测试获取配置列表 ==========");
        
        try {
            // 获取dev环境的所有配置
            List<NacosConfig> configs = nacosConfigService.listConfigs(1, 20, null, TEST_NAMESPACE);
            
            log.info("获取到配置列表，总数: {}", configs.size());
            
            if (!configs.isEmpty()) {
                log.info("配置列表详情:");
                for (int i = 0; i < Math.min(configs.size(), 10); i++) {
                    NacosConfig config = configs.get(i);
                    log.info("  [{}] dataId: {}, group: {}, type: {}", 
                            i + 1, config.getDataId(), config.getGroup(), config.getType());
                }
                
                if (configs.size() > 10) {
                    log.info("  ... 还有 {} 个配置", configs.size() - 10);
                }
            } else {
                log.info("当前环境暂无配置");
            }
            
            assertNotNull(configs);
            
        } catch (Exception e) {
            log.error("获取配置列表失败", e);
            fail("获取配置列表不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testListConfigsByGroup() {
        log.info("========== 测试按分组获取配置列表 ==========");
        
        try {
            // 获取指定分组的配置
            List<NacosConfig> configs = nacosConfigService.listConfigs(1, 20, TEST_GROUP, TEST_NAMESPACE);
            
            log.info("获取到分组 [{}] 的配置列表，总数: {}", TEST_GROUP, configs.size());
            
            for (NacosConfig config : configs) {
                log.info("  dataId: {}, group: {}, type: {}", 
                        config.getDataId(), config.getGroup(), config.getType());
                assertEquals(TEST_GROUP, config.getGroup());
            }
            
            assertNotNull(configs);
            
        } catch (Exception e) {
            log.error("按分组获取配置列表失败", e);
            fail("按分组获取配置列表不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testConfigExists() {
        log.info("========== 测试检查配置是否存在 ==========");
        
        try {
            // 先发布配置
            testPublishConfig();
            
            // 检查配置是否存在
            boolean exists = nacosConfigService.configExists(TEST_DATA_ID, TEST_GROUP, TEST_NAMESPACE);
            log.info("配置 {}/{}/{} 是否存在: {}", TEST_NAMESPACE, TEST_GROUP, TEST_DATA_ID, exists);
            
            // 检查不存在的配置
            boolean notExists = nacosConfigService.configExists("non-existent-config", TEST_GROUP, TEST_NAMESPACE);
            log.info("不存在的配置检查结果: {}", notExists);
            
            assertFalse(notExists, "不存在的配置应该返回false");
            
        } catch (Exception e) {
            log.error("检查配置存在性失败", e);
            fail("检查配置存在性不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testEnvironmentConnection() {
        log.info("========== 测试环境连接 ==========");
        
        try {
            // 通过获取配置列表来测试连接
            List<NacosConfig> configs = nacosConfigService.listConfigs(1, 1, null, TEST_NAMESPACE);
            log.info("环境连接测试成功，可以正常获取配置列表");
            assertNotNull(configs);
            
        } catch (Exception e) {
            log.error("环境连接测试失败", e);
            fail("环境连接测试不应该失败: " + e.getMessage());
        }
    }
} 