package com.just.upgrade.model;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 升级请求参数
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
public class UpgradeRequest {

    /**
     * 环境名称
     */
    @NotBlank(message = "环境名称不能为空")
    private String environment;
    /**
     * 服务名称
     */
    @NotBlank(message = "服务名称不能为空")
    private String serviceName;
    /**
     * 是否升级注册中心
     */
    @NotNull(message = "是否升级注册中心不能为空")
    private Boolean upgradeRegistry = false;
    /**
     * 是否自动刷新
     */
    @NotNull(message = "是否自动刷新不能为空")
    private Boolean autoRefresh = false;

    /**
     * 是否发布
     */
    @NotNull(message = "是否发布nacos不能为空")
    private Boolean publishMseNacos = true;

    /**
     * 分支名称（可选，默认为master）
     */
    private String branchName = "master";
    /**
     * 升级的Nacos环境变量组名称
     */
    @NotNull(message = "DM组")
    private String dmGroup;
} 