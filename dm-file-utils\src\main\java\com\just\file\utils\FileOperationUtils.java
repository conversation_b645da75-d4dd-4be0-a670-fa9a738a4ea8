package com.just.file.utils;

import com.just.common.constants.ErrorCodes;
import com.just.common.constants.GlobalConstants;
import com.just.common.exception.SystemException;
import com.just.common.utils.StringUtils;
import com.just.file.dto.FileOperationResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 文件操作工具类
 * 统一封装所有文件操作，包括读取、写入、复制、移动、删除等
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
@Slf4j
public final class FileOperationUtils {
    
    private FileOperationUtils() {
        // 私有构造函数，防止实例化
    }
    
    /**
     * 默认字符编码
     */
    private static final Charset DEFAULT_CHARSET = StandardCharsets.UTF_8;
    
    /**
     * 时间戳格式
     */
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
    
    // ======================== 文件读取方法 ========================
    
    /**
     * 读取文件内容为字符串
     * 
     * @param filePath 文件路径
     * @return 文件内容
     * @throws SystemException 读取失败时抛出
     */
    public static String readFileToString(String filePath) {
        return readFileToString(filePath, DEFAULT_CHARSET);
    }
    
    /**
     * 读取文件内容为字符串
     * 
     * @param filePath 文件路径
     * @param charset 字符编码
     * @return 文件内容
     * @throws SystemException 读取失败时抛出
     */
    public static String readFileToString(String filePath, Charset charset) {
        if (StringUtils.isBlank(filePath)) {
            throw new SystemException(ErrorCodes.PARAM_NULL, "文件路径不能为空");
        }
        
        try {
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                throw new SystemException(ErrorCodes.FILE_NOT_FOUND, "文件不存在: " + filePath);
            }
            
            return FileUtils.readFileToString(path.toFile(), charset);
        } catch (IOException e) {
            log.error("读取文件失败: {}", filePath, e);
            throw SystemException.fileOperationError(filePath, "读取", e);
        }
    }
    
    /**
     * 读取文件内容为行列表
     * 
     * @param filePath 文件路径
     * @return 行列表
     * @throws SystemException 读取失败时抛出
     */
    public static List<String> readFileToLines(String filePath) {
        return readFileToLines(filePath, DEFAULT_CHARSET);
    }
    
    /**
     * 读取文件内容为行列表
     * 
     * @param filePath 文件路径
     * @param charset 字符编码
     * @return 行列表
     * @throws SystemException 读取失败时抛出
     */
    public static List<String> readFileToLines(String filePath, Charset charset) {
        if (StringUtils.isBlank(filePath)) {
            throw new SystemException(ErrorCodes.PARAM_NULL, "文件路径不能为空");
        }
        
        try {
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                throw new SystemException(ErrorCodes.FILE_NOT_FOUND, "文件不存在: " + filePath);
            }
            
            return FileUtils.readLines(path.toFile(), charset);
        } catch (IOException e) {
            log.error("读取文件行失败: {}", filePath, e);
            throw SystemException.fileOperationError(filePath, "读取行", e);
        }
    }
    
    /**
     * 从资源路径读取文件内容
     * 
     * @param resourcePath 资源路径
     * @return 文件内容
     * @throws SystemException 读取失败时抛出
     */
    public static String readResourceToString(String resourcePath) {
        return readResourceToString(resourcePath, DEFAULT_CHARSET);
    }
    
    /**
     * 从资源路径读取文件内容
     * 
     * @param resourcePath 资源路径
     * @param charset 字符编码
     * @return 文件内容
     * @throws SystemException 读取失败时抛出
     */
    public static String readResourceToString(String resourcePath, Charset charset) {
        if (StringUtils.isBlank(resourcePath)) {
            throw new SystemException(ErrorCodes.PARAM_NULL, "资源路径不能为空");
        }
        
        try (InputStream inputStream = FileOperationUtils.class.getClassLoader().getResourceAsStream(resourcePath)) {
            if (inputStream == null) {
                throw new SystemException(ErrorCodes.FILE_NOT_FOUND, "资源文件不存在: " + resourcePath);
            }
            
            return IOUtils.toString(inputStream, charset);
        } catch (IOException e) {
            log.error("读取资源文件失败: {}", resourcePath, e);
            throw SystemException.fileOperationError(resourcePath, "读取资源", e);
        }
    }
    
    /**
     * 获取资源文件输入流
     * 
     * @param resourcePath 资源路径
     * @return 输入流
     * @throws SystemException 获取失败时抛出
     */
    public static InputStream getResourceAsStream(String resourcePath) {
        if (StringUtils.isBlank(resourcePath)) {
            throw new SystemException(ErrorCodes.PARAM_NULL, "资源路径不能为空");
        }
        
        InputStream inputStream = FileOperationUtils.class.getClassLoader().getResourceAsStream(resourcePath);
        if (inputStream == null) {
            throw new SystemException(ErrorCodes.FILE_NOT_FOUND, "资源文件不存在: " + resourcePath);
        }
        
        return inputStream;
    }
    
    // ======================== 文件写入方法 ========================
    
    /**
     * 写入字符串到文件
     * 
     * @param filePath 文件路径
     * @param content 文件内容
     * @return 操作结果
     */
    public static FileOperationResult writeStringToFile(String filePath, String content) {
        return writeStringToFile(filePath, content, DEFAULT_CHARSET, false);
    }
    
    /**
     * 写入字符串到文件
     * 
     * @param filePath 文件路径
     * @param content 文件内容
     * @param append 是否追加
     * @return 操作结果
     */
    public static FileOperationResult writeStringToFile(String filePath, String content, boolean append) {
        return writeStringToFile(filePath, content, DEFAULT_CHARSET, append);
    }
    
    /**
     * 写入字符串到文件
     * 
     * @param filePath 文件路径
     * @param content 文件内容
     * @param charset 字符编码
     * @param append 是否追加
     * @return 操作结果
     */
    public static FileOperationResult writeStringToFile(String filePath, String content, Charset charset, boolean append) {
        if (StringUtils.isBlank(filePath)) {
            return FileOperationResult.failure("写入文件", filePath, "文件路径不能为空", "参数验证失败");
        }
        
        if (content == null) {
            content = "";
        }
        
        FileOperationResult result = FileOperationResult.builder()
                .operationType("写入文件")
                .filePath(filePath)
                .build()
                .start();
        
        try {
            Path path = Paths.get(filePath);
            // 确保父目录存在
            createDirectoryIfNotExists(path.getParent().toString());
            
            FileUtils.writeStringToFile(path.toFile(), content, charset, append);
            
            result.setSuccess(true);
            result.setMessage(String.format("成功写入文件: %s (%d字符)", filePath, content.length()));
            result.addProcessedCount(1);
            
            log.debug("成功写入文件: {}, 大小: {} 字符", filePath, content.length());
            
        } catch (IOException e) {
            log.error("写入文件失败: {}", filePath, e);
            result.setSuccess(false);
            result.setMessage("写入文件失败");
            result.setErrorDetail(e.getMessage());
            result.addFailedCount(1);
            result.addFailedFile(filePath);
        }
        
        return result.finish();
    }
    
    /**
     * 写入行列表到文件
     * 
     * @param filePath 文件路径
     * @param lines 行列表
     * @return 操作结果
     */
    public static FileOperationResult writeLinesToFile(String filePath, Collection<String> lines) {
        return writeLinesToFile(filePath, lines, DEFAULT_CHARSET, false);
    }
    
    /**
     * 写入行列表到文件
     * 
     * @param filePath 文件路径
     * @param lines 行列表
     * @param charset 字符编码
     * @param append 是否追加
     * @return 操作结果
     */
    public static FileOperationResult writeLinesToFile(String filePath, Collection<String> lines, Charset charset, boolean append) {
        if (StringUtils.isBlank(filePath)) {
            return FileOperationResult.failure("写入行", filePath, "文件路径不能为空", "参数验证失败");
        }
        
        if (lines == null) {
            lines = Collections.emptyList();
        }
        
        FileOperationResult result = FileOperationResult.builder()
                .operationType("写入行")
                .filePath(filePath)
                .build()
                .start();
        
        try {
            Path path = Paths.get(filePath);
            // 确保父目录存在
            createDirectoryIfNotExists(path.getParent().toString());
            
            FileUtils.writeLines(path.toFile(), charset.name(), lines, append);
            
            result.setSuccess(true);
            result.setMessage(String.format("成功写入文件: %s (%d行)", filePath, lines.size()));
            result.addProcessedCount(lines.size());
            
            log.debug("成功写入文件: {}, 行数: {}", filePath, lines.size());
            
        } catch (IOException e) {
            log.error("写入文件行失败: {}", filePath, e);
            result.setSuccess(false);
            result.setMessage("写入文件行失败");
            result.setErrorDetail(e.getMessage());
            result.addFailedCount(1);
            result.addFailedFile(filePath);
        }
        
        return result.finish();
    }
    
    // ======================== 文件操作方法 ========================
    
    /**
     * 复制文件
     * 
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @return 操作结果
     */
    public static FileOperationResult copyFile(String sourcePath, String targetPath) {
        return copyFile(sourcePath, targetPath, false);
    }
    
    /**
     * 复制文件
     * 
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @param overwrite 是否覆盖
     * @return 操作结果
     */
    public static FileOperationResult copyFile(String sourcePath, String targetPath, boolean overwrite) {
        if (StringUtils.isBlank(sourcePath) || StringUtils.isBlank(targetPath)) {
            return FileOperationResult.failure("复制文件", sourcePath, "文件路径不能为空", "参数验证失败");
        }
        
        FileOperationResult result = FileOperationResult.builder()
                .operationType("复制文件")
                .filePath(sourcePath)
                .build()
                .start();
        
        try {
            Path source = Paths.get(sourcePath);
            Path target = Paths.get(targetPath);
            
            if (!Files.exists(source)) {
                throw new IOException("源文件不存在: " + sourcePath);
            }
            
            // 确保目标目录存在
            createDirectoryIfNotExists(target.getParent().toString());
            
            // 复制选项
            CopyOption[] options = overwrite ? 
                new CopyOption[]{StandardCopyOption.REPLACE_EXISTING} : 
                new CopyOption[]{};
            
            Files.copy(source, target, options);
            
            result.setSuccess(true);
            result.setMessage(String.format("成功复制文件: %s -> %s", sourcePath, targetPath));
            result.addProcessedCount(1);
            
            log.debug("成功复制文件: {} -> {}", sourcePath, targetPath);
            
        } catch (IOException e) {
            log.error("复制文件失败: {} -> {}", sourcePath, targetPath, e);
            result.setSuccess(false);
            result.setMessage("复制文件失败");
            result.setErrorDetail(e.getMessage());
            result.addFailedCount(1);
            result.addFailedFile(sourcePath);
        }
        
        return result.finish();
    }
    
    /**
     * 移动文件
     * 
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @return 操作结果
     */
    public static FileOperationResult moveFile(String sourcePath, String targetPath) {
        return moveFile(sourcePath, targetPath, false);
    }
    
    /**
     * 移动文件
     * 
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @param overwrite 是否覆盖
     * @return 操作结果
     */
    public static FileOperationResult moveFile(String sourcePath, String targetPath, boolean overwrite) {
        if (StringUtils.isBlank(sourcePath) || StringUtils.isBlank(targetPath)) {
            return FileOperationResult.failure("移动文件", sourcePath, "文件路径不能为空", "参数验证失败");
        }
        
        FileOperationResult result = FileOperationResult.builder()
                .operationType("移动文件")
                .filePath(sourcePath)
                .build()
                .start();
        
        try {
            Path source = Paths.get(sourcePath);
            Path target = Paths.get(targetPath);
            
            if (!Files.exists(source)) {
                throw new IOException("源文件不存在: " + sourcePath);
            }
            
            // 确保目标目录存在
            createDirectoryIfNotExists(target.getParent().toString());
            
            // 移动选项
            CopyOption[] options = overwrite ? 
                new CopyOption[]{StandardCopyOption.REPLACE_EXISTING} : 
                new CopyOption[]{};
            
            Files.move(source, target, options);
            
            result.setSuccess(true);
            result.setMessage(String.format("成功移动文件: %s -> %s", sourcePath, targetPath));
            result.addProcessedCount(1);
            
            log.debug("成功移动文件: {} -> {}", sourcePath, targetPath);
            
        } catch (IOException e) {
            log.error("移动文件失败: {} -> {}", sourcePath, targetPath, e);
            result.setSuccess(false);
            result.setMessage("移动文件失败");
            result.setErrorDetail(e.getMessage());
            result.addFailedCount(1);
            result.addFailedFile(sourcePath);
        }
        
        return result.finish();
    }
    
    /**
     * 删除文件
     * 
     * @param filePath 文件路径
     * @return 操作结果
     */
    public static FileOperationResult deleteFile(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            return FileOperationResult.failure("删除文件", filePath, "文件路径不能为空", "参数验证失败");
        }
        
        FileOperationResult result = FileOperationResult.builder()
                .operationType("删除文件")
                .filePath(filePath)
                .build()
                .start();
        
        try {
            Path path = Paths.get(filePath);
            
            if (!Files.exists(path)) {
                result.setSuccess(true);
                result.setMessage("文件不存在，无需删除: " + filePath);
                return result.finish();
            }
            
            Files.delete(path);
            
            result.setSuccess(true);
            result.setMessage("成功删除文件: " + filePath);
            result.addProcessedCount(1);
            
            log.debug("成功删除文件: {}", filePath);
            
        } catch (IOException e) {
            log.error("删除文件失败: {}", filePath, e);
            result.setSuccess(false);
            result.setMessage("删除文件失败");
            result.setErrorDetail(e.getMessage());
            result.addFailedCount(1);
            result.addFailedFile(filePath);
        }
        
        return result.finish();
    }
    
    // ======================== 目录操作方法 ========================
    
    /**
     * 创建目录（如果不存在）
     * 
     * @param directoryPath 目录路径
     * @return 操作结果
     */
    public static FileOperationResult createDirectoryIfNotExists(String directoryPath) {
        if (StringUtils.isBlank(directoryPath)) {
            return FileOperationResult.failure("创建目录", directoryPath, "目录路径不能为空", "参数验证失败");
        }
        
        FileOperationResult result = FileOperationResult.builder()
                .operationType("创建目录")
                .filePath(directoryPath)
                .build()
                .start();
        
        try {
            Path path = Paths.get(directoryPath);
            
            if (Files.exists(path)) {
                if (!Files.isDirectory(path)) {
                    throw new IOException("路径已存在但不是目录: " + directoryPath);
                }
                result.setSuccess(true);
                result.setMessage("目录已存在: " + directoryPath);
                return result.finish();
            }
            
            Files.createDirectories(path);
            
            result.setSuccess(true);
            result.setMessage("成功创建目录: " + directoryPath);
            result.addProcessedCount(1);
            
            log.debug("成功创建目录: {}", directoryPath);
            
        } catch (IOException e) {
            log.error("创建目录失败: {}", directoryPath, e);
            result.setSuccess(false);
            result.setMessage("创建目录失败");
            result.setErrorDetail(e.getMessage());
            result.addFailedCount(1);
            result.addFailedFile(directoryPath);
        }
        
        return result.finish();
    }
    
    /**
     * 删除目录及其内容
     * 
     * @param directoryPath 目录路径
     * @return 操作结果
     */
    public static FileOperationResult deleteDirectory(String directoryPath) {
        if (StringUtils.isBlank(directoryPath)) {
            return FileOperationResult.failure("删除目录", directoryPath, "目录路径不能为空", "参数验证失败");
        }
        
        FileOperationResult result = FileOperationResult.builder()
                .operationType("删除目录")
                .filePath(directoryPath)
                .build()
                .start();
        
        try {
            Path path = Paths.get(directoryPath);
            
            if (!Files.exists(path)) {
                result.setSuccess(true);
                result.setMessage("目录不存在，无需删除: " + directoryPath);
                return result.finish();
            }
            
            if (!Files.isDirectory(path)) {
                throw new IOException("路径不是目录: " + directoryPath);
            }
            
            FileUtils.deleteDirectory(path.toFile());
            
            result.setSuccess(true);
            result.setMessage("成功删除目录: " + directoryPath);
            result.addProcessedCount(1);
            
            log.debug("成功删除目录: {}", directoryPath);
            
        } catch (IOException e) {
            log.error("删除目录失败: {}", directoryPath, e);
            result.setSuccess(false);
            result.setMessage("删除目录失败");
            result.setErrorDetail(e.getMessage());
            result.addFailedCount(1);
            result.addFailedFile(directoryPath);
        }
        
        return result.finish();
    }
    
    /**
     * 列出目录中的所有文件
     * 
     * @param directoryPath 目录路径
     * @return 文件列表
     * @throws SystemException 操作失败时抛出
     */
    public static List<String> listFiles(String directoryPath) {
        return listFiles(directoryPath, null);
    }
    
    /**
     * 列出目录中的文件（支持过滤）
     * 
     * @param directoryPath 目录路径
     * @param extension 文件扩展名过滤（可为null）
     * @return 文件列表
     * @throws SystemException 操作失败时抛出
     */
    public static List<String> listFiles(String directoryPath, String extension) {
        if (StringUtils.isBlank(directoryPath)) {
            throw new SystemException(ErrorCodes.PARAM_NULL, "目录路径不能为空");
        }
        
        try {
            Path path = Paths.get(directoryPath);
            
            if (!Files.exists(path)) {
                throw new SystemException(ErrorCodes.DIRECTORY_NOT_FOUND, "目录不存在: " + directoryPath);
            }
            
            if (!Files.isDirectory(path)) {
                throw new SystemException(ErrorCodes.PARAM_ERROR, "路径不是目录: " + directoryPath);
            }
            
            try (Stream<Path> stream = Files.list(path)) {
                return stream
                        .filter(Files::isRegularFile)
                        .filter(p -> extension == null || p.toString().toLowerCase().endsWith("." + extension.toLowerCase()))
                        .map(Path::toString)
                        .collect(Collectors.toList());
            }
            
        } catch (IOException e) {
            log.error("列出文件失败: {}", directoryPath, e);
            throw SystemException.fileOperationError(directoryPath, "列出文件", e);
        }
    }
    
    // ======================== 备份方法 ========================
    
    /**
     * 备份文件
     * 
     * @param filePath 文件路径
     * @return 操作结果（data字段包含备份文件路径）
     */
    public static FileOperationResult backupFile(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            return FileOperationResult.failure("备份文件", filePath, "文件路径不能为空", "参数验证失败");
        }
        
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
        String backupPath = filePath + GlobalConstants.BACKUP_FILE_SUFFIX + "_" + timestamp;
        
        FileOperationResult result = copyFile(filePath, backupPath);
        result.setOperationType("备份文件");
        
        if (result.isSuccess()) {
            result.setMessage(String.format("成功备份文件: %s -> %s", filePath, backupPath));
            result.setData(backupPath);
        }
        
        return result;
    }
    
    // ======================== 文件信息方法 ========================
    
    /**
     * 检查文件是否存在
     * 
     * @param filePath 文件路径
     * @return 是否存在
     */
    public static boolean exists(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            return false;
        }
        return Files.exists(Paths.get(filePath));
    }
    
    /**
     * 检查是否为文件
     * 
     * @param filePath 文件路径
     * @return 是否为文件
     */
    public static boolean isFile(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            return false;
        }
        return Files.isRegularFile(Paths.get(filePath));
    }
    
    /**
     * 检查是否为目录
     * 
     * @param directoryPath 目录路径
     * @return 是否为目录
     */
    public static boolean isDirectory(String directoryPath) {
        if (StringUtils.isBlank(directoryPath)) {
            return false;
        }
        return Files.isDirectory(Paths.get(directoryPath));
    }
    
    /**
     * 获取文件大小
     * 
     * @param filePath 文件路径
     * @return 文件大小（字节）
     * @throws SystemException 操作失败时抛出
     */
    public static long getFileSize(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            throw new SystemException(ErrorCodes.PARAM_NULL, "文件路径不能为空");
        }
        
        try {
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                throw new SystemException(ErrorCodes.FILE_NOT_FOUND, "文件不存在: " + filePath);
            }
            
            return Files.size(path);
        } catch (IOException e) {
            log.error("获取文件大小失败: {}", filePath, e);
            throw SystemException.fileOperationError(filePath, "获取大小", e);
        }
    }
    
    /**
     * 获取文件最后修改时间
     * 
     * @param filePath 文件路径
     * @return 最后修改时间
     * @throws SystemException 操作失败时抛出
     */
    public static LocalDateTime getLastModifiedTime(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            throw new SystemException(ErrorCodes.PARAM_NULL, "文件路径不能为空");
        }
        
        try {
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                throw new SystemException(ErrorCodes.FILE_NOT_FOUND, "文件不存在: " + filePath);
            }
            
            BasicFileAttributes attrs = Files.readAttributes(path, BasicFileAttributes.class);
            return LocalDateTime.ofInstant(attrs.lastModifiedTime().toInstant(), 
                    java.time.ZoneId.systemDefault());
        } catch (IOException e) {
            log.error("获取文件修改时间失败: {}", filePath, e);
            throw SystemException.fileOperationError(filePath, "获取修改时间", e);
        }
    }
    
    // ======================== 临时文件方法 ========================
    
    /**
     * 创建临时文件
     * 
     * @param prefix 文件名前缀
     * @param suffix 文件名后缀
     * @return 临时文件路径
     * @throws SystemException 创建失败时抛出
     */
    public static String createTempFile(String prefix, String suffix) {
        try {
            if (StringUtils.isBlank(prefix)) {
                prefix = GlobalConstants.TEMP_FILE_PREFIX;
            }
            
            Path tempFile = Files.createTempFile(prefix, suffix);
            String tempFilePath = tempFile.toString();
            
            log.debug("创建临时文件: {}", tempFilePath);
            return tempFilePath;
        } catch (IOException e) {
            log.error("创建临时文件失败", e);
            throw SystemException.fileOperationError("临时文件", "创建", e);
        }
    }
    
    /**
     * 创建临时目录
     * 
     * @param prefix 目录名前缀
     * @return 临时目录路径
     * @throws SystemException 创建失败时抛出
     */
    public static String createTempDirectory(String prefix) {
        try {
            if (StringUtils.isBlank(prefix)) {
                prefix = GlobalConstants.TEMP_FILE_PREFIX;
            }
            
            Path tempDir = Files.createTempDirectory(prefix);
            String tempDirPath = tempDir.toString();
            
            log.debug("创建临时目录: {}", tempDirPath);
            return tempDirPath;
        } catch (IOException e) {
            log.error("创建临时目录失败", e);
            throw SystemException.fileOperationError("临时目录", "创建", e);
        }
    }
}