package com.just.template.model;

import lombok.Builder;
import lombok.Data;

/**
 * Application Nacos配置模板参数模型
 * 对应 application.ftl 模板
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
@Builder
public class PomConfigModel {

    /**
     * 服务名称
     * 对应模板中的 ${serviceName}
     */
    private String version;

    /**
     * 服务名称
     * 对应模板中的 ${serviceName}
     */
    private Boolean register = false;


    /**
     * 验证必需的参数
     *
     * @throws IllegalArgumentException 如果缺少必需参数
     */
    public void validate() {
        if (version == null || version.trim().isEmpty()) {
            throw new IllegalArgumentException("version is required for Pom configuration");
        }
    }
}