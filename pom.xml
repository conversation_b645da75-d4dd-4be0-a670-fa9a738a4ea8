﻿<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.just</groupId>
    <artifactId>dm-auto-utils</artifactId>
    <version>2.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>DM Auto Utils - 重构</name>
    <description>DM自动化运维工具重构版</description>

    <modules>
        <module>dm-common-core</module>
        <module>dm-file-utils</module>
        <module>dm-template-core</module>
        <module>dm-git-core</module>
        <module>dm-nacos-core</module>
        <module>dm-upgrade-center</module>
        <!--<module>dm-web-api</module>
        <module>dm-web-console</module>
        <module>dm-application</module>-->
    </modules>

    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        
        <!-- 版本管理 -->
        <spring.boot.version>2.7.5</spring.boot.version>
        <spring.cloud.version>2021.0.5</spring.cloud.version>
        <spring.cloud.alibaba.version>2021.0.5.0</spring.cloud.alibaba.version>
        <spring.shell.version>2.1.9</spring.shell.version>
        <nacos.client.version>2.2.0</nacos.client.version>
        <jgit.version>5.13.0.202109080827-r</jgit.version>
        <freemarker.version>2.3.31</freemarker.version>
        <hutool.version>5.8.25</hutool.version>
        <lombok.version>1.18.24</lombok.version>
        <poi.version>4.1.2</poi.version>
        <aliyun.mse.version>7.18.1</aliyun.mse.version>
        <gitlab4j.version>4.19.0</gitlab4j.version>
        <commons.lang3.version>3.12.0</commons.lang3.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot Dependencies -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <!-- Spring Cloud Dependencies -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <!-- Spring Cloud Alibaba -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <!-- 内部模块依赖 -->
            <dependency>
                <groupId>com.just</groupId>
                <artifactId>dm-common-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.just</groupId>
                <artifactId>dm-file-utils</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.just</groupId>
                <artifactId>dm-template-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.just</groupId>
                <artifactId>dm-nacos-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.just</groupId>
                <artifactId>dm-git-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.just</groupId>
                <artifactId>dm-upgrade-center</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.just</groupId>
                <artifactId>dm-web-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.just</groupId>
                <artifactId>dm-web-console</artifactId>
                <version>${project.version}</version>
            </dependency>
            
            <!-- 第三方依赖 -->
            <dependency>
                <groupId>org.springframework.shell</groupId>
                <artifactId>spring-shell-starter</artifactId>
                <version>${spring.shell.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${nacos.client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jgit</groupId>
                <artifactId>org.eclipse.jgit</artifactId>
                <version>${jgit.version}</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-poi</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>mse20190531</artifactId>
                <version>${aliyun.mse.version}</version>
            </dependency>
            <dependency>
                <groupId>org.gitlab4j</groupId>
                <artifactId>gitlab4j-api</artifactId>
                <version>${gitlab4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons.lang3.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>nexus</id>
            <name>Team Nexus Repository</name>
            <url>http://nexus.innodealing.com/nexus/content/repositories/thirdparty/</url>
        </repository>
    </repositories>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
