package com.just.git.service;

import com.just.git.config.GitConfig;
import com.just.git.exception.GitErrorCode;
import com.just.git.exception.GitOperationException;
import com.just.git.model.*;
import com.just.git.utils.GitUtils;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jgit.api.*;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.lib.Ref;
import org.eclipse.jgit.lib.Repository;
import org.eclipse.jgit.storage.file.FileRepositoryBuilder;
import org.eclipse.jgit.transport.CredentialsProvider;
import org.eclipse.jgit.transport.PushResult;
import org.eclipse.jgit.transport.RefSpec;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 基于JGit的Git操作服务实现
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@Service
public class JGitOperationServiceImpl implements GitOperationService {
    
    private final GitConfig gitConfig;
    
    public JGitOperationServiceImpl(GitConfig gitConfig) {
        this.gitConfig = gitConfig;
    }
    
    @Override
    public GitRepository cloneRepository(GitCloneRequest request) throws GitOperationException {
        log.info("开始克隆仓库: {}", request);
        
        // 验证请求
        request.validate();
        try {
            // 确保本地目录存在
            if (!GitUtils.ensureDirectoryExists(request.getLocalPath().getParent())) {
                throw new GitOperationException(
                    GitErrorCode.CLONE_FAILED,
                    "CLONE",
                    "无法创建本地目录: " + request.getLocalPath().getParent()
                );
            }
            
            // 获取有效的仓库URL
            String repositoryUrl = request.getEffectiveRepositoryUrl(gitConfig.getBaseUrl());
            log.info("克隆URL: {}", repositoryUrl);
            
            // 创建克隆命令
            CloneCommand cloneCommand = Git.cloneRepository()
                    .setURI(repositoryUrl)
                    .setDirectory(request.getLocalPath().toFile())
                    .setBranch(request.getBranch())
                    .setCloneAllBranches(request.isCloneAllBranches());
            
            // 设置认证信息
            GitCredentials credentials = request.getCredentials() != null ? 
                    request.getCredentials() : gitConfig.getDefaultCredentials();
            
            if (credentials != null) {
                CredentialsProvider credentialsProvider = GitUtils.createCredentialsProvider(credentials);
                if (credentialsProvider != null) {
                    cloneCommand.setCredentialsProvider(credentialsProvider);
                }
            }
            
            // 执行克隆
            Git git = cloneCommand.call();
            
            // 创建仓库实例
            GitRepository repository = GitRepository.of(git, request.getLocalPath(), repositoryUrl);
            repository.setCurrentBranch(request.getBranch());
            repository.setCredentials(credentials);
            
            log.info("仓库克隆成功: {}", request.getLocalPath());
            return repository;
            
        } catch (GitAPIException e) {
            log.error("克隆仓库失败", e);
            throw new GitOperationException(
                GitErrorCode.CLONE_FAILED,
                "CLONE",
                "克隆仓库失败: " + e.getMessage(),
                e
            );
        } catch (Exception e) {
            log.error("克隆仓库时发生未知错误", e);
            throw new GitOperationException(
                GitErrorCode.UNKNOWN_ERROR,
                "CLONE",
                "克隆仓库时发生未知错误: " + e.getMessage(),
                e
            );
        }
    }
    
    @Override
    public GitOperationResult branchOperation(GitRepository repository, GitBranchRequest request) 
            throws GitOperationException {
        log.info("执行分支操作: {}", request);
        
        request.validate();
        
        GitOperationResult result = GitOperationResult.builder()
                .operation("BRANCH_" + request.getOperation().name())
                .startTime(LocalDateTime.now())
                .repository(repository)
                .build();
        
        try {
            Git git = repository.getGit();
            String beforeBranch = getCurrentBranch(repository);
            
            switch (request.getOperation()) {
                case SWITCH:
                    git.checkout().setName(request.getBranchName()).call();
                    break;
                    
                case CREATE:
                    git.branchCreate()
                            .setName(request.getBranchName())
                            .setStartPoint(request.getBaseBranch())
                            .setForce(request.isForce())
                            .call();
                    break;
                    
                case CREATE_AND_SWITCH:
                    git.checkout()
                            .setCreateBranch(true)
                            .setName(request.getBranchName())
                            .setStartPoint(request.getBaseBranch())
                            .call();
                    break;
                    
                case DELETE:
                    git.branchDelete()
                            .setBranchNames(request.getBranchName())
                            .setForce(request.isForce())
                            .call();
                    break;
                    
                case MERGE:
                    // 合并操作需要更复杂的实现
                    throw new GitOperationException(
                        GitErrorCode.UNKNOWN_ERROR,
                        "BRANCH_MERGE",
                        "合并操作暂未实现"
                    );
                    
                default:
                    throw new GitOperationException(
                        GitErrorCode.UNKNOWN_ERROR,
                        "BRANCH_OPERATION",
                        "不支持的分支操作: " + request.getOperation()
                    );
            }
            
            String afterBranch = getCurrentBranch(repository);
            repository.setCurrentBranch(afterBranch);
            
            result.setSuccess(true);
            result.setMessage("分支操作成功");
            result.withBranchInfo(beforeBranch, afterBranch);
            result.markEnd();
            
            log.info("分支操作完成: {} -> {}", beforeBranch, afterBranch);
            return result;
            
        } catch (GitAPIException e) {
            log.error("分支操作失败", e);
            throw new GitOperationException(
                GitErrorCode.BRANCH_SWITCH_FAILED,
                "BRANCH_" + request.getOperation().name(),
                "分支操作失败: " + e.getMessage(),
                e
            );
        }
    }
    
    @Override
    public GitOperationResult commit(GitRepository repository, GitCommitRequest request) 
            throws GitOperationException {
        log.info("执行提交操作: {}", request.getMessage());
        
        request.validate();
        
        try {
            Git git = repository.getGit();
            
            // 添加文件到暂存区
            AddCommand addCommand = git.add();
            if (request.isAddAll()) {
                addCommand.addFilepattern(".");
            } else if (request.getFilesToAdd() != null) {
                for (String file : request.getFilesToAdd()) {
                    addCommand.addFilepattern(file);
                }
            }
            addCommand.call();
            
            // 执行提交
            CommitCommand commitCommand = git.commit()
                    .setMessage(request.getMessage())
                    .setAllowEmpty(request.isAllowEmpty())
                    .setAmend(request.isAmend());
            
            // 设置提交者信息
            if (request.getAuthorName() != null) {
                commitCommand.setAuthor(request.getAuthorName(), request.getAuthorEmail());
            }
            
            org.eclipse.jgit.revwalk.RevCommit commit = commitCommand.call();
            
            GitOperationResult result = GitOperationResult.success(
                "COMMIT", 
                "代码提交成功: " + request.getMessage()
            );
            result.setCommitId(commit.getName());
            
            log.info("代码提交成功: {}", commit.getName());
            return result;
            
        } catch (GitAPIException e) {
            log.error("代码提交失败", e);
            throw new GitOperationException(
                GitErrorCode.COMMIT_FAILED,
                "COMMIT",
                "代码提交失败: " + e.getMessage(),
                e
            );
        }
    }
    
    @Override
    public GitOperationResult push(GitRepository repository, String branchName, boolean force) 
            throws GitOperationException {
        log.info("推送代码到分支: {}", branchName);
        
        try {
            Git git = repository.getGit();
            
            PushCommand pushCommand = git.push()
                    .setRemote("origin")
                    .setForce(force);
            
            if (branchName != null) {
                RefSpec refSpec = new RefSpec("refs/heads/" + branchName + ":refs/heads/" + branchName);
                pushCommand.setRefSpecs(refSpec);
            }
            
            // 设置认证信息
            if (repository.getCredentials() != null) {
                CredentialsProvider credentialsProvider = 
                        GitUtils.createCredentialsProvider(repository.getCredentials());
                if (credentialsProvider != null) {
                    pushCommand.setCredentialsProvider(credentialsProvider);
                }
            }
            
            Iterable<PushResult> results = pushCommand.call();
            
            GitOperationResult result = GitOperationResult.success(
                "PUSH", 
                "代码推送成功到分支: " + branchName
            );
            
            log.info("代码推送成功: {}", branchName);
            return result;
            
        } catch (GitAPIException e) {
            log.error("代码推送失败", e);
            throw new GitOperationException(
                GitErrorCode.PUSH_FAILED,
                "PUSH",
                "代码推送失败: " + e.getMessage(),
                e
            );
        }
    }
    
    @Override
    public GitOperationResult pull(GitRepository repository) throws GitOperationException {
        log.info("拉取远程代码");
        
        try {
            Git git = repository.getGit();
            
            PullCommand pullCommand = git.pull();
            
            // 设置认证信息
            if (repository.getCredentials() != null) {
                CredentialsProvider credentialsProvider = 
                        GitUtils.createCredentialsProvider(repository.getCredentials());
                if (credentialsProvider != null) {
                    pullCommand.setCredentialsProvider(credentialsProvider);
                }
            }
            
            PullResult pullResult = pullCommand.call();
            
            GitOperationResult result = GitOperationResult.success(
                "PULL", 
                "代码拉取成功"
            );
            
            log.info("代码拉取成功");
            return result;
            
        } catch (GitAPIException e) {
            log.error("代码拉取失败", e);
            throw new GitOperationException(
                GitErrorCode.PULL_FAILED,
                "PULL",
                "代码拉取失败: " + e.getMessage(),
                e
            );
        }
    }
    
    @Override
    public GitRepository.RepositoryStatus getRepositoryStatus(GitRepository repository) 
            throws GitOperationException {
        try {
            Git git = repository.getGit();
            Status status = git.status().call();
            
            if (status.hasUncommittedChanges()) {
                if (status.getConflicting().size() > 0) {
                    return GitRepository.RepositoryStatus.CONFLICTED;
                } else if (status.getChanged().size() > 0 || status.getAdded().size() > 0 || 
                          status.getRemoved().size() > 0) {
                    return GitRepository.RepositoryStatus.STAGED;
                } else {
                    return GitRepository.RepositoryStatus.MODIFIED;
                }
            } else {
                return GitRepository.RepositoryStatus.CLEAN;
            }
            
        } catch (GitAPIException e) {
            log.error("获取仓库状态失败", e);
            throw new GitOperationException(
                GitErrorCode.REPOSITORY_STATE_ERROR,
                "GET_STATUS",
                "获取仓库状态失败: " + e.getMessage(),
                e
            );
        }
    }
    
    @Override
    public String getCurrentBranch(GitRepository repository) throws GitOperationException {
        try {
            return repository.getGit().getRepository().getBranch();
        } catch (IOException e) {
            log.error("获取当前分支失败", e);
            throw new GitOperationException(
                GitErrorCode.REPOSITORY_STATE_ERROR,
                "GET_CURRENT_BRANCH",
                "获取当前分支失败: " + e.getMessage(),
                e
            );
        }
    }
    
    @Override
    public List<String> listBranches(GitRepository repository, boolean includeRemote) 
            throws GitOperationException {
        try {
            Git git = repository.getGit();
            ListBranchCommand listCommand = git.branchList();
            if (includeRemote) {
                listCommand.setListMode(ListBranchCommand.ListMode.ALL);
            }
            List<Ref> branches = listCommand.call();
            
            return branches.stream()
                    .map(ref -> ref.getName().replaceFirst("^refs/(heads|remotes)/", ""))
                    .collect(Collectors.toList());
            
        } catch (GitAPIException e) {
            log.error("获取分支列表失败", e);
            throw new GitOperationException(
                GitErrorCode.REPOSITORY_STATE_ERROR,
                "LIST_BRANCHES",
                "获取分支列表失败: " + e.getMessage(),
                e
            );
        }
    }
    
    @Override
    public boolean hasUncommittedChanges(GitRepository repository) throws GitOperationException {
        try {
            Git git = repository.getGit();
            Status status = git.status().call();
            return status.hasUncommittedChanges();
        } catch (GitAPIException e) {
            log.error("检查仓库状态失败", e);
            throw new GitOperationException(
                GitErrorCode.REPOSITORY_STATE_ERROR,
                "CHECK_CHANGES",
                "检查仓库状态失败: " + e.getMessage(),
                e
            );
        }
    }
    
    @Override
    public List<String> getUncommittedFiles(GitRepository repository) throws GitOperationException {
        try {
            Git git = repository.getGit();
            Status status = git.status().call();
            
            List<String> uncommittedFiles = new ArrayList<>();
            uncommittedFiles.addAll(status.getModified());
            uncommittedFiles.addAll(status.getAdded());
            uncommittedFiles.addAll(status.getRemoved());
            uncommittedFiles.addAll(status.getUntracked());
            
            return uncommittedFiles;
        } catch (GitAPIException e) {
            log.error("获取未提交文件列表失败", e);
            throw new GitOperationException(
                GitErrorCode.REPOSITORY_STATE_ERROR,
                "GET_UNCOMMITTED_FILES",
                "获取未提交文件列表失败: " + e.getMessage(),
                e
            );
        }
    }
    
    @Override
    public GitOperationResult discardChanges(GitRepository repository) throws GitOperationException {
        try {
            Git git = repository.getGit();
            
            // 重置所有文件到HEAD
            git.reset().setMode(ResetCommand.ResetType.HARD).call();
            
            // 清理未跟踪的文件
            git.clean().setCleanDirectories(true).call();
            
            return GitOperationResult.success("DISCARD_CHANGES", "成功丢弃所有未提交的更改");
            
        } catch (GitAPIException e) {
            log.error("丢弃更改失败", e);
            throw new GitOperationException(
                GitErrorCode.REPOSITORY_STATE_ERROR,
                "DISCARD_CHANGES",
                "丢弃更改失败: " + e.getMessage(),
                e
            );
        }
    }
    
    @Override
    public void closeRepository(GitRepository repository) {
        if (repository != null) {
            GitUtils.closeGitSafely(repository.getGit());
        }
    }
    
    @Override
    public List<String> validateRepository(GitRepository repository) {
        if (repository == null) {
            return java.util.Collections.singletonList("Git仓库实例为空");
        }
        return repository.validate();
    }
} 