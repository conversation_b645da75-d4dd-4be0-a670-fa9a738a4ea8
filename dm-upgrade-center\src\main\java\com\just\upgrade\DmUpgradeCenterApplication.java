package com.just.upgrade;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * DM升级中心应用主启动类
 * 
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
@SpringBootApplication(scanBasePackages = {
    "com.just.upgrade",
    "com.just.file",
    "com.just.common",
    "com.just.template"
})
@EnableCaching
@EnableAsync
@ConfigurationPropertiesScan(basePackages = {
    "com.just.file.config",
    "com.just.upgrade.config"
})
public class DmUpgradeCenterApplication {

    public static void main(String[] args) {
        SpringApplication.run(DmUpgradeCenterApplication.class, args);
    }
} 