# Vue 3前端集成dm-upgrade-center任务

## 任务概述
创建基于Vue 3 + Ant Design Vue的Web前端模块，集成dm-upgrade-center模块的REST API接口。

## 技术栈
- 前端框架：Vue 3 (Composition API + `<script setup>`)
- UI组件库：Ant Design Vue
- HTTP客户端：axios
- 构建工具：Vite

## 功能模块

### 1. MSE Nacos主模块
包含两个子功能页面：

#### 1.1 Nacos服务升级页面
- **接口对接**：`POST /api/upgrade/nacos/upgrade`
- **页面布局**：上下布局（筛选条件 + 升级信息展示）
- **主要功能**：
  - 升级参数表单（带搜索的下拉框）
  - 实时升级进度显示
  - 升级日志展示

#### 1.2 Nacos配置推送页面
- **接口对接**：
  - 获取配置：`POST /api/upgrade/nacos/config/single`
  - 搜索配置：`POST /api/upgrade/nacos/config/search`
  - 发布配置：相关发布接口
- **主要功能**：
  - 配置查询和搜索
  - 配置内容编辑
  - 配置发布操作

## 执行计划

### 第一阶段：项目初始化
- [x] 创建Vue项目结构
- [x] 安装依赖包

### 第二阶段：API接口封装
- [x] 创建API服务层
- [x] 配置axios拦截器

### 第三阶段：页面组件开发
- [x] 创建Nacos服务升级页面
- [x] 创建Nacos配置推送页面

### 第四阶段：路由和布局
- [x] 配置路由系统
- [x] 创建主布局组件

### 第五阶段：开发配置和测试
- [x] 配置开发环境
- [x] 创建启动脚本和文档

## 环境要求

### 必需软件
1. **Node.js** >= 16.0.0
   - 下载地址：https://nodejs.org/
   - 推荐使用LTS版本

2. **npm** >= 7.0.0 (随Node.js一起安装)

### 验证环境
```bash
node --version    # 应显示 v16.x.x 或更高版本
npm --version     # 应显示 7.x.x 或更高版本
```

## API接口分析

### UpgradeRequest参数
```java
{
  "environment": "string",      // 环境名称（必填）
  "serviceName": "string",      // 服务名称（必填）
  "upgradeRegistry": boolean,   // 是否升级注册中心
  "autoRefresh": boolean,       // 是否自动刷新
  "publishMseNacos": boolean,   // 是否发布nacos
  "branchName": "string",       // 分支名称（默认master）
  "dmGroup": "string"           // DM组（必填）
}
```

### NacosSearchRequest参数
```java
{
  "environment": "string",      // 环境名称（必填）
  "dataId": "string",          // 配置ID
  "group": "string",           // 分组名称（默认DEFAULT_GROUP）
  "namespace": "string",       // 命名空间
  "fileName": "string",        // 文件名称（模糊搜索）
  "keyword": "string",         // 关键词
  "pageNum": number,           // 页码（默认1）
  "pageSize": number           // 页大小（默认50）
}
```

### NacosConfig响应
```java
{
  "dataId": "string",          // 配置ID
  "group": "string",           // 配置组
  "namespace": "string",       // 命名空间
  "content": "string",         // 配置内容
  "type": "string",            // 配置类型
  "environment": "string",     // 环境名称
  "appName": "string",         // 应用名称
  "status": "string",          // 配置状态
  "createTime": "datetime",    // 创建时间
  "updateTime": "datetime"     // 修改时间
}
```

## 开发要求
1. 严格遵循Ant Design Vue设计规范
2. 实现响应式布局
3. 添加loading状态和错误处理
4. 代码结构清晰，组件化开发
5. 集成现有API接口，确保数据交互正确

## 交付物
- 完整的Vue项目结构
- 两个功能页面的完整实现
- API接口调用封装
- 基本的路由配置
- 项目启动说明文档
