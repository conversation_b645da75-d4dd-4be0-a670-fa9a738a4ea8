package com.just.nacos.service;

import com.aliyun.mse20190531.Client;
import com.aliyun.mse20190531.models.GetNacosConfigResponse;
import com.aliyun.mse20190531.models.ListNacosConfigsResponseBody;
import com.aliyun.mse20190531.models.ListTagResourcesRequest;
import com.aliyun.teautil.models.RuntimeOptions;
import com.just.nacos.enums.NacosEnvironmentEnum;
import com.just.nacos.model.NacosEnvironment;

import java.util.List;
import java.util.Map;

/**
 * 阿里云MSE服务接口
 * <p>提供阿里云MSE服务的客户端创建及API调用功能</p>
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
public interface AliyunMseActionService {
    
    // ===== 客户端管理 =====
    
    /**
     * 创建MSE客户端
     * 
     * @param accessKeyId 访问密钥ID
     * @param accessKeySecret 访问密钥密钥
     * @param endpoint 服务端点，默认为 "mse.cn-shanghai.aliyuncs.com"
     * @return MSE客户端实例
     * @throws Exception 创建客户端异常
     */
    Client createClient(String accessKeyId, String accessKeySecret, String endpoint) throws Exception;
    
    /**
     * 基于环境配置创建MSE客户端
     * 
     * @param environment Nacos环境配置
     * @return MSE客户端实例
     * @throws Exception 创建客户端异常
     */
    Client createClient(NacosEnvironment environment) throws Exception;
    
    /**
     * 使用环境枚举创建MSE客户端
     * 
     * @param nacosEnvironmentEnum 环境枚举
     * @return MSE客户端实例
     * @throws Exception 创建客户端异常
     */
    Client createClientFromEnv(NacosEnvironmentEnum nacosEnvironmentEnum) throws Exception;
    
    // ===== 标签资源管理 =====
    
    /**
     * 查询标签资源列表
     * 
     * @param environment Nacos环境配置
     * @return 标签资源响应
     * @throws Exception API调用异常
     */
    Object listTagResources(NacosEnvironment environment) throws Exception;
    
    /**
     * 带自定义选项查询标签资源列表
     * 
     * @param request 查询请求
     * @param runtime 运行时选项
     * @return 标签资源响应
     * @throws Exception API调用异常
     */
    Object listTagResourcesWithOptions(ListTagResourcesRequest request, RuntimeOptions runtime) throws Exception;
    
    // ===== 配置管理 =====

    /**
     * 获取指定命名空间下的所有配置
     */
    GetNacosConfigResponse getNacosConfig(NacosEnvironment environment, String dataId, String group, String namespace) throws Exception;
    
    /**
     * 基于环境配置查询Nacos配置列表
     * 
     * @param environment Nacos环境配置
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 配置列表响应
     * @throws Exception API调用异常
     */
    ListNacosConfigsResponseBody listNacosConfigs(NacosEnvironment environment,String group, Integer pageNum, Integer pageSize) throws Exception;
    
    /**
     * 查询配置列表（兼容旧版本接口）
     * 
     * @param instanceId 实例ID
     * @param regionId 区域ID
     * @param namespace 命名空间
     * @param group 分组
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 配置列表响应
     * @throws Exception API调用异常
     */
    ListNacosConfigsResponseBody listNacosConfigs(String instanceId, String regionId, String namespace, String group,
                                                  Integer pageNum, Integer pageSize) throws Exception;
    
    /**
     * 创建配置（基于环境配置）
     * 
     * @param environment Nacos环境配置
     * @param dataId 配置ID
     * @param content 配置内容
     * @param type 配置类型（properties/json/xml/yaml/text）
     * @return 创建结果
     * @throws Exception API调用异常
     */
    Object createNacosConfig(NacosEnvironment environment, String dataId, String content, String type) throws Exception;

    /**
     * 创建配置（基于环境配置）
     *
     * @param environment Nacos环境配置
     * @param dataId 配置ID
     * @param content 配置内容
     * @param type 配置类型（properties/json/xml/yaml/text）
     * @return 创建结果
     * @throws Exception API调用异常
     */
    Object updateNacosConfig(NacosEnvironment environment, String dataId, String content, String type) throws Exception;
    
    /**
     * 创建或更新Nacos配置（兼容旧版本接口）
     * 
     * @param instanceId 实例ID
     * @param regionId 区域ID
     * @param dataId 配置ID
     * @param group 分组
     * @param namespace 命名空间
     * @param content 配置内容
     * @param type 配置类型（properties/json/xml/yaml/text）
     * @return 创建结果
     * @throws Exception API调用异常
     */
    Object createNacosConfig(String instanceId, String regionId, String dataId,
                             String group, String namespace, String content, String type) throws Exception;
    
    /**
     * 删除Nacos配置（基于环境配置）
     * 
     * @param environment Nacos环境配置
     * @param dataId 配置ID
     * @return 删除结果
     * @throws Exception API调用异常
     */
    Object deleteNacosConfig(NacosEnvironment environment, String dataId) throws Exception;
    
    /**
     * 删除Nacos配置（兼容旧版本接口）
     * 
     * @param instanceId 实例ID
     * @param regionId 区域ID
     * @param dataId 配置ID
     * @param group 分组
     * @param namespace 命名空间
     * @return 删除结果
     * @throws Exception API调用异常
     */
    Object deleteNacosConfig(String instanceId, String regionId, String dataId, 
                            String group, String namespace) throws Exception;
    
    // ===== 增强功能 =====
    
    /**
     * 批量创建或更新配置
     * 
     * @param environment Nacos环境配置
     * @param configs 配置映射（dataId -> content）
     * @param type 配置类型
     * @return 成功创建/更新的数量
     * @throws Exception API调用异常
     */
    int batchCreateOrUpdateConfigs(NacosEnvironment environment, Map<String, String> configs, String type) throws Exception;
    
    /**
     * 批量删除配置
     * 
     * @param environment Nacos环境配置
     * @param dataIds 配置ID列表
     * @return 成功删除的数量
     * @throws Exception API调用异常
     */
    int batchDeleteConfigs(NacosEnvironment environment, List<String> dataIds) throws Exception;
    
    /**
     * 复制配置到另一个环境
     * 
     * @param sourceEnvironment 源环境
     * @param targetEnvironment 目标环境
     * @param dataIds 要复制的配置ID列表（如果为空则复制所有配置）
     * @param overwrite 是否覆盖已存在的配置
     * @return 成功复制的数量
     * @throws Exception API调用异常
     */
    int copyConfigsToEnvironment(NacosEnvironment sourceEnvironment, NacosEnvironment targetEnvironment, 
                               List<String> dataIds, boolean overwrite) throws Exception;
    
    /**
     * 同步配置到多个环境
     * 
     * @param sourceEnvironment 源环境
     * @param targetEnvironments 目标环境列表
     * @param dataIds 要同步的配置ID列表（如果为空则同步所有配置）
     * @param overwrite 是否覆盖已存在的配置
     * @return 同步结果摘要信息
     * @throws Exception API调用异常
     */
    String syncConfigsToEnvironments(NacosEnvironment sourceEnvironment, List<NacosEnvironment> targetEnvironments,
                                   List<String> dataIds, boolean overwrite) throws Exception;
    
    /**
     * 备份环境所有配置
     * 
     * @param environment Nacos环境配置
     * @param backupPath 备份路径
     * @return 备份文件路径
     * @throws Exception API调用异常
     */
    String backupEnvironmentConfigs(NacosEnvironment environment, String backupPath) throws Exception;
    
    /**
     * 从备份恢复配置
     * 
     * @param environment Nacos环境配置
     * @param backupFilePath 备份文件路径
     * @param overwrite 是否覆盖已存在的配置
     * @return 恢复成功的配置数量
     * @throws Exception API调用异常
     */
    int restoreConfigsFromBackup(NacosEnvironment environment, String backupFilePath, boolean overwrite) throws Exception;

    
    /**
     * 获取环境配置统计信息
     * 
     * @param environment Nacos环境配置
     * @return 统计信息
     * @throws Exception API调用异常
     */
    Map<String, Object> getEnvironmentConfigStatistics(NacosEnvironment environment) throws Exception;
    
    /**
     * 搜索配置
     * 
     * @param environment Nacos环境配置
     * @param keyword 搜索关键词
     * @param searchInContent 是否在配置内容中搜索
     * @return 匹配的配置列表
     * @throws Exception API调用异常
     */
    List<Map<String, Object>> searchConfigs(NacosEnvironment environment, String keyword, boolean searchInContent) throws Exception;
} 