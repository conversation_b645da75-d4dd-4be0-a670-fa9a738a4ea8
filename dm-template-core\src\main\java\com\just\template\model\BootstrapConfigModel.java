package com.just.template.model;

import cn.hutool.core.collection.CollectionUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Bootstrap配置模板参数模型
 * 对应 bootstrap-simple.ftl 模板
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BootstrapConfigModel {

    /**
     * 服务名称
     * 对应模板中的 ${serviceName}
     */
    private String serviceName;

    /**
     * Nacos服务器地址
     * 对应模板中的 ${nacosServerAddr}
     * 默认值: ${NACOS_ADDR}
     */
    private String nacosServerAddr;

    /**
     * Nacos命名空间
     * 对应模板中的 ${nacosNamespace}
     * 默认值: ${NACOS_NAMESPACE}
     */
    private String nacosNamespace;

    /**
     * Nacos访问密钥
     * 对应模板中的 ${nacosAccessKey}
     * 默认值: ${NACOS_ACCESS_KEY}
     */
    private String nacosAccessKey;

    /**
     * Nacos访问密钥Secret
     * 对应模板中的 ${nacosSecretKey}
     * 默认值: ${NACOS_SECRET_KEY}
     */
    private String nacosSecretKey;

    /**
     * 是否启用服务发现
     * 对应模板中的 <#if enableDiscovery>
     */
    private Boolean enableDiscovery = false;

    /**
     * 扩展配置列表
     * 对应模板中的 extensionConfigs
     */
    private List<ExtensionConfig> extensionConfigs = new ArrayList<>();

    /**
     * 默认的配置刷新设置
     * 对应模板中的 ${config.refresh}
     */
    private boolean refresh = true;

    /**
     * 验证必需的参数
     *
     * @throws IllegalArgumentException 如果缺少必需参数
     */
    public void validate() {
        if (serviceName == null || serviceName.trim().isEmpty()) {
            throw new IllegalArgumentException("serviceName is required for Bootstrap configuration");
        }
        if (nacosServerAddr == null || nacosServerAddr.trim().isEmpty()) {
            throw new IllegalArgumentException("nacosServerAddr is required for Bootstrap configuration");
        }
        if (nacosNamespace == null || nacosNamespace.trim().isEmpty()) {
            throw new IllegalArgumentException("nacosNamespace is required for Bootstrap configuration");
        }
    }

    /**
     * 扩展配置内部类
     */
    public static class ExtensionConfig {
        private String dataId;
        private String group = "DEFAULT_GROUP";
        private boolean refresh = true;

        public ExtensionConfig() {
        }

        public ExtensionConfig(String dataId, String group, boolean refresh) {
            this.dataId = dataId;
            this.group = group;
            this.refresh = refresh;
        }

        // Getters and Setters
        public String getDataId() {
            return dataId;
        }

        public void setDataId(String dataId) {
            this.dataId = dataId;
        }

        public String getGroup() {
            return group;
        }

        public void setGroup(String group) {
            this.group = group;
        }

        public boolean isRefresh() {
            return refresh;
        }

        public void setRefresh(boolean refresh) {
            this.refresh = refresh;
        }
    }

    public void addExtensionConfig(String dataId, String group, boolean refresh) {
        if (CollectionUtil.isEmpty(this.extensionConfigs)) {
            this.extensionConfigs = new ArrayList<>();
        }
        this.extensionConfigs.add(new ExtensionConfig(dataId, group, refresh));
    }


} 