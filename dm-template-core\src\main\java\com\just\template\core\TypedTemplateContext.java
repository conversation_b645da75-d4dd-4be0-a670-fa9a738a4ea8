package com.just.template.core;

import com.just.template.model.ApplicationNacosConfigModel;
import com.just.template.model.BootstrapConfigModel;
import com.just.template.model.PomConfigModel;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * 类型安全的模板上下文
 * 支持强类型的配置模型，提供更好的类型安全性和IDE支持
 *
 * <AUTHOR>
 * @since 2.0.0
 */
public class TypedTemplateContext {

    /**
     * 内部数据存储
     */
    private final Map<String, Object> data = new HashMap<>();

    /**
     * 原始配置模型引用
     */
    private Object sourceModel;

    public TypedTemplateContext() {
    }

    /**
     * 从Bootstrap配置模型创建上下文
     *
     * @param model Bootstrap配置模型
     * @return 模板上下文
     */
    public static TypedTemplateContext from(BootstrapConfigModel model) {
        if (model == null) {
            throw new IllegalArgumentException("BootstrapConfigModel cannot be null");
        }

        // 验证配置
        model.validate();

        TypedTemplateContext context = new TypedTemplateContext();
        context.sourceModel = model;

        // 映射所有字段到模板变量
        context.data.put("serviceName", model.getServiceName());
        context.data.put("nacosServerAddr", model.getNacosServerAddr());
        context.data.put("nacosNamespace", model.getNacosNamespace());
        context.data.put("nacosAccessKey", model.getNacosAccessKey());
        context.data.put("nacosSecretKey", model.getNacosSecretKey());
        context.data.put("enableDiscovery", model.getEnableDiscovery());
        context.data.put("refresh", model.isRefresh());
        context.data.put("extensionConfigs", model.getExtensionConfigs());

        // 添加兼容性别名
        context.data.put("applicationName", model.getServiceName()); // 兼容旧模板

        // 将整个配置对象也传递给模板（用于复杂访问）
        context.data.put("config", model);
        return context;
    }

    /**
     * 从Application Nacos配置模型创建上下文
     *
     * @param model Application Nacos配置模型
     * @return 模板上下文
     */
    public static TypedTemplateContext from(ApplicationNacosConfigModel model) {
        if (model == null) {
            throw new IllegalArgumentException("ApplicationNacosConfigModel cannot be null");
        }

        // 验证配置
        model.validate();

        TypedTemplateContext context = new TypedTemplateContext();
        context.sourceModel = model;
        // 映射所有字段到模板变量
        context.data.put("content", model.getContent());
        return context;
    }


    /**
     * 从Application Nacos配置模型创建上下文
     *
     * @param model Application Nacos配置模型
     * @return 模板上下文
     */
    public static TypedTemplateContext from(PomConfigModel model) {
        if (model == null) {
            throw new IllegalArgumentException("PomConfigModel cannot be null");
        }

        // 验证配置
        model.validate();

        TypedTemplateContext context = new TypedTemplateContext();
        context.sourceModel = model;
        // 映射所有字段到模板变量
        context.data.put("version", model.getVersion());
        context.data.put("register", model.getRegister());
        return context;
    }


    /**
     * 从任意配置模型创建上下文（使用反射）
     *
     * @param model 配置模型
     * @return 模板上下文
     */
    public static TypedTemplateContext fromModel(Object model) {
        if (model == null) {
            throw new IllegalArgumentException("Model cannot be null");
        }

        if (model instanceof BootstrapConfigModel) {
            return from((BootstrapConfigModel) model);
        }

        if (model instanceof ApplicationNacosConfigModel) {
            return from((ApplicationNacosConfigModel) model);
        }

        // 使用反射处理其他类型的模型
        return fromReflection(model);
    }

    /**
     * 使用反射从任意对象创建上下文
     */
    private static TypedTemplateContext fromReflection(Object model) {
        TypedTemplateContext context = new TypedTemplateContext();
        context.sourceModel = model;

        Class<?> clazz = model.getClass();
        try {
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(model);
                if (value != null) {
                    context.data.put(field.getName(), value);
                }
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException("Failed to extract model properties: " + e.getMessage(), e);
        }

        return context;
    }

    /**
     * 创建空的上下文（用于手动设置参数）
     */
    public static TypedTemplateContext empty() {
        return new TypedTemplateContext();
    }


    // 手动设置参数的方法
    public TypedTemplateContext put(String key, Object value) {
        data.put(key, value);
        return this;
    }

    public Object get(String key) {
        return data.get(key);
    }

    public String getString(String key) {
        Object value = data.get(key);
        return value != null ? value.toString() : null;
    }

    public String getString(String key, String defaultValue) {
        String value = getString(key);
        return value != null ? value : defaultValue;
    }

    public boolean getBoolean(String key, boolean defaultValue) {
        Object value = data.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof String) {
            return "true".equalsIgnoreCase((String) value);
        }
        return defaultValue;
    }

    public boolean containsKey(String key) {
        return data.containsKey(key);
    }

    /**
     * 获取所有数据（用于模板引擎）
     */
    public Map<String, Object> getData() {
        return new HashMap<>(data);
    }

    /**
     * 获取源配置模型
     */
    public Object getSourceModel() {
        return sourceModel;
    }

    /**
     * 获取源配置模型的特定类型
     */
    @SuppressWarnings("unchecked")
    public <T> T getSourceModel(Class<T> type) {
        if (sourceModel != null && type.isAssignableFrom(sourceModel.getClass())) {
            return (T) sourceModel;
        }
        return null;
    }

    /**
     * 验证必需的参数
     */
    public void validateRequired(String... requiredKeys) {
        if (requiredKeys == null) {
            return;
        }

        for (String key : requiredKeys) {
            if (!containsKey(key) || get(key) == null) {
                throw new IllegalArgumentException("Required parameter '" + key + "' is missing");
            }
        }
    }

    /**
     * 添加调试信息
     */
    public void addDebugInfo() {
        data.put("debugMode", true);
        data.put("contextSize", data.size());
        data.put("sourceModelType", sourceModel != null ? sourceModel.getClass().getSimpleName() : "none");
    }


    @Override
    public String toString() {
        return "TypedTemplateContext{" +
                "dataSize=" + data.size() +
                ", sourceModelType=" + (sourceModel != null ? sourceModel.getClass().getSimpleName() : "none") +
                ", keys=" + data.keySet() +
                '}';
    }
} 