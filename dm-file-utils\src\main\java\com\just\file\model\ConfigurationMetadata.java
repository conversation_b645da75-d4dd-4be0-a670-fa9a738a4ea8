package com.just.file.model;

import lombok.Builder;
import lombok.Value;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 配置元数据
 * 包含配置读取和处理的相关信息
 * 
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
@Value
@Builder(toBuilder = true)
public class ConfigurationMetadata {
    
    /**
     * 总行数
     */
    int totalRows;
    
    /**
     * 有效行数
     */
    int validRows;
    
    /**
     * 读取耗时
     */
    Duration readDuration;
    
    /**
     * 处理耗时
     */
    Duration processingDuration;
    
    /**
     * 读取开始时间
     */
    LocalDateTime readStartTime;
    
    /**
     * 读取结束时间
     */
    LocalDateTime readEndTime;
    
    /**
     * 文件大小（字节）
     */
    long fileSize;
    
    /**
     * 工作表数量
     */
    int sheetCount;
    
    /**
     * 验证错误列表
     */
    List<ValidationError> validationErrors;
    
    /**
     * 处理器信息
     */
    String processorInfo;
    
    /**
     * 缓存命中标识
     */
    boolean cacheHit;
    
    /**
     * 数据来源
     */
    String dataSource;

    /**
     * 验证错误
     */
    @Value
    @Builder(toBuilder = true)
    public static class ValidationError {
        /**
         * 错误代码
         */
        String errorCode;
        
        /**
         * 错误消息
         */
        String message;
        
        /**
         * 行号
         */
        int rowIndex;
        
        /**
         * 列号
         */
        int columnIndex;
        
        /**
         * 错误值
         */
        String errorValue;
        
        /**
         * 严重级别
         */
        ErrorLevel level;
    }

    /**
     * 错误级别
     */
    public enum ErrorLevel {
        INFO, WARN, ERROR, FATAL
    }
} 