package com.just.file.config;

import com.just.file.service.*;
import com.just.file.utils.ExcelReaderHelper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ContextConfiguration;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FileUtilsAutoConfiguration 测试类
 * 验证自动配置是否正确工作
 * 
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
@SpringBootTest
@ContextConfiguration(classes = FileUtilsAutoConfiguration.class)
class FileUtilsAutoConfigurationTest {

    @Resource
    private ApplicationContext applicationContext;

    @Test
    void testExcelReaderHelperBean() {
        // 验证 ExcelReaderHelper Bean 是否正确配置
        ExcelReaderHelper excelReaderHelper = applicationContext.getBean(ExcelReaderHelper.class);
        assertNotNull(excelReaderHelper);
    }

    @Test
    void testConfigurationProcessorBeans() {
        // 验证配置处理器 Bean 是否正确配置
        List<ConfigurationProcessor> processors = 
            applicationContext.getBeansOfType(ConfigurationProcessor.class)
                .values().stream().collect(Collectors.toList());
        
        assertFalse(processors.isEmpty());
        
        // 验证是否包含预期的处理器
        boolean hasNacosProcessor = processors.stream()
            .anyMatch(p -> p instanceof NacosConfigurationProcessor);
        boolean hasUrlProcessor = processors.stream()
            .anyMatch(p -> p instanceof UrlMappingConfigurationProcessor);
        
        assertTrue(hasNacosProcessor, "应该包含 NacosConfigurationProcessor");
        assertTrue(hasUrlProcessor, "应该包含 UrlMappingConfigurationProcessor");
    }

    @Test
    void testConfigurationDataFactoryBean() {
        // 验证 ConfigurationDataFactory Bean 是否正确配置
        ConfigurationDataFactory factory = applicationContext.getBean(ConfigurationDataFactory.class);
        assertNotNull(factory);
    }

    @Test
    void testExcelConfigServiceBean() {
        // 验证 ExcelConfigService Bean 是否正确配置
        ExcelConfigService excelConfigService = applicationContext.getBean(ExcelConfigService.class);
        assertNotNull(excelConfigService);
    }

    @Test
    void testFileOperationServiceBean() {
        // 验证 FileOperationService Bean 是否正确配置
        FileOperationService fileOperationService = applicationContext.getBean(FileOperationService.class);
        assertNotNull(fileOperationService);
    }

    @Test
    void testConfigValidatorBean() {
        // 验证配置验证器是否正确配置
        FileUtilsAutoConfiguration.FileUtilsConfigValidator validator = 
            applicationContext.getBean(FileUtilsAutoConfiguration.FileUtilsConfigValidator.class);
        assertNotNull(validator);
        assertTrue(validator.isConfigurationValid());
    }

    @Test
    void testExcelConfigPropertiesBean() {
        // 验证配置属性 Bean 是否正确加载
        ExcelConfigProperties properties = applicationContext.getBean(ExcelConfigProperties.class);
        assertNotNull(properties);
    }
} 