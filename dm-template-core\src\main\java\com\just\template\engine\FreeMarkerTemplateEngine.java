package com.just.template.engine;

import com.just.template.core.TemplateEngine;
import com.just.template.core.TypedTemplateContext;
import com.just.template.exception.TemplateException;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateExceptionHandler;
import freemarker.template.Version;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.Locale;

/**
 * FreeMarker模板引擎实现
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
public class FreeMarkerTemplateEngine implements TemplateEngine {
    
    /**
     * FreeMarker配置
     */
    private final Configuration configuration;
    
    /**
     * 模板根路径
     */
    private final String templateRoot;
    
    public FreeMarkerTemplateEngine() {
        this("templates");
    }
    
    public FreeMarkerTemplateEngine(String templateRoot) {
        this.templateRoot = templateRoot;
        this.configuration = createConfiguration();
    }
    
    /**
     * 创建FreeMarker配置
     */
    private Configuration createConfiguration() {
        Configuration config = new Configuration(new Version(2, 3, 31));
        // 设置模板加载路径
        config.setClassForTemplateLoading(this.getClass(), "/" + templateRoot);
        // 设置字符编码
        config.setDefaultEncoding("UTF-8");
        
        // 设置区域
        config.setLocale(Locale.SIMPLIFIED_CHINESE);
        
        // 设置异常处理器
        config.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
        
        // 设置兼容性选项
        config.setLogTemplateExceptions(false);
        config.setWrapUncheckedExceptions(true);
        config.setFallbackOnNullLoopVariable(false);
        
        return config;
    }
    
    @Override
    public String process(String templateName, TypedTemplateContext context) throws TemplateException {
        if (!StringUtils.hasText(templateName)) {
            throw new TemplateException("Template name cannot be empty");
        }
        
        if (context == null) {
            throw new TemplateException(templateName, "TypedTemplateContext cannot be null");
        }
        
        try {
            Template template = configuration.getTemplate(templateName);
            StringWriter writer = new StringWriter();
            template.process(context.getData(), writer);
            return writer.toString();
            
        } catch (IOException e) {
            throw new TemplateException(templateName, "Failed to load template", e);
        } catch (freemarker.template.TemplateException e) {
            throw new TemplateException(templateName, "Failed to process template: " + e.getMessage(), e);
        }
    }
    
    @Override
    public String processString(String templateContent, TypedTemplateContext context) throws TemplateException {
        if (!StringUtils.hasText(templateContent)) {
            throw new TemplateException("Template content cannot be empty");
        }
        
        if (context == null) {
            throw new TemplateException("TypedTemplateContext cannot be null");
        }
        
        try {
            Template template = new Template("stringTemplate", new StringReader(templateContent), configuration);
            StringWriter writer = new StringWriter();
            template.process(context.getData(), writer);
            return writer.toString();
            
        } catch (IOException e) {
            throw new TemplateException("Failed to create template from string", e);
        } catch (freemarker.template.TemplateException e) {
            throw new TemplateException("Failed to process template string: " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean templateExists(String templateName) {
        if (!StringUtils.hasText(templateName)) {
            return false;
        }
        
        try {
            configuration.getTemplate(templateName);
            return true;
        } catch (IOException e) {
            return false;
        }
    }
    
    /**
     * 获取模板根路径
     */
    public String getTemplateRoot() {
        return templateRoot;
    }
    
    /**
     * 获取FreeMarker配置
     */
    public Configuration getConfiguration() {
        return configuration;
    }
} 