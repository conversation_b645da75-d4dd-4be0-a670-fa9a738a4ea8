package com.just.common.constants;

/**
 * 错误码定义
 * 统一管理系统中的所有错误码
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
public final class ErrorCodes {
    
    private ErrorCodes() {
        // 私有构造函数，防止实例化
    }
    
    // ======================== 通用错误码 (1000-1999) ========================
    
    /**
     * 操作成功
     */
    public static final String SUCCESS = "0000";
    
    /**
     * 系统内部错误
     */
    public static final String SYSTEM_ERROR = "1000";
    
    /**
     * 参数错误
     */
    public static final String PARAM_ERROR = "1001";
    
    /**
     * 参数为空
     */
    public static final String PARAM_NULL = "1002";
    
    /**
     * 参数格式错误
     */
    public static final String PARAM_FORMAT_ERROR = "1003";
    
    /**
     * 操作超时
     */
    public static final String TIMEOUT_ERROR = "1004";
    
    /**
     * 权限不足
     */
    public static final String PERMISSION_DENIED = "1005";
    
    /**
     * 资源不存在
     */
    public static final String RESOURCE_NOT_FOUND = "1006";
    
    /**
     * 资源已存在
     */
    public static final String RESOURCE_ALREADY_EXISTS = "1007";
    
    /**
     * 操作被取消
     */
    public static final String OPERATION_CANCELLED = "1008";
    
    /**
     * 配置错误
     */
    public static final String CONFIG_ERROR = "1009";
    
    // ======================== 文件操作错误码 (2000-2999) ========================
    
    /**
     * 文件不存在
     */
    public static final String FILE_NOT_FOUND = "2000";
    
    /**
     * 文件读取失败
     */
    public static final String FILE_READ_ERROR = "2001";
    
    /**
     * 文件写入失败
     */
    public static final String FILE_WRITE_ERROR = "2002";
    
    /**
     * 文件删除失败
     */
    public static final String FILE_DELETE_ERROR = "2003";
    
    /**
     * 文件创建失败
     */
    public static final String FILE_CREATE_ERROR = "2004";
    
    /**
     * 文件权限不足
     */
    public static final String FILE_PERMISSION_DENIED = "2005";
    
    /**
     * 目录不存在
     */
    public static final String DIRECTORY_NOT_FOUND = "2006";
    
    /**
     * 目录创建失败
     */
    public static final String DIRECTORY_CREATE_ERROR = "2007";
    
    /**
     * 文件格式不支持
     */
    public static final String FILE_FORMAT_NOT_SUPPORTED = "2008";
    
    /**
     * 文件大小超限
     */
    public static final String FILE_SIZE_EXCEEDED = "2009";
    
    // ======================== Nacos相关错误码 (3000-3999) ========================
    
    /**
     * Nacos连接失败
     */
    public static final String NACOS_CONNECTION_ERROR = "3000";
    
    /**
     * Nacos配置获取失败
     */
    public static final String NACOS_CONFIG_GET_ERROR = "3001";
    
    /**
     * Nacos配置发布失败
     */
    public static final String NACOS_CONFIG_PUBLISH_ERROR = "3002";
    
    /**
     * Nacos配置不存在
     */
    public static final String NACOS_CONFIG_NOT_FOUND = "3003";
    
    /**
     * Nacos命名空间不存在
     */
    public static final String NACOS_NAMESPACE_NOT_FOUND = "3004";
    
    /**
     * Nacos服务注册失败
     */
    public static final String NACOS_SERVICE_REGISTER_ERROR = "3005";
    
    /**
     * Nacos访问密钥错误
     */
    public static final String NACOS_ACCESS_KEY_ERROR = "3006";
    
    /**
     * Nacos配置解析失败
     */
    public static final String NACOS_CONFIG_PARSE_ERROR = "3007";
    
    /**
     * Nacos配置验证失败
     */
    public static final String NACOS_CONFIG_VALIDATION_ERROR = "3008";
    
    /**
     * Nacos环境配置错误
     */
    public static final String NACOS_ENV_CONFIG_ERROR = "3009";
    
    // ======================== Git相关错误码 (4000-4999) ========================
    
    /**
     * Git仓库不存在
     */
    public static final String GIT_REPOSITORY_NOT_FOUND = "4000";
    
    /**
     * Git克隆失败
     */
    public static final String GIT_CLONE_ERROR = "4001";
    
    /**
     * Git提交失败
     */
    public static final String GIT_COMMIT_ERROR = "4002";
    
    /**
     * Git推送失败
     */
    public static final String GIT_PUSH_ERROR = "4003";
    
    /**
     * Git分支切换失败
     */
    public static final String GIT_BRANCH_SWITCH_ERROR = "4004";
    
    /**
     * Git分支创建失败
     */
    public static final String GIT_BRANCH_CREATE_ERROR = "4005";
    
    /**
     * Git合并失败
     */
    public static final String GIT_MERGE_ERROR = "4006";
    
    /**
     * Git权限不足
     */
    public static final String GIT_PERMISSION_DENIED = "4007";
    
    /**
     * Git配置错误
     */
    public static final String GIT_CONFIG_ERROR = "4008";
    
    /**
     * Git远程仓库错误
     */
    public static final String GIT_REMOTE_ERROR = "4009";
    
    // ======================== GitLab相关错误码 (5000-5999) ========================
    
    /**
     * GitLab API调用失败
     */
    public static final String GITLAB_API_ERROR = "5000";
    
    /**
     * GitLab项目不存在
     */
    public static final String GITLAB_PROJECT_NOT_FOUND = "5001";
    
    /**
     * GitLab访问令牌无效
     */
    public static final String GITLAB_TOKEN_INVALID = "5002";
    
    /**
     * GitLab合并请求创建失败
     */
    public static final String GITLAB_MR_CREATE_ERROR = "5003";
    
    /**
     * GitLab分支操作失败
     */
    public static final String GITLAB_BRANCH_ERROR = "5004";
    
    /**
     * GitLab用户权限不足
     */
    public static final String GITLAB_USER_PERMISSION_DENIED = "5005";
    
    /**
     * GitLab网络连接错误
     */
    public static final String GITLAB_NETWORK_ERROR = "5006";
    
    /**
     * GitLab服务不可用
     */
    public static final String GITLAB_SERVICE_UNAVAILABLE = "5007";
    
    /**
     * GitLab配置错误
     */
    public static final String GITLAB_CONFIG_ERROR = "5008";
    
    /**
     * GitLab操作超时
     */
    public static final String GITLAB_TIMEOUT_ERROR = "5009";
    
    // ======================== 模板相关错误码 (6000-6999) ========================
    
    /**
     * 模板不存在
     */
    public static final String TEMPLATE_NOT_FOUND = "6000";
    
    /**
     * 模板解析失败
     */
    public static final String TEMPLATE_PARSE_ERROR = "6001";
    
    /**
     * 模板渲染失败
     */
    public static final String TEMPLATE_RENDER_ERROR = "6002";
    
    /**
     * 模板变量未定义
     */
    public static final String TEMPLATE_VARIABLE_UNDEFINED = "6003";
    
    /**
     * 模板格式错误
     */
    public static final String TEMPLATE_FORMAT_ERROR = "6004";
    
    /**
     * 模板加载失败
     */
    public static final String TEMPLATE_LOAD_ERROR = "6005";
    
    /**
     * 模板配置错误
     */
    public static final String TEMPLATE_CONFIG_ERROR = "6006";
    
    // ======================== 配置升级相关错误码 (7000-7999) ========================
    
    /**
     * 配置升级失败
     */
    public static final String CONFIG_UPGRADE_ERROR = "7000";
    
    /**
     * 配置生成失败
     */
    public static final String CONFIG_GENERATE_ERROR = "7001";
    
    /**
     * 配置验证失败
     */
    public static final String CONFIG_VALIDATE_ERROR = "7002";
    
    /**
     * 环境变量未解析
     */
    public static final String ENV_VARIABLE_UNRESOLVED = "7003";
    
    /**
     * FeignClient升级失败
     */
    public static final String FEIGN_CLIENT_UPGRADE_ERROR = "7004";
    
    /**
     * Bootstrap配置生成失败
     */
    public static final String BOOTSTRAP_CONFIG_ERROR = "7005";
    
    /**
     * POM文件更新失败
     */
    public static final String POM_UPDATE_ERROR = "7006";
    
    /**
     * Logback配置更新失败
     */
    public static final String LOGBACK_UPDATE_ERROR = "7007";
    
    /**
     * 服务名称解析失败
     */
    public static final String SERVICE_NAME_PARSE_ERROR = "7008";
    
    /**
     * 配置发布失败
     */
    public static final String CONFIG_PUBLISH_ERROR = "7009";
    
    // ======================== Excel相关错误码 (8000-8999) ========================
    
    /**
     * Excel文件读取失败
     */
    public static final String EXCEL_READ_ERROR = "8000";
    
    /**
     * Excel文件格式错误
     */
    public static final String EXCEL_FORMAT_ERROR = "8001";
    
    /**
     * Excel工作表不存在
     */
    public static final String EXCEL_SHEET_NOT_FOUND = "8002";
    
    /**
     * Excel数据解析失败
     */
    public static final String EXCEL_DATA_PARSE_ERROR = "8003";
    
    /**
     * Excel文件写入失败
     */
    public static final String EXCEL_WRITE_ERROR = "8004";
    
    /**
     * Excel文件损坏
     */
    public static final String EXCEL_FILE_CORRUPTED = "8005";
    
    // ======================== Web API相关错误码 (9000-9999) ========================
    
    /**
     * API请求失败
     */
    public static final String API_REQUEST_ERROR = "9000";
    
    /**
     * API响应解析失败
     */
    public static final String API_RESPONSE_PARSE_ERROR = "9001";
    
    /**
     * API认证失败
     */
    public static final String API_AUTH_ERROR = "9002";
    
    /**
     * API限流错误
     */
    public static final String API_RATE_LIMIT_ERROR = "9003";
    
    /**
     * API版本不兼容
     */
    public static final String API_VERSION_INCOMPATIBLE = "9004";
    
    /**
     * API服务不可用
     */
    public static final String API_SERVICE_UNAVAILABLE = "9005";
}