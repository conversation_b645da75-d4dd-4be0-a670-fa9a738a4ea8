package com.just.nacos.service.impl;

import com.aliyun.mse20190531.models.GetNacosConfigResponse;
import com.aliyun.mse20190531.models.GetNacosConfigResponseBody;
import com.aliyun.mse20190531.models.ListNacosConfigsResponseBody;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.just.nacos.config.NacosCoreProperties;
import com.just.nacos.exception.NacosConfigException;
import com.just.nacos.model.NacosConfig;
import com.just.nacos.model.NacosEnvironment;
import com.just.nacos.service.AliyunMseActionService;
import com.just.nacos.service.NacosConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Nacos配置管理服务实现
 * 基于 AliyunMseActionService 实现
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@Service
public class NacosConfigServiceImpl implements NacosConfigService {

    private final AliyunMseActionService mseActionService;
    private final NacosCoreProperties properties;
    private final ObjectMapper objectMapper;
    private final ObjectMapper yamlMapper;

    public NacosConfigServiceImpl(AliyunMseActionService mseActionService, NacosCoreProperties properties) {
        this.mseActionService = mseActionService;
        this.properties = properties;
        this.objectMapper = new ObjectMapper();
        this.yamlMapper = new ObjectMapper(new YAMLFactory());
    }

    @PostConstruct
    public void init() {
        log.info("Nacos配置服务初始化完成，基于AliyunMseActionService实现");
    }

    @Override
    public boolean publishConfig(NacosConfig config) {
        try {
            validateConfig(config);

            // 获取配置对应的环境
            NacosEnvironment environment = getEnvironmentForConfig(config);
            if (environment == null) {
                throw NacosConfigException.validationFailed("environment", "无法找到匹配的环境配置");
            }

            String dataId = config.getDataId();
            String content = config.getContent();
            String type = config.getType() != null ? config.getType() : "properties";
            String group = config.getGroup();

            // 格式化配置内容
            String formattedContent = formatConfigContent(content, type);
            environment.setDefaultGroup(group);

            boolean b = this.configExists(config.getDataId(), group, environment.getNamespace());
            Object result = null;
            if (b) {
                log.info("配置已存在: {}/{}", group, config.getDataId());
                result = mseActionService.updateNacosConfig(environment, config.getDataId(), formattedContent, type);
            } else {
                result = mseActionService.createNacosConfig(environment, dataId, formattedContent, type);
                log.info("配置不存在: {}/{}", group, config.getDataId());
            }
            boolean success = result != null;
            if (success) {
                log.info("配置发布成功: {}/{} [环境: {}]", config.getGroup(), dataId, environment.getName());
            } else {
                log.warn("配置发布失败: {}/{} [环境: {}]", config.getGroup(), dataId, environment.getName());
            }

            return success;

        } catch (Exception e) {
            log.error("发布配置失败: {}/{}", config.getGroup(), config.getDataId(), e);
            throw NacosConfigException.publishFailed(config.getDataId(), config.getGroup(), e);
        }
    }

    @Override
    public Optional<NacosConfig> getConfig(String dataId, String group) {
        return getConfig(dataId, group, null);
    }

    @Override
    public Optional<NacosConfig> getConfig(String dataId, String group, String namespace) {
        try {
            // 根据namespace获取对应的环境
            NacosEnvironment environment = getEnvironmentByNamespace(namespace);
            if (environment == null) {
                log.warn("找不到命名空间为 {} 的环境配置", namespace);
                return Optional.empty();
            }
            String effectiveGroup = group != null ? group : environment.getDefaultGroup();
            // 获取配置列表来查找指定的配置
            GetNacosConfigResponse nacosConfigResponse = mseActionService.getNacosConfig(environment, dataId, group, environment.getNamespace());
            GetNacosConfigResponseBody.GetNacosConfigResponseBodyConfiguration config = nacosConfigResponse.getBody().getConfiguration();
            if (config == null) {
                return Optional.empty();
            }
            // 查找匹配的配置
            if (dataId.equals(config.getDataId()) && effectiveGroup.equals(config.getGroup())) {
                // 检测配置类型基于dataId
                String detectedType = detectConfigType(config.getDataId());
                NacosConfig nacosConfig = NacosConfig.builder()
                        .dataId(config.getDataId())
                        .group(config.getGroup())
                        .namespace(namespace)
                        .content(config.getContent())
                        .type(detectedType)
                        .updateTime(LocalDateTime.now())
                        .status(NacosConfig.ConfigStatus.ACTIVE)
                        .build();
                return Optional.of(nacosConfig);
            }
            return Optional.empty();
        } catch (Exception e) {
            log.error("获取配置失败: {}/{} [namespace: {}]", group, dataId, namespace, e);
            return Optional.empty();
        }
    }

    @Override
    public boolean deleteConfig(String dataId, String group) {
        return deleteConfig(dataId, group, null);
    }

    @Override
    public boolean deleteConfig(String dataId, String group, String namespace) {
        try {
            // 根据namespace获取对应的环境
            NacosEnvironment environment = getEnvironmentByNamespace(namespace);
            if (environment == null) {
                throw NacosConfigException.validationFailed("environment", "找不到命名空间为 " + namespace + " 的环境配置");
            }

            // 使用 AliyunMseActionService 删除配置
            Object result = mseActionService.deleteNacosConfig(environment, dataId);

            boolean success = result != null;
            if (success) {
                log.info("配置删除成功: {}/{} [环境: {}]", group, dataId, environment.getName());
            } else {
                log.warn("配置删除失败: {}/{} [环境: {}]", group, dataId, environment.getName());
            }
            return success;
        } catch (Exception e) {
            log.error("删除配置失败: {}/{} [namespace: {}]", group, dataId, namespace, e);
            throw NacosConfigException.deleteFailed(dataId, group, e);
        }
    }

    @Override
    public List<NacosConfig> listConfigs(int pageNo, int pageSize) {
        return listConfigs(pageNo, pageSize, null, null);
    }

    @Override
    public List<NacosConfig> listConfigs(int pageNo, int pageSize, String group, String namespace) {
        try {
            // 根据namespace获取对应的环境
            NacosEnvironment environment = getEnvironmentByNamespace(namespace);
            if (environment == null) {
                // 如果没有指定namespace，使用默认环境
                environment = getDefaultEnvironment();
                if (environment == null) {
                    log.warn("找不到可用的环境配置");
                    return new ArrayList<>();
                }
            }

            // 使用 AliyunMseActionService 获取配置列表
            ListNacosConfigsResponseBody response = mseActionService.listNacosConfigs(environment, group, pageNo, pageSize);
            if (response == null || response.getConfigurations() == null) {
                return new ArrayList<>();
            }

            // 转换为 NacosConfig 对象列表
            List<NacosConfig> configs = new ArrayList<>();
            for (ListNacosConfigsResponseBody.ListNacosConfigsResponseBodyConfigurations config : response.getConfigurations()) {
                    String detectedType = detectConfigType(config.getDataId());
                    NacosConfig nacosConfig = NacosConfig.builder()
                            .dataId(config.getDataId())
                            .group(config.getGroup())
                            .namespace(environment.getNamespace())
                            .type(detectedType)
                            .updateTime(LocalDateTime.now())
                            .status(NacosConfig.ConfigStatus.ACTIVE)
                            .build();
                    configs.add(nacosConfig);
            }

            log.info("获取配置列表成功，环境: {}, 总数: {}", environment.getName(), configs.size());
            return configs;

        } catch (Exception e) {
            log.error("列出配置失败 [namespace: {}, group: {}]", namespace, group, e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean configExists(String dataId, String group) {
        return configExists(dataId, group, null);
    }

    @Override
    public boolean configExists(String dataId, String group, String namespace) {
        return getConfig(dataId, group, namespace).isPresent();
    }

    @Override
    public int batchPublishConfigs(List<NacosConfig> configs) {
        if (configs == null || configs.isEmpty()) {
            return 0;
        }

        // 按环境分组处理
        Map<String, List<NacosConfig>> configsByNamespace = configs.stream()
                .collect(Collectors.groupingBy(config -> config.getNamespace() != null ? config.getNamespace() : "default"));

        int totalSuccessCount = 0;

        for (Map.Entry<String, List<NacosConfig>> entry : configsByNamespace.entrySet()) {
            String namespace = entry.getKey();
            List<NacosConfig> namespaceConfigs = entry.getValue();

            try {
                NacosEnvironment environment = getEnvironmentByNamespace(namespace);
                if (environment == null) {
                    log.error("找不到命名空间为 {} 的环境配置，跳过 {} 个配置", namespace, namespaceConfigs.size());
                    continue;
                }

                // 转换为批量操作所需的Map格式
                Map<String, String> configMap = namespaceConfigs.stream()
                        .collect(Collectors.toMap(
                                NacosConfig::getDataId,
                                config -> formatConfigContent(config.getContent(), config.getType()),
                                (existing, replacement) -> replacement // 处理重复键
                        ));

                // 使用增强的批量操作功能
                String type = namespaceConfigs.get(0).getType() != null ? namespaceConfigs.get(0).getType() : "properties";
                int successCount = mseActionService.batchCreateOrUpdateConfigs(environment, configMap, type);
                totalSuccessCount += successCount;

                log.info("批量发布配置完成，环境: {}, 总数: {}, 成功: {}", environment.getName(), namespaceConfigs.size(), successCount);

            } catch (Exception e) {
                log.error("批量发布配置失败，命名空间: {}", namespace, e);
            }
        }

        log.info("批量发布配置全部完成，总成功数: {}", totalSuccessCount);
        return totalSuccessCount;
    }

    @Override
    public int batchDeleteConfigs(List<NacosConfig> configs) {
        if (configs == null || configs.isEmpty()) {
            return 0;
        }

        // 按环境分组处理
        Map<String, List<NacosConfig>> configsByNamespace = configs.stream()
                .collect(Collectors.groupingBy(config -> config.getNamespace() != null ? config.getNamespace() : "default"));

        int totalSuccessCount = 0;

        for (Map.Entry<String, List<NacosConfig>> entry : configsByNamespace.entrySet()) {
            String namespace = entry.getKey();
            List<NacosConfig> namespaceConfigs = entry.getValue();

            try {
                NacosEnvironment environment = getEnvironmentByNamespace(namespace);
                if (environment == null) {
                    log.error("找不到命名空间为 {} 的环境配置，跳过 {} 个配置", namespace, namespaceConfigs.size());
                    continue;
                }

                // 提取dataId列表
                List<String> dataIds = namespaceConfigs.stream()
                        .map(NacosConfig::getDataId)
                        .collect(Collectors.toList());

                // 使用增强的批量删除功能
                int successCount = mseActionService.batchDeleteConfigs(environment, dataIds);
                totalSuccessCount += successCount;

                log.info("批量删除配置完成，环境: {}, 总数: {}, 成功: {}", environment.getName(), namespaceConfigs.size(), successCount);

            } catch (Exception e) {
                log.error("批量删除配置失败，命名空间: {}", namespace, e);
            }
        }

        log.info("批量删除配置全部完成，总成功数: {}", totalSuccessCount);
        return totalSuccessCount;
    }

    @Override
    public String validateConfigFormat(String content, String type) {
        try {
            if (!StringUtils.hasText(content)) {
                return "配置内容不能为空";
            }

            switch (type.toLowerCase()) {
                case "json":
                    objectMapper.readTree(content);
                    break;
                case "yaml":
                case "yml":
                    yamlMapper.readTree(content);
                    break;
                case "properties":
                    Properties props = new Properties();
                    props.load(new java.io.StringReader(content));
                    break;
                case "xml":
                    // 简单的XML格式检查
                    if (!content.trim().startsWith("<") || !content.trim().endsWith(">")) {
                        return "XML格式无效";
                    }
                    break;
                default:
                    // 对于其他格式，不做特殊验证
                    break;
            }

            return "格式验证通过";

        } catch (Exception e) {
            return "格式验证失败: " + e.getMessage();
        }
    }

    @Override
    public String formatConfigContent(String content, String type) {
        try {
            if (!StringUtils.hasText(content) || !StringUtils.hasText(type)) {
                return content;
            }

            switch (type.toLowerCase()) {
                case "json":
                    Object jsonObj = objectMapper.readValue(content, Object.class);
                    return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(jsonObj);
                case "yaml":
                case "yml":
                    Object yamlObj = yamlMapper.readValue(content, Object.class);
                    return yamlMapper.writerWithDefaultPrettyPrinter().writeValueAsString(yamlObj);
                default:
                    return content;
            }

        } catch (Exception e) {
            log.warn("格式化配置内容失败，返回原内容: {}", e.getMessage());
            return content;
        }
    }

    /**
     * 验证配置有效性
     */
    private void validateConfig(NacosConfig config) {
        if (config == null) {
            throw NacosConfigException.validationFailed("config", "配置对象不能为空");
        }

        if (!StringUtils.hasText(config.getDataId())) {
            throw NacosConfigException.validationFailed("dataId", "配置ID不能为空");
        }

        if (!StringUtils.hasText(config.getContent())) {
            throw NacosConfigException.validationFailed("content", "配置内容不能为空");
        }

        // 验证配置格式
        if (StringUtils.hasText(config.getType())) {
            String validationResult = validateConfigFormat(config.getContent(), config.getType());
            if (!validationResult.equals("格式验证通过")) {
                throw NacosConfigException.validationFailed("format", validationResult);
            }
        }
    }

    /**
     * 根据配置获取对应的环境
     */
    private NacosEnvironment getEnvironmentForConfig(NacosConfig config) {
        // 优先使用配置中的namespace
        if (StringUtils.hasText(config.getNamespace())) {
            return getEnvironmentByNamespace(config.getNamespace());
        }

        // 优先使用配置中的group来推断环境
        if (StringUtils.hasText(config.getGroup())) {
            for (NacosCoreProperties.EnvironmentConfig envConfig : properties.getEnvironments().values()) {
                if (config.getGroup().equals(envConfig.getDefaultGroup())) {
                    return convertToNacosEnvironment(envConfig);
                }
            }
        }

        // 返回默认环境
        return getDefaultEnvironment();
    }

    /**
     * 根据namespace获取环境配置
     */
    private NacosEnvironment getEnvironmentByNamespace(String namespace) {
        if (!StringUtils.hasText(namespace) || "default" .equals(namespace)) {
            return getDefaultEnvironment();
        }

        return properties.getEnvironments().values().stream()
                .filter(env -> namespace.equals(env.getNamespace()))
                .map(this::convertToNacosEnvironment)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取默认环境
     */
    private NacosEnvironment getDefaultEnvironment() {
        return properties.getEnvironments().values().stream()
                .filter(env -> env.isEnabled())
                .map(this::convertToNacosEnvironment)
                .findFirst()
                .orElse(null);
    }

    /**
     * 将EnvironmentConfig转换为NacosEnvironment
     */
    private NacosEnvironment convertToNacosEnvironment(NacosCoreProperties.EnvironmentConfig envConfig) {
        return NacosEnvironment.builder()
                .name(envConfig.getName())
                .description(envConfig.getDescription())
                .namespace(envConfig.getNamespace())
                .defaultGroup(envConfig.getDefaultGroup())
                .serverAddr(envConfig.getServerAddr())
                .accessKey(envConfig.getAccessKey())
                .secretKey(envConfig.getSecretKey())
                .enabled(envConfig.isEnabled())
                .endPoint(envConfig.getEndPoint())
                .regionId(envConfig.getRegionId())
                .instanceId(envConfig.getInstanceId())
                .build();
    }

    /**
     * 根据dataId检测配置类型
     */
    private String detectConfigType(String dataId) {
        if (dataId == null) {
            return "properties";
        }

        if (dataId.endsWith(".yaml") || dataId.endsWith(".yml")) {
            return "yaml";
        } else if (dataId.endsWith(".json")) {
            return "json";
        } else if (dataId.endsWith(".xml")) {
            return "xml";
        } else if (dataId.endsWith(".properties")) {
            return "properties";
        } else {
            return "properties"; // 默认类型
        }
    }
}