package com.just.nacos.model;

import com.just.nacos.enums.EnvironmentStatus;
import com.just.nacos.enums.NacosEnvironmentEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Nacos环境配置模型
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NacosEnvironment {

    /**
     * 环境名称
     */
    private String name;

    /**
     * 环境显示名称
     */
    private String displayName;

    /**
     * 环境描述
     */
    private String description;

    /**
     * 命名空间ID
     */
    private String namespace;

    /**
     * 默认配置组
     */
    private String defaultGroup;

    /**
     * 环境类型
     */
    private NacosEnvironmentEnum type;

    /**
     * 环境状态
     */
    private EnvironmentStatus status;

    /**
     * Nacos服务器地址
     */
    private String serverAddr;
    /**
     * 访问密钥
     */
    private String accessKey;
    /**
     * 密钥
     */
    private String secretKey;

    /**
     * 配置前缀
     */
    private String configPrefix;

    /**
     * 环境变量映射
     */
    private Map<String, String> variables;

    /**
     * 关联的应用列表
     */
    private List<String> applications;

    /**
     * 环境标签
     */
    private List<String> tags;

    /**
     * 优先级（数字越小优先级越高）
     */
    private Integer priority;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 是否为默认环境
     */
    private Boolean isDefault;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改者
     */
    private String modifier;
    /**
     *
     */
    private String regionId = "cn.shanghai";
    /**
     * 扩展属性
     **/
    private String endPoint;
    /**
     * 实例ID
     */
    private String instanceId;


    /**
     * 扩展属性
     */
    private Map<String, Object> metadata;


    /**
     * 检查环境配置是否有效
     *
     * @return 是否有效
     */
    public boolean isValid() {
        return name != null && !name.trim().isEmpty() &&
               namespace != null && !namespace.trim().isEmpty() &&
               defaultGroup != null && !defaultGroup.trim().isEmpty();
    }

    /**
     * 检查环境是否可用
     *
     * @return 是否可用
     */
    public boolean isAvailable() {
        return Boolean.TRUE.equals(enabled) && 
               status == EnvironmentStatus.ACTIVE;
    }

    /**
     * 获取环境的完整标识
     *
     * @return 环境标识
     */
    public String getFullIdentifier() {
        return String.format("%s[%s]", name, namespace);
    }

    /**
     * 获取配置Data ID前缀
     *
     * @return Data ID前缀
     */
    public String getConfigDataIdPrefix() {
        if (configPrefix != null && !configPrefix.trim().isEmpty()) {
            return configPrefix;
        }
        return name + ".";
    }

    /**
     * 检查是否包含指定应用
     *
     * @param appName 应用名称
     * @return 是否包含
     */
    public boolean containsApplication(String appName) {
        return applications != null && applications.contains(appName);
    }

    /**
     * 检查是否包含指定标签
     *
     * @param tag 标签
     * @return 是否包含
     */
    public boolean containsTag(String tag) {
        return tags != null && tags.contains(tag);
    }

    /**
     * 获取环境变量值
     *
     * @param key 变量键
     * @return 变量值
     */
    public String getVariable(String key) {
        return variables != null ? variables.get(key) : null;
    }

    /**
     * 获取环境变量值（带默认值）
     *
     * @param key 变量键
     * @param defaultValue 默认值
     * @return 变量值
     */
    public String getVariable(String key, String defaultValue) {
        String value = getVariable(key);
        return value != null ? value : defaultValue;
    }

    /**
     * 替换字符串中的环境变量
     *
     * @param template 模板字符串
     * @return 替换后的字符串
     */
    public String replaceVariables(String template) {
        if (template == null || variables == null) {
            return template;
        }

        String result = template;
        for (Map.Entry<String, String> entry : variables.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            result = result.replace(placeholder, entry.getValue());
        }
        return result;
    }

    /**
     * 生成Nacos配置的Data ID
     *
     * @param configName 配置名称
     * @return Data ID
     */
    public String generateDataId(String configName) {
        return getConfigDataIdPrefix() + configName;
    }

    /**
     * 生成Nacos配置的Data ID（带应用名称）
     *
     * @param appName 应用名称
     * @param configName 配置名称
     * @return Data ID
     */
    public String generateDataId(String appName, String configName) {
        return getConfigDataIdPrefix() + appName + "." + configName;
    }


    /**
     * 生成环境摘要信息
     *
     * @return 环境摘要
     */
    public String getSummary() {
        return String.format("Environment[%s] - %s (%s, %s)", 
                name, 
                displayName != null ? displayName : name,
                type != null ? type.getDescription() : "未知类型",
                status != null ? status.getDescription() : "未知状态");
    }

    /**
     * 获取环境的显示信息
     *
     * @return 显示信息
     */
    public String getDisplayInfo() {
        StringBuilder info = new StringBuilder();
        info.append("环境: ").append(displayName != null ? displayName : name).append("\n");
        info.append("类型: ").append(type != null ? type.getDescription() : "未知").append("\n");
        info.append("状态: ").append(status != null ? status.getDescription() : "未知").append("\n");
        info.append("命名空间: ").append(namespace).append("\n");
        info.append("默认分组: ").append(defaultGroup).append("\n");
        info.append("是否启用: ").append(Boolean.TRUE.equals(enabled) ? "是" : "否").append("\n");
        
        if (applications != null && !applications.isEmpty()) {
            info.append("关联应用: ").append(String.join(", ", applications)).append("\n");
        }
        
        if (description != null) {
            info.append("描述: ").append(description);
        }
        
        return info.toString();
    }
}