spring:
  application:
    name: dm-upgrade-center-test
  cache:
    type: simple
    cache-names: excelConfigCache

# 测试环境配置
# DM模块配置
dm:
  # Git配置（从dm-git-core的测试配置读取）
  git:
    base-url: http://git.innodealing.cn/
    token: **************************

  # Nacos配置（从dm-nacos-core的测试配置读取）
  nacos:
    environments:
      dev:
        name: dev
        description: 开发环境
        server-addr: mse-4d0af020-nacos-ans.mse.aliyuncs.com:8848
        instance-id: mse_regserverless_cn-3mp3ys14x01
        access-key: LTAI5tJ72ozLyf6eQnsupgV5
        secret-key: ******************************
        namespace: dev
        default-group: dev
        enabled: true
      qa:
        name: qa
        description: qa环境
        end-point: mse.cn-shanghai.aliyuncs.com
        instance-id: mse_regserverless_cn-3mp3ys14x01
        server-addr: mse-4d0af020-nacos-ans.mse.aliyuncs.com:8848
        access-key: LTAI5tJ72ozLyf6eQnsupgV5
        secret-key: ******************************
        namespace: qa
        default-group: qa
        enabled: true
      uat:
        name: uat
        server-addr: mse-f3de5d70-nacos-ans.mse.aliyuncs.com:8848
        instance-id: mse_prepaid_public_cn-k963wd2yi02
        access-key: LTAI5tJ72ozLyf6eQnsupgV5
        secret-key: ******************************
        description: uat环境
        namespace: uat
        default-group: uat
        enabled: true

# 日志配置
logging:
  level:
    com.just.file: DEBUG
    com.just.upgrade: DEBUG
    org.springframework.cache: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Excel配置属性  
excel:
  config:
    cache:
      enabled: true
      time-to-live: PT5M
      max-size: 100
    files:
      nacos:
        path: nacos环境变量对应文件.xlsx
        sheets:
          - ROCKETMQ
          - RABBITMQ
          - es
          - RDS__MYSQL
          - REDIS
          - POLAR
          - pg
          - kafka
          - canal
          - kettlepack
          - oracle
          - sqlserver
          - dingtalk
      url:
        path: url.xlsx
        sheets:
          - dev
          - qa
          - uat
          - prd
    supported-environments:
      - dev
      - qa
      - uat
      - prod
      - test

