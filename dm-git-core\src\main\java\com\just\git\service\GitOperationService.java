package com.just.git.service;

import com.just.git.exception.GitOperationException;
import com.just.git.model.*;

import java.nio.file.Path;
import java.util.List;

/**
 * Git操作服务接口
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
public interface GitOperationService {
    
    /**
     * 克隆Git仓库
     * 
     * @param request 克隆请求
     * @return Git仓库实例
     * @throws GitOperationException 操作失败时抛出
     */
    GitRepository cloneRepository(GitCloneRequest request) throws GitOperationException;
    
    /**
     * 分支操作（切换、创建、删除等）
     * 
     * @param repository Git仓库
     * @param request 分支操作请求
     * @return 操作结果
     * @throws GitOperationException 操作失败时抛出
     */
    GitOperationResult branchOperation(GitRepository repository, GitBranchRequest request) throws GitOperationException;
    
    /**
     * 提交代码
     * 
     * @param repository Git仓库
     * @param request 提交请求
     * @return 操作结果
     * @throws GitOperationException 操作失败时抛出
     */
    GitOperationResult commit(GitRepository repository, GitCommitRequest request) throws GitOperationException;
    
    /**
     * 推送代码到远程仓库
     * 
     * @param repository Git仓库
     * @param branchName 分支名称
     * @param force 是否强制推送
     * @return 操作结果
     * @throws GitOperationException 操作失败时抛出
     */
    GitOperationResult push(GitRepository repository, String branchName, boolean force) throws GitOperationException;
    
    /**
     * 从远程仓库拉取代码
     * 
     * @param repository Git仓库
     * @return 操作结果
     * @throws GitOperationException 操作失败时抛出
     */
    GitOperationResult pull(GitRepository repository) throws GitOperationException;
    
    /**
     * 获取仓库状态信息
     * 
     * @param repository Git仓库
     * @return 仓库状态
     * @throws GitOperationException 操作失败时抛出
     */
    GitRepository.RepositoryStatus getRepositoryStatus(GitRepository repository) throws GitOperationException;
    
    /**
     * 获取当前分支名称
     * 
     * @param repository Git仓库
     * @return 当前分支名称
     * @throws GitOperationException 操作失败时抛出
     */
    String getCurrentBranch(GitRepository repository) throws GitOperationException;
    
    /**
     * 获取所有分支列表
     * 
     * @param repository Git仓库
     * @param includeRemote 是否包含远程分支
     * @return 分支名称列表
     * @throws GitOperationException 操作失败时抛出
     */
    List<String> listBranches(GitRepository repository, boolean includeRemote) throws GitOperationException;
    
    /**
     * 检查仓库是否有未提交的更改
     * 
     * @param repository Git仓库
     * @return 是否有未提交的更改
     * @throws GitOperationException 操作失败时抛出
     */
    boolean hasUncommittedChanges(GitRepository repository) throws GitOperationException;
    
    /**
     * 获取未提交的文件列表
     * 
     * @param repository Git仓库
     * @return 未提交的文件路径列表
     * @throws GitOperationException 操作失败时抛出
     */
    List<String> getUncommittedFiles(GitRepository repository) throws GitOperationException;
    
    /**
     * 丢弃所有未提交的更改
     * 
     * @param repository Git仓库
     * @return 操作结果
     * @throws GitOperationException 操作失败时抛出
     */
    GitOperationResult discardChanges(GitRepository repository) throws GitOperationException;
    
    /**
     * 关闭Git仓库资源
     * 
     * @param repository Git仓库
     */
    void closeRepository(GitRepository repository);
    
    /**
     * 验证Git仓库有效性
     * 
     * @param repository Git仓库
     * @return 验证结果
     */
    List<String> validateRepository(GitRepository repository);
} 