package com.just.common.enums;

import lombok.Getter;

/**
 * 操作状态枚举
 * 用于表示各种操作的执行状态
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
@Getter
public enum OperationStatus {
    
    /**
     * 初始化
     */
    INIT("INIT", "初始化", "操作已创建但尚未开始"),
    
    /**
     * 进行中
     */
    RUNNING("RUNNING", "进行中", "操作正在执行"),
    
    /**
     * 成功
     */
    SUCCESS("SUCCESS", "成功", "操作执行成功"),
    
    /**
     * 失败
     */
    FAILED("FAILED", "失败", "操作执行失败"),
    
    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消", "操作已被取消"),
    
    /**
     * 超时
     */
    TIMEOUT("TIMEOUT", "超时", "操作执行超时"),
    
    /**
     * 警告
     */
    WARNING("WARNING", "警告", "操作完成但有警告");
    
    /**
     * 状态代码
     */
    private final String code;
    
    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 状态描述
     */
    private final String description;
    
    OperationStatus(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    /**
     * 根据代码查找操作状态
     * 
     * @param code 状态代码
     * @return 操作状态，未找到返回null
     */
    public static OperationStatus fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (OperationStatus status : values()) {
            if (status.getCode().equalsIgnoreCase(code.trim())) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为最终状态（不会再变化）
     * 
     * @return 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == SUCCESS || this == FAILED || this == CANCELLED || this == TIMEOUT;
    }
    
    /**
     * 判断是否为成功状态
     * 
     * @return 是否为成功状态
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }
    
    /**
     * 判断是否为错误状态
     * 
     * @return 是否为错误状态
     */
    public boolean isError() {
        return this == FAILED || this == TIMEOUT;
    }
}