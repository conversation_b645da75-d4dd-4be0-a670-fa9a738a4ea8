package com.just.common.enums;

import lombok.Getter;

/**
 * 日志级别枚举
 * 用于统一管理日志级别
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
@Getter
public enum LogLevel {
    
    /**
     * 调试级别
     */
    DEBUG("DEBUG", "调试", 1),
    
    /**
     * 信息级别
     */
    INFO("INFO", "信息", 2),
    
    /**
     * 警告级别
     */
    WARN("WARN", "警告", 3),
    
    /**
     * 错误级别
     */
    ERROR("ERROR", "错误", 4),
    
    /**
     * 致命错误级别
     */
    FATAL("FATAL", "致命错误", 5);
    
    /**
     * 级别代码
     */
    private final String code;
    
    /**
     * 级别名称
     */
    private final String name;
    
    /**
     * 级别优先级（数字越大优先级越高）
     */
    private final int priority;
    
    LogLevel(String code, String name, int priority) {
        this.code = code;
        this.name = name;
        this.priority = priority;
    }
    
    /**
     * 根据代码查找日志级别
     * 
     * @param code 级别代码
     * @return 日志级别，未找到返回null
     */
    public static LogLevel fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (LogLevel level : values()) {
            if (level.getCode().equalsIgnoreCase(code.trim())) {
                return level;
            }
        }
        return null;
    }
    
    /**
     * 判断当前级别是否高于指定级别
     * 
     * @param other 其他级别
     * @return 是否高于指定级别
     */
    public boolean isHigherThan(LogLevel other) {
        return this.priority > other.priority;
    }
    
    /**
     * 判断当前级别是否低于指定级别
     * 
     * @param other 其他级别
     * @return 是否低于指定级别
     */
    public boolean isLowerThan(LogLevel other) {
        return this.priority < other.priority;
    }
    
    /**
     * 判断是否为错误级别
     * 
     * @return 是否为错误级别
     */
    public boolean isError() {
        return this == ERROR || this == FATAL;
    }
    
    /**
     * 判断是否为警告级别或以上
     * 
     * @return 是否为警告级别或以上
     */
    public boolean isWarningOrAbove() {
        return this.priority >= WARN.priority;
    }
}