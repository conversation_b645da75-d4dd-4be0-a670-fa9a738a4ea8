package com.just.git;

import com.just.git.config.GitConfig;
import com.just.git.config.GitCoreAutoConfiguration;
import com.just.git.model.*;
import com.just.git.service.GitOperationService;
import com.just.git.service.GitProjectService;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;


/**
 * Git基本操作测试
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {GitCoreAutoConfiguration.class})
public class BasicGitOperationsTest {
    
    @Autowired
    private GitOperationService gitOperationService;
    @Resource
    private GitProjectService gitProjectService;
    
    @Autowired
    private GitConfig gitConfig;
    
    private static final String TEST_PROJECT_PATH = System.getProperty("user.home") + "/git-workspace/test-basic";
    
    private Path testProjectPath;
    private GitRepository repository;
    @Before
    public void setUp() throws IOException, ExecutionException, InterruptedException {
        testProjectPath = Paths.get(TEST_PROJECT_PATH);
        CompletableFuture<List<GitLabProject>> listCompletableFuture = gitProjectService.loadAllProjects();
        listCompletableFuture.get();
        // 清理可能存在的旧测试目录
        if (Files.exists(testProjectPath)) {
            deleteDirectory(testProjectPath.toFile());
        }

        log.info("基本Git操作测试环境设置完成");
        log.info("测试项目路径: {}", TEST_PROJECT_PATH);
    }


    
    @Test
    public void testCloneRepository() {
        try {
            log.info("测试克隆仓库...");
            GitLabProject gitLabProjects = gitProjectService.searchProjectsByName("onshore-bond-abs").stream().findFirst().get();
            GitCloneRequest cloneRequest = GitCloneRequest.builder()
                    .repositoryUrl(gitLabProjects.getWebUrl())
                    .localPath(testProjectPath)
                    .branch("master")
                    .credentials(gitConfig.getDefaultCredentials())
                    .build();

             repository = gitOperationService.cloneRepository(cloneRequest);

            log.info("仓库克隆成功");
            log.info("本地路径: {}", repository.getLocalPath());
            log.info("仓库URL: {}", repository.getRepositoryUrl());
            log.info("当前分支: {}", repository.getCurrentBranch());
            
        } catch (Exception e) {
            log.error("仓库克隆测试失败", e);
            throw new RuntimeException("仓库克隆测试失败", e);
        }
    }
    
    @Test
    public void testBranchOperations() {
        try {
            log.info("测试分支操作...");
            testCloneRepository();
            // 列出所有分支
            List<String> branches = gitOperationService.listBranches(repository, false);
            log.info("本地分支列表: {}", String.join(", ", branches));
            
            // 获取当前分支
            String currentBranch = gitOperationService.getCurrentBranch(repository);
            log.info("当前分支: {}", currentBranch);
            
            // 创建新分支（测试分支）
            String testBranchName = "test-feature-" + System.currentTimeMillis();
            GitBranchRequest createRequest = GitBranchRequest.builder()
                    .operation(GitBranchRequest.BranchOperation.CREATE)
                    .branchName(testBranchName)
                    .baseBranch(currentBranch)
                    .force(false)
                    .build();
            
            GitOperationResult createResult = gitOperationService.branchOperation(repository, createRequest);
            if (createResult.isSuccess()) {
                log.info("分支创建成功: {}", testBranchName);
            } else {
                log.warn("分支创建失败: {}", createResult.getErrorMessage());
            }
            createRequest.setOperation(GitBranchRequest.BranchOperation.SWITCH);
            gitOperationService.branchOperation(repository,createRequest);

        } catch (Exception e) {
            log.error("分支操作测试失败", e);
        }
    }
    
    @Test
    public void testRepositoryStatus() {
        try {
            log.info("测试仓库状态检查...");
            
            // 首先需要克隆仓库
            if (repository == null) {
                testCloneRepository();
            }
            
            // 检查仓库状态
            GitRepository.RepositoryStatus status = gitOperationService.getRepositoryStatus(repository);
            log.info("仓库状态: {}", status);
            
            // 检查是否有未提交的更改
            boolean hasUncommittedChanges = gitOperationService.hasUncommittedChanges(repository);
            log.info("是否有未提交更改: {}", hasUncommittedChanges);
            
            // 获取未提交的文件列表
            List<String> uncommittedFiles = gitOperationService.getUncommittedFiles(repository);
            if (!uncommittedFiles.isEmpty()) {
                log.info("未提交的文件:");
                uncommittedFiles.forEach(file -> log.info("  - {}", file));
            } else {
                log.info("没有未提交的文件");
            }
            
        } catch (Exception e) {
            log.error("仓库状态检查失败", e);
        }
    }
    
    @Test
    public void testCredentialsTypes() {
        log.info("测试不同类型的认证方式...");
        
        // 测试Token认证
        GitCredentials tokenCreds = null;
        log.info("Token认证创建成功: {}", tokenCreds.getAuthType());
        log.info("Token认证有效性: {}", tokenCreds.isValid());
        
        // 测试用户名密码认证
        GitCredentials userPassCreds = GitCredentials.ofUsernamePassword("testuser", "testpass");
        log.info("用户名密码认证创建成功: {}", userPassCreds.getAuthType());
        log.info("用户名密码认证有效性: {}", userPassCreds.isValid());
        
        // 测试SSH密钥认证
        GitCredentials sshCreds = GitCredentials.ofSshKey("/path/to/ssh/key", "password");
        log.info("SSH密钥认证创建成功: {}", sshCreds.getAuthType());
        log.info("SSH密钥认证有效性: {}", sshCreds.isValid());
    }
    
    @Test
    public void testPullOperation() {
        try {
            log.info("测试拉取操作...");
            
            // 首先需要克隆仓库
            if (repository == null) {
                testCloneRepository();
            }
            
            GitOperationResult pullResult = gitOperationService.pull(repository);
            if (pullResult.isSuccess()) {
                log.info("拉取操作成功: {}", pullResult.getMessage());
            } else {
                log.warn("拉取操作失败: {}", pullResult.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("拉取操作测试失败", e);
        }
    }
    
    @After
    public void tearDown() {
        // 关闭仓库
        if (repository != null) {
            try {
                gitOperationService.closeRepository(repository);
                log.info("仓库已关闭");
            } catch (Exception e) {
                log.warn("关闭仓库时发生错误: {}", e.getMessage());
            }
        }
        
        // 清理测试目录
        if (testProjectPath != null && Files.exists(testProjectPath)) {
            try {
                deleteDirectory(testProjectPath.toFile());
                log.info("测试目录已清理: {}", testProjectPath);
            } catch (Exception e) {
                log.warn("清理测试目录时发生错误: {}", e.getMessage());
            }
        }
    }
    
    /**
     * 递归删除目录
     */
    private void deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }
} 