package com.just.template.config;

import com.just.template.core.TemplateEngine;
import com.just.template.engine.FreeMarkerTemplateEngine;
import com.just.template.factory.TypedTemplateFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 模板自动配置
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
@Configuration
public class TemplateAutoConfiguration {
    
    /**
     * 创建默认的FreeMarker模板引擎
     */
    @Bean
    @ConditionalOnMissingBean(TemplateEngine.class)
    public TemplateEngine templateEngine() {
        return new FreeMarkerTemplateEngine();
    }
    
    /**
     * 创建默认的模板工厂
     */
    @Bean
    @ConditionalOnMissingBean(TypedTemplateFactory.class)
    public TypedTemplateFactory templateFactory(TemplateEngine templateEngine) {
        return new TypedTemplateFactory(templateEngine);
    }
} 