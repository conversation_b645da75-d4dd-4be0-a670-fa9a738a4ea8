package com.just.file.service;

import com.just.file.config.ExcelConfigProperties;
import com.just.file.model.*;
import com.just.file.utils.ExcelReaderHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * k8s映射配置处理器
 * 专门处理k8s映射配置
 *
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class K8sKeyValueConfigurationProcessor implements ConfigurationProcessor {

    private final ExcelReaderHelper excelReaderHelper;

    @Override
    public ConfigurationType getSupportedType() {
        return ConfigurationType.KEY_VALUE;
    }

    @Override
    public ProcessingResult processWithMetadata(ExcelConfigProperties.FileConfig fileConfig, ProcessingContext context) {
        String environment = context.getEnvironment();
        log.info("开始处理k8s映射配置，文件: {}，环境: {}", fileConfig.getPath(), environment);
        try {
            processAllSheets(fileConfig, context);
            ConfigurationMetadata metadata = buildProcessingMetadata(fileConfig, context);
            log.info("成功处理所有k8s配置，总属性数: {}", context.getAllProperties().size());
            return ProcessingResult.success(ConfigurationType.KEY_VALUE, context.getAllProperties(), metadata);

        } catch (Exception e) {
            log.error("处理k8s映射配置失败，环境: {}", environment, e);
            return ProcessingResult.failure(ConfigurationType.KEY_VALUE, "处理失败: " + e.getMessage());
        }
    }

    @Override
    public boolean validate(ExcelConfigProperties.FileConfig fileConfig) {
        return fileConfig != null &&
                fileConfig.getPath() != null &&
                fileConfig.getSheets() != null &&
                !fileConfig.getSheets().isEmpty() &&
                fileConfig.isEnabled();
    }


    /**
     * 处理所有工作表
     */
    private void processAllSheets(ExcelConfigProperties.FileConfig fileConfig, ProcessingContext context) {
        for (String sheetName : fileConfig.getSheets()) {
            try {
                processSheet(fileConfig.getPath(), sheetName, context);
                log.debug("成功处理工作表: {}", sheetName);
            } catch (Exception e) {
                handleSheetError(sheetName, e, context);
            }
        }
    }

    /**
     * 处理单个工作表
     */
    private void processSheet(String filePath, String sheetName, ProcessingContext context) {
        List<List<Object>> rows = excelReaderHelper.readSheet(filePath, sheetName);
        context.addTotalRows(rows.size());

        for (int i = 0; i < rows.size(); i++) {
            List<Object> row = rows.get(i);
            if (!excelReaderHelper.isEmptyRow(row)) {
                processRow(filePath, sheetName, row, i + 1, context);
            }
        }
    }

    /**
     * 处理单行数据
     */
    private void processRow(String filePath, String sheetName, List<Object> row, int rowIndex, ProcessingContext context) {
        try {
            String stringValue = excelReaderHelper.getStringValue(row, 0).replace("：", ":").replaceAll("\"", "");
            String[] split = stringValue.split(":");
            if (split.length < 2) {
                return;
            }
            String k8sEnv = split[0];
            String nacosEnv = stringValue.substring(stringValue.indexOf(":") + 1);

            ConfigurationDataCenter.NacosProperty property = createNacosProperty(
                    filePath, sheetName, rowIndex, k8sEnv, nacosEnv, "k8s_env", context.getEnvironment());

            if (property.isValid()) {
                context.addValidProperty(property);
            } else {
                addValidationError(context, rowIndex, "行数据不完整", String.valueOf(row));
            }
        } catch (Exception e) {
            addValidationError(context, rowIndex, "处理行数据失败: " + e.getMessage(), String.valueOf(row));
        }
    }

    /**
     * 创建Nacos属性对象
     */
    private ConfigurationDataCenter.NacosProperty createNacosProperty(
            String filePath, String sheetName, int rowIndex,
            String k8sEnv, String nacosEnv, String nacosFileName, String environment) {

        return ConfigurationDataCenter.NacosProperty.builder()
                .fileName(filePath)
                .environment(environment)
                .sheetName(sheetName)
                .type(ConfigurationType.NACOS)
                .rowIndex(rowIndex)
                .k8sEnv(k8sEnv)
                .nacosEnv(nacosEnv)
                .nacosFileName(nacosFileName)
                .build();
    }

    /**
     * 处理工作表错误
     */
    private void handleSheetError(String sheetName, Exception e, ProcessingContext context) {
        log.warn("处理工作表 {} 失败: {}", sheetName, e.getMessage());
        context.addValidationError(ConfigurationMetadata.ValidationError.builder()
                .errorCode("SHEET_READ_ERROR")
                .message("读取工作表失败: " + e.getMessage())
                .rowIndex(0)
                .columnIndex(0)
                .errorValue(sheetName)
                .level(ConfigurationMetadata.ErrorLevel.ERROR)
                .build());
    }

    /**
     * 添加验证错误
     */
    private void addValidationError(ProcessingContext context, int rowIndex, String message, String errorValue) {
        context.addValidationError(ConfigurationMetadata.ValidationError.builder()
                .errorCode("INVALID_ROW")
                .message(message)
                .rowIndex(rowIndex)
                .columnIndex(0)
                .errorValue(errorValue)
                .level(ConfigurationMetadata.ErrorLevel.WARN)
                .build());
    }

    /**
     * 构建处理元数据
     */
    private ConfigurationMetadata buildProcessingMetadata(ExcelConfigProperties.FileConfig fileConfig, ProcessingContext context) {
        long endTime = System.currentTimeMillis();
        long duration = endTime - context.getStartTime();

        return ConfigurationMetadata.builder()
                .totalRows(context.getTotalRows())
                .validRows(context.getValidRows())
                .readDuration(java.time.Duration.ofMillis(duration))
                .processingDuration(java.time.Duration.ofMillis(duration))
                .readStartTime(java.time.LocalDateTime.now().minusNanos(duration * 1000000))
                .readEndTime(java.time.LocalDateTime.now())
                .sheetCount(context.getTotalSheets())
                .validationErrors(context.getValidationErrors())
                .processorInfo(this.getClass().getSimpleName())
                .cacheHit(false)
                .dataSource(fileConfig.getPath())
                .build();
    }


} 