package com.just.common.exception;

import com.just.common.constants.ErrorCodes;
import lombok.Getter;

/**
 * 系统异常
 * 用于系统层面的不可预期异常
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
@Getter
public class SystemException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private final String errorCode;
    
    /**
     * 错误消息
     */
    private final String errorMessage;
    
    /**
     * 错误详情（可选）
     */
    private final Object errorDetails;
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     */
    public SystemException(String message) {
        super(message);
        this.errorCode = ErrorCodes.SYSTEM_ERROR;
        this.errorMessage = message;
        this.errorDetails = null;
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param message 错误消息
     */
    public SystemException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.errorMessage = message;
        this.errorDetails = null;
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param message 错误消息
     * @param errorDetails 错误详情
     */
    public SystemException(String errorCode, String message, Object errorDetails) {
        super(message);
        this.errorCode = errorCode;
        this.errorMessage = message;
        this.errorDetails = errorDetails;
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原因异常
     */
    public SystemException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = ErrorCodes.SYSTEM_ERROR;
        this.errorMessage = message;
        this.errorDetails = null;
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param message 错误消息
     * @param cause 原因异常
     */
    public SystemException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorMessage = message;
        this.errorDetails = null;
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param message 错误消息
     * @param errorDetails 错误详情
     * @param cause 原因异常
     */
    public SystemException(String errorCode, String message, Object errorDetails, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorMessage = message;
        this.errorDetails = errorDetails;
    }
    
    // ======================== 便捷方法 ========================
    
    /**
     * 创建文件操作异常
     * 
     * @param fileName 文件名
     * @param operation 操作类型
     * @param cause 原因异常
     * @return 系统异常
     */
    public static SystemException fileOperationError(String fileName, String operation, Throwable cause) {
        return new SystemException(ErrorCodes.FILE_READ_ERROR, 
                String.format("文件操作失败: %s，操作: %s", fileName, operation), cause);
    }
    
    /**
     * 创建网络连接异常
     * 
     * @param url 连接地址
     * @param cause 原因异常
     * @return 系统异常
     */
    public static SystemException networkError(String url, Throwable cause) {
        return new SystemException(ErrorCodes.SYSTEM_ERROR, 
                "网络连接失败: " + url, cause);
    }
    
    /**
     * 创建数据库操作异常
     * 
     * @param operation 操作类型
     * @param cause 原因异常
     * @return 系统异常
     */
    public static SystemException databaseError(String operation, Throwable cause) {
        return new SystemException(ErrorCodes.SYSTEM_ERROR, 
                "数据库操作失败: " + operation, cause);
    }
    
    /**
     * 创建序列化异常
     * 
     * @param objectType 对象类型
     * @param cause 原因异常
     * @return 系统异常
     */
    public static SystemException serializationError(String objectType, Throwable cause) {
        return new SystemException(ErrorCodes.SYSTEM_ERROR, 
                "序列化失败: " + objectType, cause);
    }
    
    /**
     * 创建反序列化异常
     * 
     * @param objectType 对象类型
     * @param cause 原因异常
     * @return 系统异常
     */
    public static SystemException deserializationError(String objectType, Throwable cause) {
        return new SystemException(ErrorCodes.SYSTEM_ERROR, 
                "反序列化失败: " + objectType, cause);
    }
    
    /**
     * 创建配置加载异常
     * 
     * @param configName 配置名称
     * @param cause 原因异常
     * @return 系统异常
     */
    public static SystemException configLoadError(String configName, Throwable cause) {
        return new SystemException(ErrorCodes.CONFIG_ERROR, 
                "配置加载失败: " + configName, cause);
    }
    
    /**
     * 创建外部服务调用异常
     * 
     * @param serviceName 服务名称
     * @param cause 原因异常
     * @return 系统异常
     */
    public static SystemException externalServiceError(String serviceName, Throwable cause) {
        return new SystemException(ErrorCodes.SYSTEM_ERROR, 
                "外部服务调用失败: " + serviceName, cause);
    }
    
    @Override
    public String toString() {
        return String.format("SystemException{errorCode='%s', errorMessage='%s', errorDetails=%s}", 
                errorCode, errorMessage, errorDetails);
    }
}