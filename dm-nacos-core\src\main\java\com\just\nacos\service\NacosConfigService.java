package com.just.nacos.service;

import com.just.nacos.model.NacosConfig;

import java.util.List;
import java.util.Optional;

/**
 * Nacos配置管理服务接口
 *
 * <AUTHOR>
 * @since 2.0.0
 */
public interface NacosConfigService {

    /**
     * 发布配置
     *
     * @param config 配置信息
     * @return 是否成功
     */
    boolean publishConfig(NacosConfig config);

    /**
     * 获取配置
     *
     * @param dataId 配置ID
     * @param group 配置组
     * @return 配置信息
     */
    Optional<NacosConfig> getConfig(String dataId, String group);

    /**
     * 获取配置（指定命名空间）
     *
     * @param dataId 配置ID
     * @param group 配置组
     * @param namespace 命名空间
     * @return 配置信息
     */
    Optional<NacosConfig> getConfig(String dataId, String group, String namespace);

    /**
     * 删除配置
     *
     * @param dataId 配置ID
     * @param group 配置组
     * @return 是否成功
     */
    boolean deleteConfig(String dataId, String group);

    /**
     * 删除配置（指定命名空间）
     *
     * @param dataId 配置ID
     * @param group 配置组
     * @param namespace 命名空间
     * @return 是否成功
     */
    boolean deleteConfig(String dataId, String group, String namespace);

    /**
     * 列出配置（分页）
     *
     * @param pageNo 页号（从1开始）
     * @param pageSize 页大小
     * @return 配置列表
     */
    List<NacosConfig> listConfigs(int pageNo, int pageSize);

    /**
     * 列出配置（分页，指定分组和命名空间）
     *
     * @param pageNo 页号（从1开始）
     * @param pageSize 页大小
     * @param group 配置组
     * @param namespace 命名空间
     * @return 配置列表
     */
    List<NacosConfig> listConfigs(int pageNo, int pageSize, String group, String namespace);

    /**
     * 检查配置是否存在
     *
     * @param dataId 配置ID
     * @param group 配置组
     * @return 是否存在
     */
    boolean configExists(String dataId, String group);

    /**
     * 检查配置是否存在（指定命名空间）
     *
     * @param dataId 配置ID
     * @param group 配置组
     * @param namespace 命名空间
     * @return 是否存在
     */
    boolean configExists(String dataId, String group, String namespace);

    /**
     * 批量发布配置
     *
     * @param configs 配置列表
     * @return 成功发布的配置数量
     */
    int batchPublishConfigs(List<NacosConfig> configs);

    /**
     * 批量删除配置
     *
     * @param configs 要删除的配置信息（只需要dataId、group、namespace）
     * @return 成功删除的配置数量
     */
    int batchDeleteConfigs(List<NacosConfig> configs);

    /**
     * 验证配置格式
     *
     * @param content 配置内容
     * @param type 配置类型
     * @return 验证结果信息
     */
    String validateConfigFormat(String content, String type);

    /**
     * 格式化配置内容
     *
     * @param content 配置内容
     * @param type 配置类型
     * @return 格式化后的内容
     */
    String formatConfigContent(String content, String type);
} 