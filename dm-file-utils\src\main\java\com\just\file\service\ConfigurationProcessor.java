package com.just.file.service;

import com.just.file.config.ExcelConfigProperties;
import com.just.file.model.ConfigurationType;
import com.just.file.model.ProcessingContext;
import com.just.file.model.ProcessingResult;

/**
 * 配置处理器接口
 * 定义不同配置类型的统一处理方式
 *
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
public interface ConfigurationProcessor {

    /**
     * 获取支持的配置类型
     */
    ConfigurationType getSupportedType();

    /**
     * 处理配置数据和构建元数据（一次性完成，避免重复读取文件）
     *
     * @param fileConfig 文件配置
     * @return 处理结果，包含配置数据和元数据
     */
    ProcessingResult processWithMetadata(ExcelConfigProperties.FileConfig fileConfig, ProcessingContext context);


    /**
     * 验证配置是否有效
     *
     * @param fileConfig 文件配置
     * @return 是否有效
     */
    boolean validate(ExcelConfigProperties.FileConfig fileConfig);
} 