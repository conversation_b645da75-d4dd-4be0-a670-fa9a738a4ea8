package com.just.git.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Git分支操作请求
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GitBranchRequest {
    
    /**
     * 分支操作类型
     */
    public enum BranchOperation {
        CREATE,         // 创建分支
        SWITCH,         // 切换分支
        CREATE_AND_SWITCH,  // 创建并切换分支
        DELETE,         // 删除分支
        MERGE           // 合并分支
    }
    
    /**
     * 操作类型
     */
    private BranchOperation operation;
    
    /**
     * 目标分支名称
     */
    private String branchName;
    
    /**
     * 基础分支名称（用于创建新分支）
     */
    private String baseBranch;
    
    /**
     * 是否为远程分支
     */
    private boolean isRemote = false;
    
    /**
     * 是否强制操作
     */
    private boolean force = false;
    
    /**
     * 是否在操作后推送到远程
     */
    private boolean pushToRemote = false;
    
    /**
     * 是否跟踪远程分支
     */
    private boolean trackRemote = true;
    
    /**
     * 合并策略（用于MERGE操作）
     */
    private String mergeStrategy;
    
    /**
     * 验证请求参数
     */
    public void validate() {
        if (operation == null) {
            throw new IllegalArgumentException("operation不能为空");
        }
        
        if (branchName == null || branchName.trim().isEmpty()) {
            throw new IllegalArgumentException("branchName不能为空");
        }
        
        if (operation == BranchOperation.CREATE || operation == BranchOperation.CREATE_AND_SWITCH) {
            if (baseBranch == null || baseBranch.trim().isEmpty()) {
                throw new IllegalArgumentException("创建分支时baseBranch不能为空");
            }
        }
        
        if (operation == BranchOperation.MERGE) {
            if (baseBranch == null || baseBranch.trim().isEmpty()) {
                throw new IllegalArgumentException("合并分支时baseBranch不能为空");
            }
        }
    }
    
    /**
     * 创建分支切换请求
     */
    public static GitBranchRequest createSwitchRequest(String branchName) {
        return GitBranchRequest.builder()
                .operation(BranchOperation.SWITCH)
                .branchName(branchName)
                .build();
    }
    
    /**
     * 创建新分支请求
     */
    public static GitBranchRequest createNewBranchRequest(String branchName, String baseBranch) {
        return GitBranchRequest.builder()
                .operation(BranchOperation.CREATE_AND_SWITCH)
                .branchName(branchName)
                .baseBranch(baseBranch)
                .build();
    }
    
    /**
     * 创建合并请求
     */
    public static GitBranchRequest createMergeRequest(String targetBranch, String sourceBranch) {
        return GitBranchRequest.builder()
                .operation(BranchOperation.MERGE)
                .branchName(targetBranch)
                .baseBranch(sourceBranch)
                .build();
    }
} 