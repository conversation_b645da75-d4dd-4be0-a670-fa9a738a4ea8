package com.just.upgrade.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * 异步配置类
 * 为配置预加载提供专用线程池
 * 
 * <AUTHOR> Auto Utils
 * @version 3.0.0
 */
@Slf4j
@Configuration
@EnableAsync
public class AsyncConfig {

    /**
     * 配置预加载线程池
     */
    @Bean("configPreloadExecutor")
    public Executor configPreloadExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(4);
        executor.setMaxPoolSize(8);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("config-preload-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        
        executor.setRejectedExecutionHandler((r, executor1) -> {
            log.warn("配置预加载任务被拒绝执行: {}", r.toString());
            throw new RuntimeException("配置预加载任务队列已满");
        });
        
        executor.initialize();
        
        log.info("配置预加载线程池已初始化: 核心线程数={}, 最大线程数={}, 队列容量={}", 
            executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }
} 