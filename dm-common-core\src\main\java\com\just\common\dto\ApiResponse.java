package com.just.common.dto;

import com.just.common.constants.ErrorCodes;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 统一API响应格式
 * 用于所有REST API的响应数据封装
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 响应是否成功
     */
    private boolean success;
    
    /**
     * 响应状态码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 响应时间戳
     */
    @Builder.Default
    private LocalDateTime timestamp = LocalDateTime.now();
    
    /**
     * 请求ID（用于链路追踪）
     */
    private String requestId;
    
    /**
     * 创建成功响应
     * 
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success() {
        return ApiResponse.<T>builder()
                .success(true)
                .code(ErrorCodes.SUCCESS)
                .message("操作成功")
                .build();
    }
    
    /**
     * 创建成功响应（带数据）
     * 
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return ApiResponse.<T>builder()
                .success(true)
                .code(ErrorCodes.SUCCESS)
                .message("操作成功")
                .data(data)
                .build();
    }
    
    /**
     * 创建成功响应（带数据和消息）
     * 
     * @param data 响应数据
     * @param message 响应消息
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(T data, String message) {
        return ApiResponse.<T>builder()
                .success(true)
                .code(ErrorCodes.SUCCESS)
                .message(message)
                .data(data)
                .build();
    }
    
    /**
     * 创建失败响应
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> ApiResponse<T> error(String message) {
        return ApiResponse.<T>builder()
                .success(false)
                .code(ErrorCodes.SYSTEM_ERROR)
                .message(message)
                .build();
    }
    
    /**
     * 创建失败响应（带错误码）
     * 
     * @param code 错误码
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> ApiResponse<T> error(String code, String message) {
        return ApiResponse.<T>builder()
                .success(false)
                .code(code)
                .message(message)
                .build();
    }
    
    /**
     * 创建参数错误响应
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 参数错误响应
     */
    public static <T> ApiResponse<T> paramError(String message) {
        return ApiResponse.<T>builder()
                .success(false)
                .code(ErrorCodes.PARAM_ERROR)
                .message(message)
                .build();
    }
    
    /**
     * 创建系统错误响应
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 系统错误响应
     */
    public static <T> ApiResponse<T> systemError(String message) {
        return ApiResponse.<T>builder()
                .success(false)
                .code(ErrorCodes.SYSTEM_ERROR)
                .message(message)
                .build();
    }
    
    /**
     * 创建权限不足响应
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 权限不足响应
     */
    public static <T> ApiResponse<T> permissionDenied(String message) {
        return ApiResponse.<T>builder()
                .success(false)
                .code(ErrorCodes.PERMISSION_DENIED)
                .message(message)
                .build();
    }
    
    /**
     * 创建资源不存在响应
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 资源不存在响应
     */
    public static <T> ApiResponse<T> notFound(String message) {
        return ApiResponse.<T>builder()
                .success(false)
                .code(ErrorCodes.RESOURCE_NOT_FOUND)
                .message(message)
                .build();
    }
    
    /**
     * 设置请求ID
     * 
     * @param requestId 请求ID
     * @return 当前对象（支持链式调用）
     */
    public ApiResponse<T> withRequestId(String requestId) {
        this.requestId = requestId;
        return this;
    }
    
    /**
     * 设置时间戳
     * 
     * @param timestamp 时间戳
     * @return 当前对象（支持链式调用）
     */
    public ApiResponse<T> withTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
        return this;
    }
}