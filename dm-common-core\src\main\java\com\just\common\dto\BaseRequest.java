package com.just.common.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 基础请求DTO
 * 所有请求DTO的基类，包含通用的字段和验证规则
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
@Data
@EqualsAndHashCode
public class BaseRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 请求ID（用于链路追踪）
     */
    private String requestId;
    
    /**
     * 请求时间戳
     */
    private LocalDateTime requestTime;
    
    /**
     * 请求来源
     */
    private String source;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 分页页码（从1开始）
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;
    
    /**
     * 分页大小
     */
    @Min(value = 1, message = "分页大小必须大于0")
    private Integer size = 20;
    
    /**
     * 排序字段
     */
    private String sortBy;
    
    /**
     * 排序方向（ASC/DESC）
     */
    private String sortDir = "DESC";
    
    /**
     * 搜索关键字
     */
    private String keyword;
    
    /**
     * 设置请求ID
     * 
     * @param requestId 请求ID
     * @return 当前对象（支持链式调用）
     */
    public BaseRequest withRequestId(String requestId) {
        this.requestId = requestId;
        return this;
    }
    
    /**
     * 设置请求时间戳
     * 
     * @param requestTime 请求时间戳
     * @return 当前对象（支持链式调用）
     */
    public BaseRequest withRequestTime(LocalDateTime requestTime) {
        this.requestTime = requestTime;
        return this;
    }
    
    /**
     * 设置用户信息
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @return 当前对象（支持链式调用）
     */
    public BaseRequest withUser(String userId, String username) {
        this.userId = userId;
        this.username = username;
        return this;
    }
    
    /**
     * 设置客户端信息
     * 
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @return 当前对象（支持链式调用）
     */
    public BaseRequest withClient(String clientIp, String userAgent) {
        this.clientIp = clientIp;
        this.userAgent = userAgent;
        return this;
    }
    
    /**
     * 设置分页信息
     * 
     * @param page 页码
     * @param size 分页大小
     * @return 当前对象（支持链式调用）
     */
    public BaseRequest withPagination(Integer page, Integer size) {
        this.page = page;
        this.size = size;
        return this;
    }
    
    /**
     * 设置排序信息
     * 
     * @param sortBy 排序字段
     * @param sortDir 排序方向
     * @return 当前对象（支持链式调用）
     */
    public BaseRequest withSort(String sortBy, String sortDir) {
        this.sortBy = sortBy;
        this.sortDir = sortDir;
        return this;
    }
    
    /**
     * 获取偏移量（用于数据库查询）
     * 
     * @return 偏移量
     */
    public int getOffset() {
        return (page - 1) * size;
    }
    
    /**
     * 获取限制数量（用于数据库查询）
     * 
     * @return 限制数量
     */
    public int getLimit() {
        return size;
    }
    
    /**
     * 判断是否为升序排序
     * 
     * @return 是否为升序
     */
    public boolean isAscending() {
        return "ASC".equalsIgnoreCase(sortDir);
    }
    
    /**
     * 判断是否为降序排序
     * 
     * @return 是否为降序
     */
    public boolean isDescending() {
        return "DESC".equalsIgnoreCase(sortDir);
    }
    
    /**
     * 判断是否有搜索关键字
     * 
     * @return 是否有搜索关键字
     */
    public boolean hasKeyword() {
        return keyword != null && !keyword.trim().isEmpty();
    }
    
    /**
     * 获取清理后的搜索关键字
     * 
     * @return 清理后的关键字
     */
    public String getCleanKeyword() {
        return hasKeyword() ? keyword.trim() : null;
    }
}