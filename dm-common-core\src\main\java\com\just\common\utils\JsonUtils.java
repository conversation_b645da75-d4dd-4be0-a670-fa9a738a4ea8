package com.just.common.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.just.common.exception.SystemException;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * JSON工具类
 * 提供JSON序列化和反序列化功能
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
@Slf4j
public final class JsonUtils {
    
    private JsonUtils() {
        // 私有构造函数，防止实例化
    }
    
    /**
     * ObjectMapper实例（线程安全）
     */
    private static final ObjectMapper OBJECT_MAPPER = createObjectMapper();
    
    /**
     * 创建ObjectMapper实例
     * 
     * @return ObjectMapper实例
     */
    private static ObjectMapper createObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        // 注册Java 8时间模块
        mapper.registerModule(new JavaTimeModule());
        // 禁用将日期写为时间戳
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        // 忽略未知属性
        mapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return mapper;
    }
    
    /**
     * 获取ObjectMapper实例
     * 
     * @return ObjectMapper实例
     */
    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER;
    }
    
    /**
     * 将对象转换为JSON字符串
     * 
     * @param object 对象
     * @return JSON字符串
     * @throws SystemException 序列化失败时抛出
     */
    public static String toJson(Object object) {
        if (object == null) {
            return null;
        }
        
        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("对象序列化为JSON失败: {}", object.getClass().getName(), e);
            throw SystemException.serializationError(object.getClass().getName(), e);
        }
    }
    
    /**
     * 将对象转换为格式化的JSON字符串
     * 
     * @param object 对象
     * @return 格式化的JSON字符串
     * @throws SystemException 序列化失败时抛出
     */
    public static String toPrettyJson(Object object) {
        if (object == null) {
            return null;
        }
        
        try {
            return OBJECT_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("对象序列化为格式化JSON失败: {}", object.getClass().getName(), e);
            throw SystemException.serializationError(object.getClass().getName(), e);
        }
    }
    
    /**
     * 将JSON字符串转换为对象
     * 
     * @param json JSON字符串
     * @param clazz 目标类型
     * @param <T> 类型参数
     * @return 转换后的对象
     * @throws SystemException 反序列化失败时抛出
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (IOException e) {
            log.error("JSON反序列化失败: {} -> {}", json, clazz.getName(), e);
            throw SystemException.deserializationError(clazz.getName(), e);
        }
    }
    
    /**
     * 将JSON字符串转换为对象（支持泛型）
     * 
     * @param json JSON字符串
     * @param typeReference 类型引用
     * @param <T> 类型参数
     * @return 转换后的对象
     * @throws SystemException 反序列化失败时抛出
     */
    public static <T> T fromJson(String json, TypeReference<T> typeReference) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        
        try {
            return OBJECT_MAPPER.readValue(json, typeReference);
        } catch (IOException e) {
            log.error("JSON反序列化失败: {} -> {}", json, typeReference.getType(), e);
            throw SystemException.deserializationError(typeReference.getType().getTypeName(), e);
        }
    }
    
    /**
     * 将JSON字符串转换为List
     * 
     * @param json JSON字符串
     * @param elementClass 元素类型
     * @param <T> 类型参数
     * @return 转换后的List
     * @throws SystemException 反序列化失败时抛出
     */
    public static <T> List<T> fromJsonToList(String json, Class<T> elementClass) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        
        try {
            return OBJECT_MAPPER.readValue(json, 
                    OBJECT_MAPPER.getTypeFactory().constructCollectionType(List.class, elementClass));
        } catch (IOException e) {
            log.error("JSON反序列化为List失败: {} -> List<{}>", json, elementClass.getName(), e);
            throw SystemException.deserializationError("List<" + elementClass.getName() + ">", e);
        }
    }
    
    /**
     * 将JSON字符串转换为Map
     * 
     * @param json JSON字符串
     * @return 转换后的Map
     * @throws SystemException 反序列化失败时抛出
     */
    public static Map<String, Object> fromJsonToMap(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        
        try {
            return OBJECT_MAPPER.readValue(json, new TypeReference<Map<String, Object>>() {});
        } catch (IOException e) {
            log.error("JSON反序列化为Map失败: {}", json, e);
            throw SystemException.deserializationError("Map<String, Object>", e);
        }
    }
    
    /**
     * 将对象转换为Map
     * 
     * @param object 对象
     * @return 转换后的Map
     * @throws SystemException 转换失败时抛出
     */
    public static Map<String, Object> objectToMap(Object object) {
        if (object == null) {
            return null;
        }
        
        try {
            return OBJECT_MAPPER.convertValue(object, new TypeReference<Map<String, Object>>() {});
        } catch (IllegalArgumentException e) {
            log.error("对象转换为Map失败: {}", object.getClass().getName(), e);
            throw SystemException.serializationError(object.getClass().getName(), e);
        }
    }
    
    /**
     * 将Map转换为对象
     * 
     * @param map Map对象
     * @param clazz 目标类型
     * @param <T> 类型参数
     * @return 转换后的对象
     * @throws SystemException 转换失败时抛出
     */
    public static <T> T mapToObject(Map<String, Object> map, Class<T> clazz) {
        if (map == null) {
            return null;
        }
        
        try {
            return OBJECT_MAPPER.convertValue(map, clazz);
        } catch (IllegalArgumentException e) {
            log.error("Map转换为对象失败: Map -> {}", clazz.getName(), e);
            throw SystemException.deserializationError(clazz.getName(), e);
        }
    }
    
    /**
     * 深度复制对象
     * 
     * @param object 源对象
     * @param clazz 目标类型
     * @param <T> 类型参数
     * @return 复制后的对象
     * @throws SystemException 复制失败时抛出
     */
    public static <T> T deepCopy(Object object, Class<T> clazz) {
        if (object == null) {
            return null;
        }
        
        String json = toJson(object);
        return fromJson(json, clazz);
    }
    
    /**
     * 验证JSON字符串是否有效
     * 
     * @param json JSON字符串
     * @return 是否有效
     */
    public static boolean isValidJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return false;
        }
        
        try {
            OBJECT_MAPPER.readTree(json);
            return true;
        } catch (IOException e) {
            return false;
        }
    }
    
    /**
     * 格式化JSON字符串
     * 
     * @param json 原始JSON字符串
     * @return 格式化后的JSON字符串
     * @throws SystemException 格式化失败时抛出
     */
    public static String formatJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return json;
        }
        
        try {
            Object object = OBJECT_MAPPER.readValue(json, Object.class);
            return OBJECT_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(object);
        } catch (IOException e) {
            log.error("JSON格式化失败: {}", json, e);
            throw SystemException.deserializationError("JSON格式化", e);
        }
    }
    
    /**
     * 压缩JSON字符串（移除空格和换行）
     * 
     * @param json 原始JSON字符串
     * @return 压缩后的JSON字符串
     * @throws SystemException 压缩失败时抛出
     */
    public static String compactJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return json;
        }
        
        try {
            Object object = OBJECT_MAPPER.readValue(json, Object.class);
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (IOException e) {
            log.error("JSON压缩失败: {}", json, e);
            throw SystemException.deserializationError("JSON压缩", e);
        }
    }
}