package com.just.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 分页结果DTO
 * 用于封装分页查询的结果数据
 * 
 * <AUTHOR> Auto Utils
 * @version 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageResult<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 当前页码（从1开始）
     */
    private int currentPage;
    
    /**
     * 每页大小
     */
    private int pageSize;
    
    /**
     * 总记录数
     */
    private long totalCount;
    
    /**
     * 总页数
     */
    private int totalPages;
    
    /**
     * 当前页数据
     */
    private List<T> records;
    
    /**
     * 是否有上一页
     */
    private boolean hasPrevious;
    
    /**
     * 是否有下一页
     */
    private boolean hasNext;
    
    /**
     * 是否为第一页
     */
    private boolean isFirst;
    
    /**
     * 是否为最后一页
     */
    private boolean isLast;
    
    /**
     * 创建分页结果
     * 
     * @param records 当前页数据
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @param totalCount 总记录数
     * @param <T> 数据类型
     * @return 分页结果
     */
    public static <T> PageResult<T> of(List<T> records, int currentPage, int pageSize, long totalCount) {
        int totalPages = (int) Math.ceil((double) totalCount / pageSize);
        
        return PageResult.<T>builder()
                .records(records)
                .currentPage(currentPage)
                .pageSize(pageSize)
                .totalCount(totalCount)
                .totalPages(totalPages)
                .hasPrevious(currentPage > 1)
                .hasNext(currentPage < totalPages)
                .isFirst(currentPage == 1)
                .isLast(currentPage == totalPages || totalPages == 0)
                .build();
    }
    
    /**
     * 创建空的分页结果
     * 
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @param <T> 数据类型
     * @return 空分页结果
     */
    public static <T> PageResult<T> empty(int currentPage, int pageSize) {
        return of(Collections.<T>emptyList(), currentPage, pageSize, 0);
    }
    
    /**
     * 获取当前页的记录数
     * 
     * @return 当前页记录数
     */
    public int getCurrentPageSize() {
        return records != null ? records.size() : 0;
    }
    
    /**
     * 是否为空页面
     * 
     * @return 是否为空
     */
    public boolean isEmpty() {
        return records == null || records.isEmpty();
    }
    
    /**
     * 获取当前页的开始记录序号（从1开始）
     * 
     * @return 开始记录序号
     */
    public long getStartIndex() {
        return isEmpty() ? 0 : (long) (currentPage - 1) * pageSize + 1;
    }
    
    /**
     * 获取当前页的结束记录序号
     * 
     * @return 结束记录序号
     */
    public long getEndIndex() {
        return isEmpty() ? 0 : getStartIndex() + getCurrentPageSize() - 1;
    }
    
    /**
     * 转换分页结果的数据类型
     * 
     * @param mapper 数据转换函数
     * @param <R> 目标数据类型
     * @return 转换后的分页结果
     */
    public <R> PageResult<R> map(java.util.function.Function<T, R> mapper) {
        List<R> mappedRecords = records != null ? 
                records.stream().map(mapper).collect(java.util.stream.Collectors.toList()) : 
                Collections.<R>emptyList();
        
        return PageResult.<R>builder()
                .records(mappedRecords)
                .currentPage(currentPage)
                .pageSize(pageSize)
                .totalCount(totalCount)
                .totalPages(totalPages)
                .hasPrevious(hasPrevious)
                .hasNext(hasNext)
                .isFirst(isFirst)
                .isLast(isLast)
                .build();
    }
}