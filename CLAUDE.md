# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

DM Auto Utils是一个基于Spring Boot的微服务自动化运维工具，主要用于Nacos配置管理、Git操作和项目升级。项目采用多模块架构，包含以下核心模块：

- `dm-common-core`: 公共核心模块，提供通用的基础功能
- `dm-nacos-core`: Nacos操作核心模块，封装Nacos配置管理
- `dm-git-core`: Git操作核心模块，使用JGit实现Git操作
- `dm-template-core`: 模板引擎核心模块，使用FreeMarker进行配置生成
- `dm-file-utils`: 文件操作工具模块，统一文件处理
- `dm-upgrade-center`: 升级中心模块，主应用模块

## 构建和运行

### 构建项目
```bash
# 清理并编译整个项目
mvn clean compile

# 运行测试
mvn test

# 打包（跳过测试）
mvn clean package -DskipTests

# 打包并运行测试
mvn clean package
```

### 运行应用
```bash
# 运行主应用（dm-upgrade-center模块）
cd dm-upgrade-center
mvn spring-boot:run

# 或者使用jar包运行
java -jar dm-upgrade-center/target/dm-upgrade-center-2.0.0-SNAPSHOT.jar
```

### 测试命令
```bash
# 运行所有测试
mvn test

# 运行特定模块测试
cd dm-nacos-core
mvn test -Dtest=NacosCoreBasicTest

# 运行特定测试类
mvn test -Dtest=NacosConfigServiceTest
```

## 项目架构

### 模块间依赖关系
```
dm-upgrade-center (主应用)
├── dm-common-core (基础核心)
├── dm-file-utils (文件操作)
├── dm-template-core (模板引擎)
├── dm-git-core → dm-common-core
├── dm-nacos-core → dm-common-core
└── dm-template-core → dm-common-core
```

### 核心技术栈
- Spring Boot 2.7.5
- Spring Cloud Alibaba 2021.0.5.0
- Nacos Client 2.2.0
- JGit 5.13.0.202109080827-r
- FreeMarker 2.3.31
- Hutool 5.8.25

## 配置管理

### 环境配置
项目支持多环境配置（dev/qa/uat/prd），主要配置文件：
- `dm-upgrade-center/src/main/resources/application.yml`: 主配置文件
- Excel配置文件：
  - `nacos环境变量对应文件.xlsx`: Nacos环境变量映射
  - `url.xlsx`: URL配置映射
  - `k8s环境变量.xlsx`: Kubernetes环境变量

### Nacos环境配置
在`application.yml`中配置了四套环境的Nacos连接信息：
- dev: 开发环境
- qa: 测试环境  
- uat: 预发环境
- prd: 生产环境（注意：生产环境需要额外确认）

## 核心功能模块

### 1. Nacos配置管理 (dm-nacos-core)
- 多环境Nacos连接管理
- 配置文件读取和发布
- 环境变量处理和验证
- MSE（微服务引擎）集成

### 2. Git操作 (dm-git-core) 
- 使用JGit进行纯Java Git操作
- 支持克隆、分支创建、提交、推送
- GitLab集成（通过HTTP API）
- 自动化分支管理

### 3. 配置模板生成 (dm-template-core)
- FreeMarker模板引擎
- 支持bootstrap.properties、application-nacos.properties生成
- POM文件模板处理
- 动态配置替换

### 4. 文件操作 (dm-file-utils)
- Excel文件读取和处理
- 配置文件生成和管理
- 文件备份和恢复
- 批量文件操作

## 重要工作流程

### 项目升级流程
1. 从GitLab克隆或使用本地项目
2. 读取Excel配置文件获取环境变量映射
3. 使用模板引擎生成配置文件
4. 处理Nacos配置和注册中心配置
5. 可选：发布配置到Nacos
6. 可选：提交变更到Git

### 环境变量处理
- 支持从Excel文件读取环境变量映射
- 自动识别未解析的环境变量（${VARIABLE_NAME}格式）
- 提供环境变量替换建议
- 支持不同环境的差异化配置

## 安全注意事项

- 敏感信息（access-key、secret-key、token）应通过环境变量注入
- 生产环境操作需要额外确认
- GitLab token和Nacos密钥需要妥善保管
- 配置文件自动备份机制

## 开发注意事项

### 项目特殊性
这是一个**运维自动化工具**，涉及生产环境配置管理，开发时需要特别注意：

1. **生产环境保护**：所有生产环境操作都需要额外确认机制
2. **配置备份**：修改配置文件前会自动创建备份
3. **敏感信息处理**：GitLab token、Nacos密钥等敏感信息必须通过环境变量注入
4. **Excel依赖**：项目依赖特定格式的Excel配置文件，修改时需谨慎

### 双接口架构
项目同时支持两种接口方式：
- **REST API** (新架构，推荐)：适合Web界面和API调用
- **Spring Shell** (传统方式)：适合命令行操作和调试

### 模块化架构说明
- `dm-upgrade-center`是主应用模块，依赖其他所有模块
- 各模块职责清晰，避免循环依赖
- 支持独立测试和部署

## 开发指导

### 添加新的配置处理器
在`dm-file-utils`模块中实现`ConfigurationProcessor`接口：
```java
@Component
public class CustomConfigurationProcessor implements ConfigurationProcessor {
    // 实现配置处理逻辑
}
```

### 扩展Nacos环境
在`application.yml`中添加新的环境配置：
```yaml
dm:
  nacos:
    environments:
      newenv:
        name: newenv
        server-addr: your-nacos-server:8848
        # 其他配置...
```

### 添加新的模板
在`dm-template-core/src/main/resources/templates/`目录下添加`.ftl`模板文件。

### 添加新的REST API
在`UpgradeCenterController`中添加新的端点：
```java
@PostMapping("/api/upgrade/your-endpoint")
public ResponseEntity<Map<String, Object>> yourEndpoint(@RequestBody YourRequest request) {
    // API实现
}
```

## 测试策略

- 各模块都有对应的单元测试
- 集成测试主要在`dm-upgrade-center`模块
- 测试配置使用独立的`application-test.yml`
- 支持Mock外部服务进行测试

## 调试和日志

### 日志级别配置
```yaml
logging:
  level:
    com.just: INFO
    com.just.file: DEBUG
    com.just.upgrade: DEBUG
```

### 常见问题排查
1. Nacos连接问题：检查服务器地址和认证信息
2. Git操作失败：检查token和仓库访问权限
3. Excel文件读取失败：确认文件路径和格式
4. 模板生成错误：检查FreeMarker模板语法

## API接口

### REST API接口 (推荐使用)
项目提供完整的REST API接口，主要端点包括：

**Nacos升级接口:**
- `POST /api/upgrade/nacos/upgrade` - 升级Nacos服务
- `GET /api/upgrade/nacos/status/{taskId}` - 获取升级状态
- `POST /api/upgrade/nacos/cancel/{taskId}` - 取消升级任务

**Nacos配置管理接口:**
- `POST /api/upgrade/nacos/config/single` - 获取单个Nacos配置
- `POST /api/upgrade/nacos/config/search` - 模糊搜索Nacos配置
- `POST /api/upgrade/nacos/config/list` - 获取所有Nacos配置

**GitLab项目管理接口:**
- `POST /api/upgrade/gitlab/projects` - 获取GitLab项目列表
- `POST /api/upgrade/gitlab/projects/search` - 搜索GitLab项目
- `GET /api/upgrade/gitlab/projects/{projectId}` - 获取项目详情

**系统接口:**
- `GET /api/upgrade/health` - 健康检查

### Spring Shell命令行接口 (传统方式)
项目还保留了Spring Shell命令行接口，主要用于：
- 交互式配置管理
- 批量操作支持
- 开发调试使用

### API调用示例

**升级Nacos服务:**
```bash
curl -X POST http://localhost:8080/upgrade-center/api/upgrade/nacos/upgrade \
  -H "Content-Type: application/json" \
  -d '{
    "environment": "dev",
    "serviceName": "your-service",
    "upgradeRegistry": true,
    "autoRefresh": true
  }'
```

**查询升级状态:**
```bash  
curl http://localhost:8080/upgrade-center/api/upgrade/nacos/status/{taskId}
```

## 部署说明

### 运行环境要求
- Java 8+
- Maven 3.6+
- 网络访问GitLab和Nacos服务器

### 应用启动
主应用运行在8080端口，context-path为`/upgrade-center`。

**访问地址:**
- REST API: `http://localhost:8080/upgrade-center/api/`
- 健康检查: `http://localhost:8080/upgrade-center/api/upgrade/health`

### 配置外部化
建议生产环境通过环境变量或外部配置文件提供敏感信息：
- `GITLAB_TOKEN`: GitLab访问令牌
- `NACOS_ACCESS_KEY`: Nacos访问密钥
- `NACOS_SECRET_KEY`: Nacos访问密钥Secret

### 异步任务处理
项目支持异步升级任务：
1. 提交升级请求后返回taskId
2. 通过taskId轮询升级状态
3. 升级过程包含详细的进度信息和日志
4. 支持任务取消功能

## 常见问题和故障排除

### 启动问题
1. **端口冲突**：默认8080端口被占用
   - 解决：修改`application.yml`中的`server.port`
   
2. **Excel文件不存在**：找不到配置Excel文件
   - 解决：确保`nacos环境变量对应文件.xlsx`、`url.xlsx`等文件在正确位置

3. **Nacos连接失败**：无法连接到Nacos服务器
   - 检查网络连接和服务器地址
   - 验证access-key和secret-key是否正确

### 运行时问题
1. **GitLab操作失败**：
   - 检查GitLab token是否有效
   - 确认项目访问权限
   - 验证GitLab服务器地址

2. **模板生成错误**：
   - 检查FreeMarker模板语法
   - 验证模板变量是否正确传递
   - 查看详细错误日志

3. **Excel读取失败**：
   - 确认Excel文件格式正确
   - 检查工作表名称是否匹配配置
   - 验证文件编码和内容格式

### 性能优化
1. **大量项目搜索慢**：考虑增加GitLab API请求缓存
2. **Excel文件解析慢**：可以启用Excel配置缓存
3. **Nacos操作超时**：调整timeout配置

### 日志分析
重要日志位置和级别：
```yaml
logging:
  level:
    com.just: INFO
    com.just.file: DEBUG
    com.just.upgrade: DEBUG
```

日志模块说明：
- `com.just.upgrade`: 升级操作相关日志
- `com.just.file`: 文件操作相关日志
- `com.just.git`: Git操作相关日志
- `com.just.nacos`: Nacos操作相关日志

### 生产环境特别注意
1. **生产配置确认**：操作生产环境(prd)时需要额外确认
2. **配置备份验证**：确保备份文件正常生成
3. **环境变量安全**：定期轮换敏感凭据
4. **操作审计**：记录所有生产环境变更